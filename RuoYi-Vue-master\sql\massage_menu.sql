-- 按摩预约系统菜单配置
-- 主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('按摩管理', 0, 5, 'massage', NULL, 1, 0, 'M', '0', '0', NULL, 'massage', 'admin', sysdate(), '', NULL, '按摩预约系统管理');

-- 获取刚插入的主菜单ID
SET @massage_menu_id = LAST_INSERT_ID();

-- 数据统计大屏
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数据大屏', @massage_menu_id, 1, 'dashboard', 'massage/dashboard/index', 1, 0, 'C', '0', '0', 'massage:dashboard:view', 'dashboard', 'admin', sysdate(), '', NULL, '数据统计大屏');

-- 用户管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户管理', @massage_menu_id, 2, 'user', 'massage/user/index', 1, 0, 'C', '0', '0', 'massage:user:list', 'user', 'admin', sysdate(), '', NULL, '用户管理');

SET @user_menu_id = LAST_INSERT_ID();

-- 用户管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户查询', @user_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:user:query', '#', 'admin', sysdate(), '', NULL, ''),
('用户新增', @user_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:user:add', '#', 'admin', sysdate(), '', NULL, ''),
('用户修改', @user_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:user:edit', '#', 'admin', sysdate(), '', NULL, ''),
('用户删除', @user_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:user:remove', '#', 'admin', sysdate(), '', NULL, ''),
('用户导出', @user_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:user:export', '#', 'admin', sysdate(), '', NULL, '');

-- 技师管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('技师管理', @massage_menu_id, 3, 'coach', 'massage/coach/index', 1, 0, 'C', '0', '0', 'massage:coach:list', 'peoples', 'admin', sysdate(), '', NULL, '技师管理');

SET @coach_menu_id = LAST_INSERT_ID();

-- 技师管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('技师查询', @coach_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:coach:query', '#', 'admin', sysdate(), '', NULL, ''),
('技师新增', @coach_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:coach:add', '#', 'admin', sysdate(), '', NULL, ''),
('技师修改', @coach_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:coach:edit', '#', 'admin', sysdate(), '', NULL, ''),
('技师删除', @coach_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:coach:remove', '#', 'admin', sysdate(), '', NULL, ''),
('技师导出', @coach_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:coach:export', '#', 'admin', sysdate(), '', NULL, ''),
('技师审核', @coach_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'massage:coach:audit', '#', 'admin', sysdate(), '', NULL, ''),
('技师编辑页面', @coach_menu_id, 7, 'coach/edit', 'massage/coach/edit', 1, 0, 'C', '1', '0', 'massage:coach:edit', '#', 'admin', sysdate(), '', NULL, '技师编辑页面');

-- 订单管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单管理', @massage_menu_id, 4, 'order', 'massage/order/index', 1, 0, 'C', '0', '0', 'massage:order:list', 'shopping', 'admin', sysdate(), '', NULL, '订单管理');

SET @order_menu_id = LAST_INSERT_ID();

-- 订单管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单查询', @order_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:order:query', '#', 'admin', sysdate(), '', NULL, ''),
('订单新增', @order_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:order:add', '#', 'admin', sysdate(), '', NULL, ''),
('订单修改', @order_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:order:edit', '#', 'admin', sysdate(), '', NULL, ''),
('订单删除', @order_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:order:remove', '#', 'admin', sysdate(), '', NULL, ''),
('订单导出', @order_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:order:export', '#', 'admin', sysdate(), '', NULL, ''),
('订单派单', @order_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'massage:order:assign', '#', 'admin', sysdate(), '', NULL, ''),
('订单退款', @order_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'massage:order:refund', '#', 'admin', sysdate(), '', NULL, ''),
('订单完成', @order_menu_id, 8, '', '', 1, 0, 'F', '0', '0', 'massage:order:complete', '#', 'admin', sysdate(), '', NULL, '');

-- 服务管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('服务管理', @massage_menu_id, 5, 'service', 'massage/service/index', 1, 0, 'C', '0', '0', 'massage:service:list', 'skill', 'admin', sysdate(), '', NULL, '服务管理');

SET @service_menu_id = LAST_INSERT_ID();

-- 服务管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('服务查询', @service_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:service:query', '#', 'admin', sysdate(), '', NULL, ''),
('服务新增', @service_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:service:add', '#', 'admin', sysdate(), '', NULL, ''),
('服务修改', @service_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:service:edit', '#', 'admin', sysdate(), '', NULL, ''),
('服务删除', @service_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:service:remove', '#', 'admin', sysdate(), '', NULL, ''),
('服务导出', @service_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:service:export', '#', 'admin', sysdate(), '', NULL, '');

-- 城市管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('城市管理', @massage_menu_id, 6, 'city', 'massage/city/index', 1, 0, 'C', '0', '0', 'massage:city:list', 'international', 'admin', sysdate(), '', NULL, '城市管理');

SET @city_menu_id = LAST_INSERT_ID();

-- 城市管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('城市查询', @city_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:city:query', '#', 'admin', sysdate(), '', NULL, ''),
('城市新增', @city_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:city:add', '#', 'admin', sysdate(), '', NULL, ''),
('城市修改', @city_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:city:edit', '#', 'admin', sysdate(), '', NULL, ''),
('城市删除', @city_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:city:remove', '#', 'admin', sysdate(), '', NULL, ''),
('城市导出', @city_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:city:export', '#', 'admin', sysdate(), '', NULL, '');

-- 代理商管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('代理商管理', @massage_menu_id, 7, 'agent', 'massage/agent/index', 1, 0, 'C', '0', '0', 'massage:agent:list', 'tree-table', 'admin', sysdate(), '', NULL, '代理商管理');

SET @agent_menu_id = LAST_INSERT_ID();

-- 代理商管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('代理商查询', @agent_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:agent:query', '#', 'admin', sysdate(), '', NULL, ''),
('代理商新增', @agent_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:agent:add', '#', 'admin', sysdate(), '', NULL, ''),
('代理商修改', @agent_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:agent:edit', '#', 'admin', sysdate(), '', NULL, ''),
('代理商删除', @agent_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:agent:remove', '#', 'admin', sysdate(), '', NULL, ''),
('代理商导出', @agent_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:agent:export', '#', 'admin', sysdate(), '', NULL, ''),
('代理商审核', @agent_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'massage:agent:audit', '#', 'admin', sysdate(), '', NULL, '');

-- 财务管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('财务管理', @massage_menu_id, 8, 'finance', 'massage/finance/index', 1, 0, 'C', '0', '0', 'massage:finance:list', 'money', 'admin', sysdate(), '', NULL, '财务管理');

SET @finance_menu_id = LAST_INSERT_ID();

-- 财务管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('财务查询', @finance_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:finance:query', '#', 'admin', sysdate(), '', NULL, ''),
('财务导出', @finance_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:finance:export', '#', 'admin', sysdate(), '', NULL, ''),
('提现审核', @finance_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:finance:withdraw', '#', 'admin', sysdate(), '', NULL, ''),
('余额调整', @finance_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:finance:adjust', '#', 'admin', sysdate(), '', NULL, '');

-- 系统配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('系统配置', @massage_menu_id, 9, 'config', 'massage/config/index', 1, 0, 'C', '0', '0', 'massage:config:list', 'system', 'admin', sysdate(), '', NULL, '系统配置');

SET @config_menu_id = LAST_INSERT_ID();

-- 系统配置子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('配置查询', @config_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'massage:config:query', '#', 'admin', sysdate(), '', NULL, ''),
('配置修改', @config_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'massage:config:edit', '#', 'admin', sysdate(), '', NULL, ''),
('微信配置', @config_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'massage:config:wechat', '#', 'admin', sysdate(), '', NULL, ''),
('支付配置', @config_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'massage:config:payment', '#', 'admin', sysdate(), '', NULL, ''),
('地图配置', @config_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'massage:config:map', '#', 'admin', sysdate(), '', NULL, '');

-- 数据统计
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数据统计', @massage_menu_id, 10, 'statistics', NULL, 1, 0, 'M', '0', '0', NULL, 'chart', 'admin', sysdate(), '', NULL, '数据统计');

SET @statistics_menu_id = LAST_INSERT_ID();

-- 数据统计子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('营收统计', @statistics_menu_id, 1, 'revenue', 'massage/statistics/revenue', 1, 0, 'C', '0', '0', 'massage:statistics:revenue', 'money', 'admin', sysdate(), '', NULL, '营收统计'),
('用户统计', @statistics_menu_id, 2, 'user-stats', 'massage/statistics/user', 1, 0, 'C', '0', '0', 'massage:statistics:user', 'user', 'admin', sysdate(), '', NULL, '用户统计'),
('技师统计', @statistics_menu_id, 3, 'coach-stats', 'massage/statistics/coach', 1, 0, 'C', '0', '0', 'massage:statistics:coach', 'peoples', 'admin', sysdate(), '', NULL, '技师统计'),
('订单统计', @statistics_menu_id, 4, 'order-stats', 'massage/statistics/order', 1, 0, 'C', '0', '0', 'massage:statistics:order', 'shopping', 'admin', sysdate(), '', NULL, '订单统计');
