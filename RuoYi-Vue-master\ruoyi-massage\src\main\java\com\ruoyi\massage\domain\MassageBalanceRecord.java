package com.ruoyi.massage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户余额记录对象 ims_massage_service_balance_water
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageBalanceRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 关联订单ID */
    @Excel(name = "关联订单ID")
    private Long orderId;

    /** 变动类型(1充值 2消费 3退款 4升级消费 5后台减扣) */
    @Excel(name = "变动类型", readConverterExp = "1=充值,2=消费,3=退款,4=升级消费,5=后台减扣")
    private Integer type;

    /** 是否增加(1增加 0减少) */
    @Excel(name = "是否增加", readConverterExp = "1=增加,0=减少")
    private Integer add;

    /** 变动金额 */
    @Excel(name = "变动金额")
    private BigDecimal price;

    /** 变动前余额 */
    @Excel(name = "变动前余额")
    private BigDecimal beforeBalance;

    /** 变动后余额 */
    @Excel(name = "变动后余额")
    private BigDecimal afterBalance;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setAdd(Integer add) 
    {
        this.add = add;
    }

    public Integer getAdd() 
    {
        return add;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setBeforeBalance(BigDecimal beforeBalance) 
    {
        this.beforeBalance = beforeBalance;
    }

    public BigDecimal getBeforeBalance() 
    {
        return beforeBalance;
    }
    public void setAfterBalance(BigDecimal afterBalance) 
    {
        this.afterBalance = afterBalance;
    }

    public BigDecimal getAfterBalance()
    {
        return afterBalance;
    }
    public void setCreateTimestamp(Long createTimestamp)
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp()
    {
        return createTimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("userId", getUserId())
            .append("orderId", getOrderId())
            .append("type", getType())
            .append("add", getAdd())
            .append("price", getPrice())
            .append("beforeBalance", getBeforeBalance())
            .append("afterBalance", getAfterBalance())
            .append("createTimestamp", getCreateTimestamp())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
