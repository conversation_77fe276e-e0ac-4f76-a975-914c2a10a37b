package com.ruoyi.massage.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.massage.mapper.MassageUserMapper;
import com.ruoyi.massage.mapper.MassageGrowthRecordMapper;
import com.ruoyi.massage.mapper.MassageBalanceRecordMapper;
import com.ruoyi.massage.domain.MassageUser;
import com.ruoyi.massage.domain.MassageGrowthRecord;
import com.ruoyi.massage.domain.MassageBalanceRecord;
import com.ruoyi.massage.service.IMassageUserService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 按摩用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class MassageUserServiceImpl implements IMassageUserService 
{
    @Autowired
    private MassageUserMapper massageUserMapper;

    @Autowired
    private MassageGrowthRecordMapper massageGrowthRecordMapper;

    @Autowired
    private MassageBalanceRecordMapper massageBalanceRecordMapper;

    /**
     * 查询按摩用户
     * 
     * @param id 按摩用户主键
     * @return 按摩用户
     */
    @Override
    public MassageUser selectMassageUserById(Long id)
    {
        return massageUserMapper.selectMassageUserById(id);
    }

    /**
     * 查询按摩用户列表
     * 
     * @param massageUser 按摩用户
     * @return 按摩用户
     */
    @Override
    public List<MassageUser> selectMassageUserList(MassageUser massageUser)
    {
        return massageUserMapper.selectMassageUserList(massageUser);
    }

    /**
     * 新增按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMassageUser(MassageUser massageUser)
    {
        massageUser.setCreateTime(System.currentTimeMillis() / 1000);
        return massageUserMapper.insertMassageUser(massageUser);
    }

    /**
     * 修改按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMassageUser(MassageUser massageUser)
    {
        return massageUserMapper.updateMassageUser(massageUser);
    }

    /**
     * 批量删除按摩用户
     * 
     * @param ids 需要删除的按摩用户主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMassageUserByIds(Long[] ids)
    {
        return massageUserMapper.deleteMassageUserByIds(ids);
    }

    /**
     * 删除按摩用户信息
     * 
     * @param id 按摩用户主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMassageUserById(Long id)
    {
        return massageUserMapper.deleteMassageUserById(id);
    }

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户信息
     */
    @Override
    public MassageUser selectMassageUserByOpenid(String openid)
    {
        return massageUserMapper.selectMassageUserByOpenid(openid);
    }

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Override
    public MassageUser selectMassageUserByPhone(String phone)
    {
        return massageUserMapper.selectMassageUserByPhone(phone);
    }

    /**
     * 用户余额调整
     * 
     * @param userId 用户ID
     * @param amount 调整金额（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    @Override
    @Transactional
    public int adjustUserBalance(Long userId, Double amount, String remark)
    {
        MassageUser user = massageUserMapper.selectMassageUserById(userId);
        if (user == null) {
            return 0;
        }
        
        Double currentBalance = user.getBalance() != null ? user.getBalance() : 0.0;
        Double newBalance = currentBalance + amount;
        if (newBalance < 0) {
            throw new RuntimeException("余额不足，无法扣减");
        }

        // 创建余额记录
        MassageBalanceRecord balanceRecord = new MassageBalanceRecord();
        balanceRecord.setUniacid(user.getUniacid());
        balanceRecord.setUserId(userId);
        balanceRecord.setOrderId(0L);
        balanceRecord.setType(5); // 5表示后台减扣/增加
        balanceRecord.setAdd(amount > 0 ? 1 : 0);
        balanceRecord.setPrice(BigDecimal.valueOf(Math.abs(amount)));
        balanceRecord.setBeforeBalance(BigDecimal.valueOf(currentBalance));
        balanceRecord.setAfterBalance(BigDecimal.valueOf(newBalance));
        balanceRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

        // 插入余额记录
        massageBalanceRecordMapper.insertMassageBalanceRecord(balanceRecord);

        user.setBalance(newBalance);

        return massageUserMapper.updateMassageUser(user);
    }

    /**
     * 用户成长值调整
     * 
     * @param userId 用户ID
     * @param growth 调整成长值（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    @Override
    @Transactional
    public int adjustUserGrowth(Long userId, Long growth, String remark)
    {
        MassageUser user = massageUserMapper.selectMassageUserById(userId);
        if (user == null) {
            return 0;
        }
        
        Long newGrowth = (user.getGrowth() != null ? user.getGrowth() : 0L) + growth;
        if (newGrowth < 0) {
            newGrowth = 0L;
        }
        
        user.setGrowth(newGrowth.intValue());
        
        return massageUserMapper.updateMassageUser(user);
    }

    /**
     * 获取用户统计数据
     * 
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getUserStatistics()
    {
        return massageUserMapper.getUserStatistics();
    }

    /**
     * 禁用/启用用户
     * 
     * @param userId 用户ID
     * @param status 状态（1正常 2禁用）
     * @return 结果
     */
    @Override
    @Transactional
    public int changeUserStatus(Long userId, Integer status)
    {
        MassageUser user = new MassageUser();
        user.setId(userId);
        user.setStatus(status);
        return massageUserMapper.updateMassageUser(user);
    }

    /**
     * 获取用户消费统计
     *
     * @param userId 用户ID
     * @return 消费统计
     */
    @Override
    public Map<String, Object> getUserConsumptionStats(Long userId)
    {
        return massageUserMapper.getUserConsumptionStats(userId);
    }

    /**
     * 用户余额调整（BigDecimal版本）
     *
     * @param userId 用户ID
     * @param amount 调整金额（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    @Override
    @Transactional
    public int adjustBalance(Long userId, BigDecimal amount, String remark)
    {
        // 获取当前用户信息
        MassageUser user = massageUserMapper.selectMassageUserById(userId);
        if (user == null) {
            return 0;
        }

        // 计算新余额 - 将Double转换为BigDecimal进行计算
        BigDecimal currentBalance = user.getBalance() != null ?
            BigDecimal.valueOf(user.getBalance()) : BigDecimal.ZERO;
        BigDecimal newBalance = currentBalance.add(amount);

        // 余额不能为负数
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("余额不足，无法进行此操作");
        }

        // 创建余额记录
        MassageBalanceRecord balanceRecord = new MassageBalanceRecord();
        balanceRecord.setUniacid(user.getUniacid());
        balanceRecord.setUserId(userId);
        balanceRecord.setOrderId(0L);
        balanceRecord.setType(5); // 5表示管理员调整
        balanceRecord.setAdd(amount.compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);
        balanceRecord.setPrice(amount.abs());
        balanceRecord.setBeforeBalance(currentBalance);
        balanceRecord.setAfterBalance(newBalance);
        balanceRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

        // 插入余额记录
        massageBalanceRecordMapper.insertMassageBalanceRecord(balanceRecord);

        // 更新用户余额 - 将BigDecimal转换回Double
        user.setBalance(newBalance.doubleValue());

        return massageUserMapper.updateMassageUser(user);
    }

    /**
     * 用户成长值调整（Integer版本）
     *
     * @param userId 用户ID
     * @param growth 调整成长值（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    @Override
    @Transactional
    public int adjustGrowth(Long userId, Integer growth, String remark)
    {
        // 获取当前用户信息
        MassageUser user = massageUserMapper.selectMassageUserById(userId);
        if (user == null) {
            return 0;
        }

        // 计算新成长值
        Integer currentGrowth = user.getGrowth() != null ? user.getGrowth() : 0;
        Integer newGrowth = currentGrowth + growth;

        // 成长值不能为负数
        if (newGrowth < 0) {
            throw new RuntimeException("成长值不足，无法进行此操作");
        }

        // 创建成长值记录
        MassageGrowthRecord growthRecord = new MassageGrowthRecord();
        growthRecord.setUniacid(user.getUniacid());
        growthRecord.setUserId(userId);
        growthRecord.setGrowth(Math.abs(growth));
        growthRecord.setIsAdd(growth > 0 ? 1 : 0);
        growthRecord.setBeforeGrowth(currentGrowth);
        growthRecord.setAfterGrowth(newGrowth);
        growthRecord.setType(2); // 2表示管理员调整
        growthRecord.setOrderId(0L);
        growthRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

        // 插入成长值记录
        massageGrowthRecordMapper.insertMassageGrowthRecord(growthRecord);

        // 更新用户成长值
        user.setGrowth(newGrowth);

        return massageUserMapper.updateMassageUser(user);
    }

    /**
     * 获取用户消费记录
     *
     * @param userId 用户ID
     * @return 消费记录列表
     */
    @Override
    public List<Map<String, Object>> getUserOrderList(Long userId)
    {
        return massageUserMapper.selectUserOrderList(userId);
    }

    /**
     * 获取用户充值记录
     *
     * @param userId 用户ID
     * @return 充值记录列表
     */
    @Override
    public List<Map<String, Object>> getUserRechargeList(Long userId)
    {
        return massageUserMapper.selectUserRechargeList(userId);
    }

    /**
     * 获取用户成长值记录
     *
     * @param userId 用户ID
     * @return 成长值记录列表
     */
    @Override
    public List<Map<String, Object>> getUserGrowthList(Long userId)
    {
        return massageUserMapper.selectUserGrowthList(userId);
    }

    /**
     * 获取用户余额记录
     *
     * @param userId 用户ID
     * @return 余额记录列表
     */
    @Override
    public List<Map<String, Object>> getUserBalanceList(Long userId)
    {
        return massageUserMapper.selectUserBalanceList(userId);
    }

    /**
     * 获取用户优惠券列表
     *
     * @param userId 用户ID
     * @return 优惠券列表
     */
    @Override
    public List<Map<String, Object>> getUserCouponList(Long userId)
    {
        return massageUserMapper.selectUserCouponList(userId);
    }

    /**
     * 获取用户折扣卡列表
     *
     * @param userId 用户ID
     * @return 折扣卡列表
     */
    @Override
    public List<Map<String, Object>> getUserDiscountList(Long userId)
    {
        return massageUserMapper.selectUserDiscountList(userId);
    }

    /**
     * 获取用户屏蔽列表
     *
     * @param userId 用户ID
     * @return 屏蔽列表
     */
    @Override
    public List<Map<String, Object>> getUserBlockedList(Long userId)
    {
        return massageUserMapper.selectUserBlockedList(userId);
    }

    /**
     * 添加屏蔽用户
     *
     * @param userId 用户ID
     * @param blockedUserId 被屏蔽用户ID
     * @return 结果
     */
    @Override
    public int addBlockedUser(Long userId, Long blockedUserId)
    {
        return massageUserMapper.insertBlockedUser(userId, blockedUserId);
    }

    /**
     * 解除屏蔽用户
     *
     * @param ids 屏蔽记录ID列表
     * @return 结果
     */
    @Override
    public int removeBlockedUser(List<Long> ids)
    {
        return massageUserMapper.deleteBlockedUser(ids);
    }
}
