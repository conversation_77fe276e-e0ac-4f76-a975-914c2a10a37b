package com.ruoyi.massage.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.ruoyi.massage.domain.MassageUser;

/**
 * 按摩用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface IMassageUserService 
{
    /**
     * 查询按摩用户
     * 
     * @param id 按摩用户主键
     * @return 按摩用户
     */
    public MassageUser selectMassageUserById(Long id);

    /**
     * 查询按摩用户列表
     * 
     * @param massageUser 按摩用户
     * @return 按摩用户集合
     */
    public List<MassageUser> selectMassageUserList(MassageUser massageUser);

    /**
     * 新增按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    public int insertMassageUser(MassageUser massageUser);

    /**
     * 修改按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    public int updateMassageUser(MassageUser massageUser);

    /**
     * 批量删除按摩用户
     * 
     * @param ids 需要删除的按摩用户主键集合
     * @return 结果
     */
    public int deleteMassageUserByIds(Long[] ids);

    /**
     * 删除按摩用户信息
     * 
     * @param id 按摩用户主键
     * @return 结果
     */
    public int deleteMassageUserById(Long id);

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户信息
     */
    public MassageUser selectMassageUserByOpenid(String openid);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    public MassageUser selectMassageUserByPhone(String phone);

    /**
     * 用户余额调整
     *
     * @param userId 用户ID
     * @param amount 调整金额（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    public int adjustUserBalance(Long userId, Double amount, String remark);

    /**
     * 用户余额调整（BigDecimal版本）
     *
     * @param userId 用户ID
     * @param amount 调整金额（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    public int adjustBalance(Long userId, BigDecimal amount, String remark);

    /**
     * 用户成长值调整
     *
     * @param userId 用户ID
     * @param growth 调整成长值（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    public int adjustUserGrowth(Long userId, Long growth, String remark);

    /**
     * 用户成长值调整（Integer版本）
     *
     * @param userId 用户ID
     * @param growth 调整成长值（正数为增加，负数为减少）
     * @param remark 调整备注
     * @return 结果
     */
    public int adjustGrowth(Long userId, Integer growth, String remark);

    /**
     * 获取用户消费记录
     *
     * @param params 查询参数
     * @return 消费记录列表
     */
    public List<Map<String, Object>> getUserOrderList(Map<String, Object> params);

    /**
     * 获取用户充值记录
     *
     * @param userId 用户ID
     * @return 充值记录列表
     */
    public List<Map<String, Object>> getUserRechargeList(Long userId);

    /**
     * 获取用户成长值记录
     *
     * @param params 查询参数
     * @return 成长值记录列表
     */
    public List<Map<String, Object>> getUserGrowthList(Map<String, Object> params);

    /**
     * 获取用户余额记录
     *
     * @param params 查询参数
     * @return 余额记录列表
     */
    public List<Map<String, Object>> getUserBalanceList(Map<String, Object> params);

    /**
     * 获取用户优惠券列表
     *
     * @param userId 用户ID
     * @return 优惠券列表
     */
    public List<Map<String, Object>> getUserCouponList(Long userId);

    /**
     * 获取用户折扣卡列表
     *
     * @param userId 用户ID
     * @return 折扣卡列表
     */
    public List<Map<String, Object>> getUserDiscountList(Long userId);

    /**
     * 获取用户屏蔽列表
     *
     * @param userId 用户ID
     * @return 屏蔽列表
     */
    public List<Map<String, Object>> getUserBlockedList(Long userId);

    /**
     * 添加屏蔽用户
     *
     * @param userId 用户ID
     * @param blockedUserId 被屏蔽用户ID
     * @return 结果
     */
    public int addBlockedUser(Long userId, Long blockedUserId);

    /**
     * 解除屏蔽用户
     *
     * @param ids 屏蔽记录ID列表
     * @return 结果
     */
    public int removeBlockedUser(List<Long> ids);



    /**
     * 获取用户统计数据
     * 
     * @return 统计数据
     */
    public Map<String, Object> getUserStatistics();

    /**
     * 禁用/启用用户
     * 
     * @param userId 用户ID
     * @param status 状态（1正常 2禁用）
     * @return 结果
     */
    public int changeUserStatus(Long userId, Integer status);

    /**
     * 获取用户消费统计
     * 
     * @param userId 用户ID
     * @return 消费统计
     */
    public Map<String, Object> getUserConsumptionStats(Long userId);
}
