{"name": "guzzle/http", "description": "HTTP libraries used by Guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["http client", "http", "client", "Guzzle", "curl"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/common": "self.version", "guzzle/parser": "self.version", "guzzle/stream": "self.version"}, "suggest": {"ext-curl": "*"}, "autoload": {"psr-0": {"Guzzle\\Http": ""}}, "target-dir": "Guzzle/Http", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}