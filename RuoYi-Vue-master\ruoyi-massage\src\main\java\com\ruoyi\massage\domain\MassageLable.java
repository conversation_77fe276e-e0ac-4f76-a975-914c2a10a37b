package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 评价标签对象 ims_massage_service_lable
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MassageLable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标签ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String title;

    /** 状态(1启用 0禁用 -1删除) */
    @Excel(name = "状态", readConverterExp = "1=启用,0=禁用,-1=删除")
    private Integer status;

    /** 排序权重 */
    @Excel(name = "排序权重")
    private Integer top;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setTop(Integer top) 
    {
        this.top = top;
    }

    public Integer getTop() 
    {
        return top;
    }
    public void setCreateTimestamp(Long createTimestamp) 
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp() 
    {
        return createTimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("title", getTitle())
            .append("status", getStatus())
            .append("top", getTop())
            .append("createTimestamp", getCreateTimestamp())
            .toString();
    }
}
