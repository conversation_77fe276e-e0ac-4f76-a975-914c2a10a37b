<template>
  <div class="consumption-record-tab">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="消费类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择消费类型" clearable>
          <el-option label="按摩服务" value="1" />
          <el-option label="商品购买" value="2" />
          <el-option label="会员充值" value="3" />
          <el-option label="其他消费" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="消费时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ totalAmount }}</div>
            <div class="stat-label">总消费金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalCount }}</div>
            <div class="stat-label">消费次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ avgAmount }}</div>
            <div class="stat-label">平均消费</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ lastConsumptionDate }}</div>
            <div class="stat-label">最近消费</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 消费记录表格 -->
    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="order_code" width="180" />
      <el-table-column label="消费金额" align="center" prop="pay_price" width="120">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.pay_price || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="pay_type" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.pay_model === 2" type="success">余额支付</el-tag>
          <el-tag v-else-if="scope.row.pay_type === 1" type="primary">微信支付</el-tag>
          <el-tag v-else-if="scope.row.pay_type === 2" type="warning">支付宝</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="消费时间" align="center" prop="create_time" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getUserOrderList } from "@/api/massage/user";

export default {
  name: "ConsumptionRecordTab",
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消费记录表格数据
      recordList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: this.userId,
        type: null
      },
      // 统计数据
      totalAmount: 0,
      totalCount: 0,
      avgAmount: 0,
      lastConsumptionDate: '-'
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询消费记录列表 */
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        userId: this.userId
      };
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }

      getUserOrderList(params).then(response => {
        this.recordList = response.data.list || [];
        this.total = response.data.total || 0;
        this.loading = false;
        this.getStatistics();
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      if (this.recordList.length > 0) {
        this.totalAmount = this.recordList.reduce((sum, item) => sum + (parseFloat(item.pay_price) || 0), 0);
        this.totalCount = this.recordList.length;
        this.avgAmount = this.totalCount > 0 ? (this.totalAmount / this.totalCount) : 0;

        // 获取最近消费时间
        const latestOrder = this.recordList.reduce((latest, current) => {
          return (current.create_time > latest.create_time) ? current : latest;
        });
        this.lastConsumptionDate = this.parseTime(latestOrder.create_time * 1000, '{y}-{m}-{d}');
      } else {
        this.totalAmount = 0;
        this.totalCount = 0;
        this.avgAmount = 0;
        this.lastConsumptionDate = '-';
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 提供给父组件调用的刷新方法
    refresh() {
      this.getList();
      this.getStatistics();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看详情 */
    handleView(row) {
      this.$message.info('查看订单详情功能开发中...');
    }
  }
};
</script>

<style scoped>
.consumption-record-tab {
  padding: 20px 0;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}
</style>
