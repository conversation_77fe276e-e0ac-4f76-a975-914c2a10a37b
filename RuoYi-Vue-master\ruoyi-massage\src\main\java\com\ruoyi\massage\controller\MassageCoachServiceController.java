package com.ruoyi.massage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.massage.domain.MassageCoachService;
import com.ruoyi.massage.service.IMassageCoachServiceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 达人服务关联Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/massage/coachService")
public class MassageCoachServiceController extends BaseController
{
    @Autowired
    private IMassageCoachServiceService massageCoachServiceService;

    /**
     * 查询达人已关联服务列表
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:query')")
    @GetMapping("/list")
    public TableDataInfo list(MassageCoachService massageCoachService)
    {
        startPage();
        List<MassageCoachService> list = massageCoachServiceService.selectMassageCoachServiceList(massageCoachService);
        return getDataTable(list);
    }

    /**
     * 查询达人可关联服务列表
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:query')")
    @GetMapping("/available")
    public TableDataInfo available(MassageCoachService massageCoachService)
    {
        startPage();
        List<MassageCoachService> list = massageCoachServiceService.selectAvailableServiceList(massageCoachService);
        return getDataTable(list);
    }

    /**
     * 获取达人服务关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(massageCoachServiceService.selectMassageCoachServiceById(id));
    }

    /**
     * 新增达人服务关联
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:add')")
    @Log(title = "达人服务关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MassageCoachService massageCoachService)
    {
        return toAjax(massageCoachServiceService.insertMassageCoachService(massageCoachService));
    }

    /**
     * 修改达人服务关联
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:edit')")
    @Log(title = "达人服务关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MassageCoachService massageCoachService)
    {
        return toAjax(massageCoachServiceService.updateMassageCoachService(massageCoachService));
    }

    /**
     * 删除达人服务关联
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:remove')")
    @Log(title = "达人服务关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{coachId}/{serviceId}")
    public AjaxResult remove(@PathVariable Long coachId, @PathVariable Long serviceId)
    {
        return toAjax(massageCoachServiceService.deleteMassageCoachService(coachId, serviceId));
    }
}
