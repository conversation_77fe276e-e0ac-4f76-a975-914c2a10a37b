package com.ruoyi.massage.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.ruoyi.massage.domain.MassageBalanceRecord;

/**
 * 用户余额记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface IMassageBalanceRecordService 
{
    /**
     * 查询用户余额记录
     * 
     * @param id 用户余额记录主键
     * @return 用户余额记录
     */
    public MassageBalanceRecord selectMassageBalanceRecordById(Long id);

    /**
     * 查询用户余额记录列表
     * 
     * @param massageBalanceRecord 用户余额记录
     * @return 用户余额记录集合
     */
    public List<MassageBalanceRecord> selectMassageBalanceRecordList(MassageBalanceRecord massageBalanceRecord);

    /**
     * 新增用户余额记录
     * 
     * @param massageBalanceRecord 用户余额记录
     * @return 结果
     */
    public int insertMassageBalanceRecord(MassageBalanceRecord massageBalanceRecord);

    /**
     * 修改用户余额记录
     * 
     * @param massageBalanceRecord 用户余额记录
     * @return 结果
     */
    public int updateMassageBalanceRecord(MassageBalanceRecord massageBalanceRecord);

    /**
     * 批量删除用户余额记录
     * 
     * @param ids 需要删除的用户余额记录主键集合
     * @return 结果
     */
    public int deleteMassageBalanceRecordByIds(Long[] ids);

    /**
     * 删除用户余额记录信息
     * 
     * @param id 用户余额记录主键
     * @return 结果
     */
    public int deleteMassageBalanceRecordById(Long id);

    /**
     * 获取用户余额记录列表（用于明细显示）
     * 
     * @param userId 用户ID
     * @return 余额记录列表
     */
    public List<Map<String, Object>> selectUserBalanceRecordList(Long userId);

    /**
     * 创建余额记录
     * 
     * @param userId 用户ID
     * @param uniacid 应用ID
     * @param orderId 订单ID
     * @param type 记录类型(1充值 2消费 3退款 4升级消费 5管理员调整)
     * @param isAdd 是否增加(1增加 0减少)
     * @param amount 变动金额
     * @param beforeBalance 变动前余额
     * @param afterBalance 变动后余额
     * @return 结果
     */
    public int createBalanceRecord(Long userId, Integer uniacid, Long orderId, Integer type, 
                                 Integer isAdd, BigDecimal amount, BigDecimal beforeBalance, 
                                 BigDecimal afterBalance);
}
