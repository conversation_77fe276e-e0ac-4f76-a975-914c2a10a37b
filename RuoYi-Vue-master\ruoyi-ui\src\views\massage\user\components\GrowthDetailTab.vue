<template>
  <div class="growth-detail-tab">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="变动类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择变动类型" clearable>
          <el-option label="消费获得" value="1" />
          <el-option label="充值获得" value="2" />
          <el-option label="活动奖励" value="3" />
          <el-option label="管理员调整" value="4" />
          <el-option label="兑换消耗" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="变动时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ currentGrowth }}</div>
            <div class="stat-label">当前成长值</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">+{{ totalEarned }}</div>
            <div class="stat-label">累计获得</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">-{{ totalUsed }}</div>
            <div class="stat-label">累计消耗</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ lastChangeDate }}</div>
            <div class="stat-label">最近变动</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 成长值明细表格 -->
    <el-table v-loading="loading" :data="recordList">
      <el-table-column label="操作者" align="center" prop="create_user" width="120" />
      <el-table-column label="操作记录" align="center" min-width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.type_text }}</div>
          <span :class="[{ 'c-link': scope.row.is_add }, { 'c-warning': !scope.row.is_add }]">
            {{ `${scope.row.is_add ? '+' : '-'} ${scope.row.growth}` }}
          </span>
          ，现成长值<span class="ml-sm c-success">{{ scope.row.after_growth }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center" prop="create_time" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getUserGrowthList } from "@/api/massage/user";

export default {
  name: "GrowthDetailTab",
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      recordList: [],
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: this.userId,
        type: null
      },
      currentGrowth: 0,
      totalEarned: 0,
      totalUsed: 0,
      lastChangeDate: '-'
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        id: this.userId
      };
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }

      getUserGrowthList(params).then(response => {
        this.recordList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
        this.getStatistics();
      }).catch(() => {
        this.loading = false;
      });
    },
    getStatistics() {
      if (this.recordList.length > 0) {
        // 计算当前成长值（最新记录的after_growth）
        const latestRecord = this.recordList.reduce((latest, current) => {
          return (current.create_time > latest.create_time) ? current : latest;
        });
        this.currentGrowth = latestRecord.after_growth || 0;

        // 计算累计获得（正数变动的总和）
        this.totalEarned = this.recordList
          .filter(item => item.growth > 0)
          .reduce((sum, item) => sum + item.growth, 0);

        // 计算累计消耗（负数变动的总和）
        this.totalUsed = Math.abs(this.recordList
          .filter(item => item.growth < 0)
          .reduce((sum, item) => sum + item.growth, 0));

        // 获取最近变动时间
        this.lastChangeDate = this.parseTime(latestRecord.create_time * 1000, '{y}-{m}-{d}');
      } else {
        this.currentGrowth = 0;
        this.totalEarned = 0;
        this.totalUsed = 0;
        this.lastChangeDate = '-';
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 提供给父组件调用的刷新方法
    refresh() {
      this.getList();
    }
  }
};
</script>

<style scoped>
.growth-detail-tab {
  padding: 20px 0;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}
</style>
