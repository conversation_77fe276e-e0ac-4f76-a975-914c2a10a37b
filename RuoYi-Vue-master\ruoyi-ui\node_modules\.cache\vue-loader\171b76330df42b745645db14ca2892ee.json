{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\ConsumptionRecordTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\ConsumptionRecordTab.vue", "mtime": 1753760963234}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ConsumptionRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ConsumptionRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"consumption-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"消费类型\" prop=\"type\">\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择消费类型\" clearable>\n          <el-option label=\"按摩服务\" value=\"1\" />\n          <el-option label=\"商品购买\" value=\"2\" />\n          <el-option label=\"会员充值\" value=\"3\" />\n          <el-option label=\"其他消费\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"消费时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalAmount }}</div>\n            <div class=\"stat-label\">总消费金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ totalCount }}</div>\n            <div class=\"stat-label\">消费次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgAmount }}</div>\n            <div class=\"stat-label\">平均消费</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastConsumptionDate }}</div>\n            <div class=\"stat-label\">最近消费</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 消费记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单号\" align=\"center\" prop=\"order_code\" width=\"180\" />\n      <el-table-column label=\"消费金额\" align=\"center\" prop=\"pay_price\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #f56c6c; font-weight: bold;\">¥{{ scope.row.pay_price || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"支付方式\" align=\"center\" prop=\"pay_type\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.pay_model === 2\" type=\"success\">余额支付</el-tag>\n          <el-tag v-else-if=\"scope.row.pay_type === 1\" type=\"primary\">微信支付</el-tag>\n          <el-tag v-else-if=\"scope.row.pay_type === 2\" type=\"warning\">支付宝</el-tag>\n          <el-tag v-else type=\"info\">其他</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"消费时间\" align=\"center\" prop=\"create_time\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserOrderList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"ConsumptionRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 消费记录表格数据\n      recordList: [],\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        type: null\n      },\n      // 统计数据\n      totalAmount: 0,\n      totalCount: 0,\n      avgAmount: 0,\n      lastConsumptionDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    /** 查询消费记录列表 */\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        userId: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserOrderList(params).then(response => {\n        this.recordList = response.data.list || [];\n        this.total = response.data.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    /** 获取统计数据 */\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        this.totalAmount = this.recordList.reduce((sum, item) => sum + (parseFloat(item.pay_price) || 0), 0);\n        this.totalCount = this.recordList.length;\n        this.avgAmount = this.totalCount > 0 ? (this.totalAmount / this.totalCount) : 0;\n\n        // 获取最近消费时间\n        const latestOrder = this.recordList.reduce((latest, current) => {\n          return (current.create_time > latest.create_time) ? current : latest;\n        });\n        this.lastConsumptionDate = this.parseTime(latestOrder.create_time * 1000, '{y}-{m}-{d}');\n      } else {\n        this.totalAmount = 0;\n        this.totalCount = 0;\n        this.avgAmount = 0;\n        this.lastConsumptionDate = '-';\n      }\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n      this.getStatistics();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 查看详情 */\n    handleView(row) {\n      this.$message.info('查看订单详情功能开发中...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.consumption-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}