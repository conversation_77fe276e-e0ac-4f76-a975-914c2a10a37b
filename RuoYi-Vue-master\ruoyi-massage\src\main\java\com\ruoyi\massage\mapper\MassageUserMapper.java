package com.ruoyi.massage.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.massage.domain.MassageUser;
import org.apache.ibatis.annotations.Param;

/**
 * 按摩用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface MassageUserMapper 
{
    /**
     * 查询按摩用户
     * 
     * @param id 按摩用户主键
     * @return 按摩用户
     */
    public MassageUser selectMassageUserById(Long id);

    /**
     * 查询按摩用户列表
     * 
     * @param massageUser 按摩用户
     * @return 按摩用户集合
     */
    public List<MassageUser> selectMassageUserList(MassageUser massageUser);

    /**
     * 新增按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    public int insertMassageUser(MassageUser massageUser);

    /**
     * 修改按摩用户
     * 
     * @param massageUser 按摩用户
     * @return 结果
     */
    public int updateMassageUser(MassageUser massageUser);

    /**
     * 删除按摩用户
     * 
     * @param id 按摩用户主键
     * @return 结果
     */
    public int deleteMassageUserById(Long id);

    /**
     * 批量删除按摩用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMassageUserByIds(Long[] ids);

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户信息
     */
    public MassageUser selectMassageUserByOpenid(@Param("openid") String openid);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    public MassageUser selectMassageUserByPhone(@Param("phone") String phone);

    /**
     * 获取用户统计数据
     * 
     * @return 统计数据
     */
    public Map<String, Object> getUserStatistics();

    /**
     * 获取今日新增用户数
     * 
     * @return 今日新增用户数
     */
    public Long getTodayNewUsers();

    /**
     * 获取用户增长趋势
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 增长趋势数据
     */
    public List<Map<String, Object>> getUserGrowthTrend(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取活跃用户数
     * 
     * @param days 天数
     * @return 活跃用户数
     */
    public Long getActiveUsers(@Param("days") Integer days);

    /**
     * 获取用户城市分布
     * 
     * @return 城市分布数据
     */
    public List<Map<String, Object>> getUserCityDistribution();

    /**
     * 获取用户性别分布
     * 
     * @return 性别分布数据
     */
    public List<Map<String, Object>> getUserGenderDistribution();

    /**
     * 获取用户年龄分布
     * 
     * @return 年龄分布数据
     */
    public List<Map<String, Object>> getUserAgeDistribution();

    /**
     * 获取用户消费统计
     * 
     * @param userId 用户ID
     * @return 消费统计
     */
    public Map<String, Object> getUserConsumptionStats(@Param("userId") Long userId);

    /**
     * 批量更新用户状态
     *
     * @param ids 用户ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateUserStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 获取用户消费记录
     *
     * @param params 查询参数
     * @return 消费记录列表
     */
    public List<Map<String, Object>> selectUserOrderList(Map<String, Object> params);

    /**
     * 获取用户充值记录
     *
     * @param userId 用户ID
     * @return 充值记录列表
     */
    public List<Map<String, Object>> selectUserRechargeList(@Param("userId") Long userId);

    /**
     * 获取用户成长值记录
     *
     * @param params 查询参数
     * @return 成长值记录列表
     */
    public List<Map<String, Object>> selectUserGrowthList(Map<String, Object> params);

    /**
     * 获取用户评价记录
     *
     * @param userId 用户ID
     * @return 评价记录列表
     */
    public List<Map<String, Object>> selectUserCommentList(@Param("userId") Long userId);

    /**
     * 获取用户余额记录
     *
     * @param params 查询参数
     * @return 余额记录列表
     */
    public List<Map<String, Object>> selectUserBalanceList(Map<String, Object> params);

    /**
     * 获取用户优惠券列表
     *
     * @param userId 用户ID
     * @return 优惠券列表
     */
    public List<Map<String, Object>> selectUserCouponList(@Param("userId") Long userId);

    /**
     * 获取用户折扣卡列表
     *
     * @param userId 用户ID
     * @return 折扣卡列表
     */
    public List<Map<String, Object>> selectUserDiscountList(@Param("userId") Long userId);

    /**
     * 获取用户屏蔽列表
     *
     * @param userId 用户ID
     * @return 屏蔽列表
     */
    public List<Map<String, Object>> selectUserBlockedList(@Param("userId") Long userId);

    /**
     * 添加屏蔽用户
     *
     * @param userId 用户ID
     * @param blockedUserId 被屏蔽用户ID
     * @return 结果
     */
    public int insertBlockedUser(@Param("userId") Long userId, @Param("blockedUserId") Long blockedUserId);

    /**
     * 解除屏蔽用户
     *
     * @param ids 屏蔽记录ID列表
     * @return 结果
     */
    public int deleteBlockedUser(@Param("ids") List<Long> ids);
}
