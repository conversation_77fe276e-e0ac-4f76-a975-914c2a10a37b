{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753803250375}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_coach", "require", "_coachService", "name", "data", "title", "activeName", "form", "timeForm", "is_work", "start_time", "end_time", "rules", "true_user_name", "required", "message", "trigger", "min", "max", "id_card", "pattern", "virtual_collect", "type", "virtual_comment", "pv", "timeRules", "serviceList", "serviceLoading", "serviceTotal", "serviceQueryParams", "pageNum", "pageSize", "coachId", "showServiceDialog", "availableServiceList", "availableServiceLoading", "availableServiceTotal", "availableServiceQueryParams", "serviceSearchForm", "selectedServices", "orderList", "orderLoading", "orderTotal", "orderQueryParams", "created", "id", "$route", "query", "getInfo", "getWorkTime", "getServiceList", "getOrderList", "reset", "methods", "_this", "getCoach", "then", "response", "license", "self_img", "text", "resetForm", "submitForm", "_this2", "$refs", "validate", "valid", "updateCoach", "$modal", "msgSuccess", "goBack", "addCoach", "$router", "push", "path", "_this3", "getCoachWorkTime", "catch", "saveWorkTime", "_this4", "_objectSpread2", "default", "saveCoachWorkTime", "_this5", "listCoachService", "rows", "total", "getAvailableServiceList", "_this6", "listAvailableService", "searchAvailableServices", "resetServiceSearch", "handleServiceSelectionChange", "selection", "addServices", "_this7", "length", "msgError", "serviceIds", "map", "item", "addCoachService", "removeService", "serviceId", "_this8", "confirm", "delCoachService", "_this9", "listCoachOrder", "getOrderStatusType", "status", "statusMap", "getOrderStatusText"], "sources": ["src/views/massage/coach/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <el-tabs v-model=\"activeName\" type=\"card\">\n        <!-- 基础信息 -->\n        <el-tab-pane label=\"基础信息\" name=\"basic\">\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"真实姓名\" prop=\"true_user_name\">\n              <el-input v-model=\"form.true_user_name\" maxlength=\"15\" show-word-limit placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证号\" prop=\"id_card\">\n              <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证照片\" prop=\"license\">\n              <image-upload v-model=\"form.license\" :limit=\"2\"/>\n              <div class=\"el-upload__tip\">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"生活照\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\" :limit=\"9\"/>\n              <div class=\"el-upload__tip\">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"达人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入达人简介\" />\n            </el-form-item>\n\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟收藏量\" prop=\"virtual_collect\">\n                  <el-input-number v-model=\"form.virtual_collect\" :min=\"0\" placeholder=\"请输入虚拟收藏量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟评论量\" prop=\"virtual_comment\">\n                  <el-input-number v-model=\"form.virtual_comment\" :min=\"0\" placeholder=\"请输入虚拟评论量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"访问量\" prop=\"pv\">\n                  <el-input-number v-model=\"form.pv\" :min=\"0\" placeholder=\"请输入访问量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n            <div style=\"text-align: center; margin-top: 20px;\">\n              <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n              <el-button @click=\"goBack\">取 消</el-button>\n            </div>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 接单时间 -->\n        <el-tab-pane label=\"接单时间\" name=\"workTime\" v-if=\"form.id\">\n          <el-form ref=\"timeForm\" :model=\"timeForm\" :rules=\"timeRules\" label-width=\"120px\">\n            <el-form-item label=\"是否接单\" prop=\"is_work\">\n              <el-radio-group v-model=\"timeForm.is_work\">\n                <el-radio :label=\"1\">接单</el-radio>\n                <el-radio :label=\"0\">休息</el-radio>\n              </el-radio-group>\n            </el-form-item>\n\n            <el-form-item label=\"接单时间\" prop=\"start_time\" v-if=\"timeForm.is_work\">\n              <div style=\"display: flex; align-items: center;\">\n                <el-time-picker\n                  v-model=\"timeForm.start_time\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n                <span style=\"margin: 0 10px;\">至</span>\n                <el-time-picker\n                  v-model=\"timeForm.end_time\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n              </div>\n            </el-form-item>\n\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"saveWorkTime\">保存接单时间</el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 已关联服务 -->\n        <el-tab-pane label=\"已关联服务\" name=\"services\" v-if=\"form.id\">\n          <div style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" @click=\"showServiceDialog = true\">关联服务</el-button>\n          </div>\n\n          <el-table v-loading=\"serviceLoading\" :data=\"serviceList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n            <el-table-column prop=\"title\" label=\"服务名称\" />\n            <el-table-column prop=\"price\" label=\"服务价格\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"time_long\" label=\"服务时长\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.time_long }}分钟</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"danger\" @click=\"removeService(scope.row.id)\">移除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"serviceTotal > 0\"\n            :total=\"serviceTotal\"\n            :page.sync=\"serviceQueryParams.pageNum\"\n            :limit.sync=\"serviceQueryParams.pageSize\"\n            @pagination=\"getServiceList\"\n          />\n        </el-tab-pane>\n\n        <!-- 服务记录 -->\n        <el-tab-pane label=\"服务记录\" name=\"orders\" v-if=\"form.id\">\n          <el-table v-loading=\"orderLoading\" :data=\"orderList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"订单ID\" width=\"80\" />\n            <el-table-column prop=\"order_code\" label=\"订单号\" width=\"180\" />\n            <el-table-column prop=\"goods_info\" label=\"服务项目\" />\n            <el-table-column prop=\"pay_price\" label=\"订单金额\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.pay_price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"order_status\" label=\"订单状态\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getOrderStatusType(scope.row.order_status)\">\n                  {{ getOrderStatusText(scope.row.order_status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"create_time\" label=\"下单时间\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"orderTotal > 0\"\n            :total=\"orderTotal\"\n            :page.sync=\"orderQueryParams.pageNum\"\n            :limit.sync=\"orderQueryParams.pageSize\"\n            @pagination=\"getOrderList\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n\n    <!-- 关联服务对话框 -->\n    <el-dialog title=\"关联服务\" :visible.sync=\"showServiceDialog\" width=\"800px\" append-to-body>\n      <el-form :inline=\"true\" :model=\"serviceSearchForm\" class=\"demo-form-inline\">\n        <el-form-item label=\"服务名称\">\n          <el-input v-model=\"serviceSearchForm.title\" placeholder=\"请输入服务名称\" clearable />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"searchAvailableServices\">查询</el-button>\n          <el-button @click=\"resetServiceSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-table\n        ref=\"serviceTable\"\n        v-loading=\"availableServiceLoading\"\n        :data=\"availableServiceList\"\n        @selection-change=\"handleServiceSelectionChange\"\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n        <el-table-column prop=\"title\" label=\"服务名称\" />\n        <el-table-column prop=\"price\" label=\"价格\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.price }}元</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"time_long\" label=\"时长\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.time_long }}分钟</span>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"availableServiceTotal > 0\"\n        :total=\"availableServiceTotal\"\n        :page.sync=\"availableServiceQueryParams.pageNum\"\n        :limit.sync=\"availableServiceQueryParams.pageSize\"\n        @pagination=\"getAvailableServiceList\"\n      />\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showServiceDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addServices\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\nimport {\n  listCoachService,\n  listAvailableService,\n  addCoachService,\n  delCoachService,\n  getCoachWorkTime,\n  saveCoachWorkTime,\n  listCoachOrder\n} from \"@/api/massage/coachService\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 接单时间表单\n      timeForm: {\n        is_work: 1,\n        start_time: null,\n        end_time: null\n      },\n      // 表单校验\n      rules: {\n        true_user_name: [\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\n          { min: 2, max: 15, message: \"姓名长度在 2 到 15 个字符\", trigger: \"blur\" }\n        ],\n        id_card: [\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\n          { pattern: /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: \"请输入正确的身份证号\", trigger: \"blur\" }\n        ],\n        virtual_collect: [\n          { type: \"number\", min: 0, message: \"虚拟收藏量不能小于0\", trigger: \"blur\" }\n        ],\n        virtual_comment: [\n          { type: \"number\", min: 0, message: \"虚拟评论量不能小于0\", trigger: \"blur\" }\n        ],\n        pv: [\n          { type: \"number\", min: 0, message: \"访问量不能小于0\", trigger: \"blur\" }\n        ]\n      },\n      // 接单时间校验\n      timeRules: {\n        is_work: [\n          { required: true, message: \"请选择是否接单\", trigger: \"change\" }\n        ]\n      },\n      // 已关联服务\n      serviceList: [],\n      serviceLoading: false,\n      serviceTotal: 0,\n      serviceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      },\n      // 可关联服务\n      showServiceDialog: false,\n      availableServiceList: [],\n      availableServiceLoading: false,\n      availableServiceTotal: 0,\n      availableServiceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null\n      },\n      serviceSearchForm: {\n        title: null\n      },\n      selectedServices: [],\n      // 服务记录\n      orderList: [],\n      orderLoading: false,\n      orderTotal: 0,\n      orderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n      this.serviceQueryParams.coachId = id;\n      this.orderQueryParams.coachId = id;\n      this.getWorkTime(id);\n      this.getServiceList();\n      this.getOrderList();\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        true_user_name: null,\n        id_card: null,\n        license: null,\n        self_img: null,\n        text: null,\n        virtual_collect: 0,\n        virtual_comment: 0,\n        pv: 0\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push({ path: \"/massage/coach\" });\n    },\n\n    /** 获取接单时间 */\n    getWorkTime(id) {\n      getCoachWorkTime(id).then(response => {\n        this.timeForm = response.data || {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      }).catch(() => {\n        // 如果获取失败，使用默认值\n        this.timeForm = {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      });\n    },\n\n    /** 保存接单时间 */\n    saveWorkTime() {\n      this.$refs[\"timeForm\"].validate(valid => {\n        if (valid) {\n          const data = {\n            coachId: this.form.id,\n            ...this.timeForm\n          };\n          saveCoachWorkTime(data).then(() => {\n            this.$modal.msgSuccess(\"接单时间保存成功\");\n          });\n        }\n      });\n    },\n\n    /** 获取已关联服务列表 */\n    getServiceList() {\n      this.serviceLoading = true;\n      listCoachService(this.serviceQueryParams).then(response => {\n        this.serviceList = response.rows;\n        this.serviceTotal = response.total;\n        this.serviceLoading = false;\n      }).catch(() => {\n        this.serviceLoading = false;\n      });\n    },\n\n    /** 获取可关联服务列表 */\n    getAvailableServiceList() {\n      this.availableServiceLoading = true;\n      listAvailableService(this.availableServiceQueryParams).then(response => {\n        this.availableServiceList = response.rows;\n        this.availableServiceTotal = response.total;\n        this.availableServiceLoading = false;\n      }).catch(() => {\n        this.availableServiceLoading = false;\n      });\n    },\n\n    /** 搜索可关联服务 */\n    searchAvailableServices() {\n      this.availableServiceQueryParams.pageNum = 1;\n      this.availableServiceQueryParams.title = this.serviceSearchForm.title;\n      this.getAvailableServiceList();\n    },\n\n    /** 重置服务搜索 */\n    resetServiceSearch() {\n      this.serviceSearchForm.title = null;\n      this.availableServiceQueryParams.title = null;\n      this.getAvailableServiceList();\n    },\n\n    /** 服务选择变化 */\n    handleServiceSelectionChange(selection) {\n      this.selectedServices = selection;\n    },\n\n    /** 添加服务 */\n    addServices() {\n      if (this.selectedServices.length === 0) {\n        this.$modal.msgError(\"请选择要关联的服务\");\n        return;\n      }\n      const data = {\n        coachId: this.form.id,\n        serviceIds: this.selectedServices.map(item => item.id)\n      };\n      addCoachService(data).then(() => {\n        this.$modal.msgSuccess(\"服务关联成功\");\n        this.showServiceDialog = false;\n        this.getServiceList();\n      });\n    },\n\n    /** 移除服务 */\n    removeService(serviceId) {\n      this.$modal.confirm('确认要移除该服务吗？').then(() => {\n        delCoachService(this.form.id, serviceId).then(() => {\n          this.$modal.msgSuccess(\"服务移除成功\");\n          this.getServiceList();\n        });\n      });\n    },\n\n    /** 获取服务记录列表 */\n    getOrderList() {\n      this.orderLoading = true;\n      listCoachOrder(this.orderQueryParams).then(response => {\n        this.orderList = response.rows;\n        this.orderTotal = response.total;\n        this.orderLoading = false;\n      }).catch(() => {\n        this.orderLoading = false;\n      });\n    },\n\n    /** 获取订单状态类型 */\n    getOrderStatusType(status) {\n      const statusMap = {\n        1: 'warning',\n        2: 'success',\n        3: 'danger',\n        4: 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n\n    /** 获取订单状态文本 */\n    getOrderStatusText(status) {\n      const statusMap = {\n        1: '待服务',\n        2: '已完成',\n        3: '已取消',\n        4: '已退款'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;AAyNA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACA;MACAC,KAAA;QACAC,cAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,OAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,eAAA,GACA;UAAAC,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,eAAA,GACA;UAAAD,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,EAAA,GACA;UAAAF,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAS,SAAA;QACAhB,OAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAU,WAAA;MACAC,cAAA;MACAC,YAAA;MACAC,kBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACA;MACAC,iBAAA;MACAC,oBAAA;MACAC,uBAAA;MACAC,qBAAA;MACAC,2BAAA;QACAP,OAAA;QACAC,QAAA;QACA1B,KAAA;MACA;MACAiC,iBAAA;QACAjC,KAAA;MACA;MACAkC,gBAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,gBAAA;QACAb,OAAA;QACAC,QAAA;QACAC,OAAA;MACA;IACA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAF,EAAA;IACA,KAAAtC,IAAA,CAAAsC,EAAA,GAAAA,EAAA;IACA,IAAAA,EAAA;MACA,KAAAxC,KAAA;MACA,KAAA2C,OAAA,CAAAH,EAAA;MACA,KAAAhB,kBAAA,CAAAG,OAAA,GAAAa,EAAA;MACA,KAAAF,gBAAA,CAAAX,OAAA,GAAAa,EAAA;MACA,KAAAI,WAAA,CAAAJ,EAAA;MACA,KAAAK,cAAA;MACA,KAAAC,YAAA;IACA;MACA,KAAA9C,KAAA;MACA,KAAA+C,KAAA;IACA;EACA;EACAC,OAAA;IACA,aACAL,OAAA,WAAAA,QAAAH,EAAA;MAAA,IAAAS,KAAA;MACA,IAAAC,eAAA,EAAAV,EAAA,EAAAW,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA/C,IAAA,GAAAkD,QAAA,CAAArD,IAAA;MACA;IACA;IACA;IACAgD,KAAA,WAAAA,MAAA;MACA,KAAA7C,IAAA;QACAsC,EAAA;QACAhC,cAAA;QACAM,OAAA;QACAuC,OAAA;QACAC,QAAA;QACAC,IAAA;QACAvC,eAAA;QACAE,eAAA;QACAC,EAAA;MACA;MACA,KAAAqC,SAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAxD,IAAA,CAAAsC,EAAA;YACA,IAAAsB,kBAAA,EAAAJ,MAAA,CAAAxD,IAAA,EAAAiD,IAAA;cACAO,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;UACA;YACA,IAAAC,eAAA,EAAAR,MAAA,CAAAxD,IAAA,EAAAiD,IAAA;cACAO,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;UACA;QACA;MACA;IACA;IACA,SACAA,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IAEA,aACAzB,WAAA,WAAAA,YAAAJ,EAAA;MAAA,IAAA8B,MAAA;MACA,IAAAC,8BAAA,EAAA/B,EAAA,EAAAW,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAAnE,QAAA,GAAAiD,QAAA,CAAArD,IAAA;UACAK,OAAA;UACAC,UAAA;UACAC,QAAA;QACA;MACA,GAAAkE,KAAA;QACA;QACAF,MAAA,CAAAnE,QAAA;UACAC,OAAA;UACAC,UAAA;UACAC,QAAA;QACA;MACA;IACA;IAEA,aACAmE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAf,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA9D,IAAA,OAAA4E,cAAA,CAAAC,OAAA;YACAjD,OAAA,EAAA+C,MAAA,CAAAxE,IAAA,CAAAsC;UAAA,GACAkC,MAAA,CAAAvE,QAAA,CACA;UACA,IAAA0E,+BAAA,EAAA9E,IAAA,EAAAoD,IAAA;YACAuB,MAAA,CAAAX,MAAA,CAAAC,UAAA;UACA;QACA;MACA;IACA;IAEA,gBACAnB,cAAA,WAAAA,eAAA;MAAA,IAAAiC,MAAA;MACA,KAAAxD,cAAA;MACA,IAAAyD,8BAAA,OAAAvD,kBAAA,EAAA2B,IAAA,WAAAC,QAAA;QACA0B,MAAA,CAAAzD,WAAA,GAAA+B,QAAA,CAAA4B,IAAA;QACAF,MAAA,CAAAvD,YAAA,GAAA6B,QAAA,CAAA6B,KAAA;QACAH,MAAA,CAAAxD,cAAA;MACA,GAAAkD,KAAA;QACAM,MAAA,CAAAxD,cAAA;MACA;IACA;IAEA,gBACA4D,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,KAAArD,uBAAA;MACA,IAAAsD,kCAAA,OAAApD,2BAAA,EAAAmB,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAAtD,oBAAA,GAAAuB,QAAA,CAAA4B,IAAA;QACAG,MAAA,CAAApD,qBAAA,GAAAqB,QAAA,CAAA6B,KAAA;QACAE,MAAA,CAAArD,uBAAA;MACA,GAAA0C,KAAA;QACAW,MAAA,CAAArD,uBAAA;MACA;IACA;IAEA,cACAuD,uBAAA,WAAAA,wBAAA;MACA,KAAArD,2BAAA,CAAAP,OAAA;MACA,KAAAO,2BAAA,CAAAhC,KAAA,QAAAiC,iBAAA,CAAAjC,KAAA;MACA,KAAAkF,uBAAA;IACA;IAEA,aACAI,kBAAA,WAAAA,mBAAA;MACA,KAAArD,iBAAA,CAAAjC,KAAA;MACA,KAAAgC,2BAAA,CAAAhC,KAAA;MACA,KAAAkF,uBAAA;IACA;IAEA,aACAK,4BAAA,WAAAA,6BAAAC,SAAA;MACA,KAAAtD,gBAAA,GAAAsD,SAAA;IACA;IAEA,WACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAxD,gBAAA,CAAAyD,MAAA;QACA,KAAA5B,MAAA,CAAA6B,QAAA;QACA;MACA;MACA,IAAA7F,IAAA;QACA4B,OAAA,OAAAzB,IAAA,CAAAsC,EAAA;QACAqD,UAAA,OAAA3D,gBAAA,CAAA4D,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAvD,EAAA;QAAA;MACA;MACA,IAAAwD,6BAAA,EAAAjG,IAAA,EAAAoD,IAAA;QACAuC,MAAA,CAAA3B,MAAA,CAAAC,UAAA;QACA0B,MAAA,CAAA9D,iBAAA;QACA8D,MAAA,CAAA7C,cAAA;MACA;IACA;IAEA,WACAoD,aAAA,WAAAA,cAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAApC,MAAA,CAAAqC,OAAA,eAAAjD,IAAA;QACA,IAAAkD,6BAAA,EAAAF,MAAA,CAAAjG,IAAA,CAAAsC,EAAA,EAAA0D,SAAA,EAAA/C,IAAA;UACAgD,MAAA,CAAApC,MAAA,CAAAC,UAAA;UACAmC,MAAA,CAAAtD,cAAA;QACA;MACA;IACA;IAEA,eACAC,YAAA,WAAAA,aAAA;MAAA,IAAAwD,MAAA;MACA,KAAAlE,YAAA;MACA,IAAAmE,4BAAA,OAAAjE,gBAAA,EAAAa,IAAA,WAAAC,QAAA;QACAkD,MAAA,CAAAnE,SAAA,GAAAiB,QAAA,CAAA4B,IAAA;QACAsB,MAAA,CAAAjE,UAAA,GAAAe,QAAA,CAAA6B,KAAA;QACAqB,MAAA,CAAAlE,YAAA;MACA,GAAAoC,KAAA;QACA8B,MAAA,CAAAlE,YAAA;MACA;IACA;IAEA,eACAoE,kBAAA,WAAAA,mBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,eACAE,kBAAA,WAAAA,mBAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}