{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753804318587}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_coach", "require", "_coachService", "name", "data", "title", "activeName", "form", "timeForm", "is_work", "start_time", "end_time", "rules", "true_user_name", "required", "message", "trigger", "min", "max", "id_card", "pattern", "virtual_collect", "type", "virtual_comment", "pv", "timeRules", "serviceList", "serviceLoading", "serviceTotal", "serviceQueryParams", "pageNum", "pageSize", "coachId", "showServiceDialog", "availableServiceList", "availableServiceLoading", "availableServiceTotal", "availableServiceQueryParams", "serviceSearchForm", "selectedServices", "orderList", "orderLoading", "orderTotal", "orderQueryParams", "created", "id", "$route", "query", "getInfo", "getWorkTime", "getServiceList", "getOrderList", "reset", "methods", "_this", "getCoach", "then", "response", "coach_name", "mobile", "sex", "work_img", "create_time", "Math", "floor", "Date", "now", "balance_cash", "total_order_num", "service_price", "auth_status", "status", "license", "self_img", "text", "resetForm", "submitForm", "_this2", "$refs", "validate", "valid", "updateCoach", "$modal", "msgSuccess", "goBack", "addCoach", "$router", "push", "path", "saveWorkTime", "_this3", "_this4", "setTimeout", "price", "time_long", "getAvailableServiceList", "_this5", "searchAvailableServices", "resetServiceSearch", "handleServiceSelectionChange", "selection", "addServices", "length", "msgError", "removeService", "serviceId", "_this6", "confirm", "_this7", "order_code", "goods_info", "pay_price", "order_status", "getOrderStatusType", "statusMap", "getOrderStatusText", "getAuthStatusType", "typeMap", "getAuthStatusText", "textMap", "getStatusType", "getStatusText", "getSexText", "calculateServiceTime", "calculateOnlineTime", "days", "calculatePerformance"], "sources": ["src/views/massage/coach/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <!-- 达人基础信息展示 -->\n      <el-card class=\"coach-info-card\" v-if=\"form.id\" style=\"margin-bottom: 20px;\">\n        <div class=\"coach-header\">\n          <div class=\"coach-avatar\">\n            <img :src=\"form.work_img\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover;\" v-if=\"form.work_img\"/>\n            <div v-else class=\"default-avatar\">\n              <i class=\"el-icon-user\" style=\"font-size: 40px; color: #ccc;\"></i>\n            </div>\n          </div>\n          <div class=\"coach-basic-info\">\n            <div class=\"coach-name-row\">\n              <h3 class=\"coach-name\">{{ form.coach_name || '未设置' }}</h3>\n              <el-tag\n                :type=\"getAuthStatusType(form.auth_status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getAuthStatusText(form.auth_status) }}\n              </el-tag>\n              <el-tag\n                :type=\"getStatusType(form.status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getStatusText(form.status) }}\n              </el-tag>\n            </div>\n            <div class=\"coach-details\">\n              <p><span class=\"label\">ID：</span><span class=\"value\">{{ form.id }}</span></p>\n              <p><span class=\"label\">性别：</span><span class=\"value\">{{ getSexText(form.sex) }}</span></p>\n              <p><span class=\"label\">手机号：</span><span class=\"value\">{{ form.mobile || '未设置' }}</span></p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"coach-stats\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">申请时间</div>\n                <div class=\"stat-value\">{{ parseTime(form.create_time * 1000, '{y}-{m}-{d}') }}</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">账号余额</div>\n                <div class=\"stat-value\">{{ form.balance_cash || 0 }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">服务时长</div>\n                <div class=\"stat-value\">{{ calculateServiceTime() }}小时</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">在线时长</div>\n                <div class=\"stat-value\">{{ calculateOnlineTime() }}小时</div>\n              </div>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">业绩</div>\n                <div class=\"stat-value\">{{ calculatePerformance() }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否认证</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.auth_status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.auth_status == 2 ? '已认证' : '未认证' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否授权</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.status == 2 ? '已授权' : '未授权' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">总订单数</div>\n                <div class=\"stat-value\">{{ form.total_order_num || 0 }}单</div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-card>\n\n      <el-tabs v-model=\"activeName\" type=\"card\">\n        <!-- 基础信息 -->\n        <el-tab-pane label=\"基础信息\" name=\"basic\">\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"真实姓名\" prop=\"true_user_name\">\n              <el-input v-model=\"form.true_user_name\" maxlength=\"15\" show-word-limit placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证号\" prop=\"id_card\">\n              <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证照片\" prop=\"license\">\n              <image-upload v-model=\"form.license\" :limit=\"2\"/>\n              <div class=\"el-upload__tip\">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"生活照\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\" :limit=\"9\"/>\n              <div class=\"el-upload__tip\">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"达人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入达人简介\" />\n            </el-form-item>\n\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟收藏量\" prop=\"virtual_collect\">\n                  <el-input-number v-model=\"form.virtual_collect\" :min=\"0\" placeholder=\"请输入虚拟收藏量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟评论量\" prop=\"virtual_comment\">\n                  <el-input-number v-model=\"form.virtual_comment\" :min=\"0\" placeholder=\"请输入虚拟评论量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"访问量\" prop=\"pv\">\n                  <el-input-number v-model=\"form.pv\" :min=\"0\" placeholder=\"请输入访问量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n            <div style=\"text-align: center; margin-top: 20px;\">\n              <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n              <el-button @click=\"goBack\">取 消</el-button>\n            </div>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 接单时间 -->\n        <el-tab-pane label=\"接单时间\" name=\"workTime\" v-if=\"form.id\">\n          <el-form ref=\"timeForm\" :model=\"timeForm\" :rules=\"timeRules\" label-width=\"120px\">\n            <el-form-item label=\"是否接单\" prop=\"is_work\">\n              <el-radio-group v-model=\"timeForm.is_work\">\n                <el-radio :label=\"1\">接单</el-radio>\n                <el-radio :label=\"0\">休息</el-radio>\n              </el-radio-group>\n            </el-form-item>\n\n            <el-form-item label=\"接单时间\" prop=\"start_time\" v-if=\"timeForm.is_work\">\n              <div style=\"display: flex; align-items: center;\">\n                <el-time-picker\n                  v-model=\"timeForm.start_time\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n                <span style=\"margin: 0 10px;\">至</span>\n                <el-time-picker\n                  v-model=\"timeForm.end_time\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n              </div>\n            </el-form-item>\n\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"saveWorkTime\">保存接单时间</el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 已关联服务 -->\n        <el-tab-pane label=\"已关联服务\" name=\"services\" v-if=\"form.id\">\n          <div style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" @click=\"showServiceDialog = true\">关联服务</el-button>\n          </div>\n\n          <el-table v-loading=\"serviceLoading\" :data=\"serviceList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n            <el-table-column prop=\"title\" label=\"服务名称\" />\n            <el-table-column prop=\"price\" label=\"服务价格\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"time_long\" label=\"服务时长\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.time_long }}分钟</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"danger\" @click=\"removeService(scope.row.id)\">移除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"serviceTotal > 0\"\n            :total=\"serviceTotal\"\n            :page.sync=\"serviceQueryParams.pageNum\"\n            :limit.sync=\"serviceQueryParams.pageSize\"\n            @pagination=\"getServiceList\"\n          />\n        </el-tab-pane>\n\n        <!-- 服务记录 -->\n        <el-tab-pane label=\"服务记录\" name=\"orders\" v-if=\"form.id\">\n          <el-table v-loading=\"orderLoading\" :data=\"orderList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"订单ID\" width=\"80\" />\n            <el-table-column prop=\"order_code\" label=\"订单号\" width=\"180\" />\n            <el-table-column prop=\"goods_info\" label=\"服务项目\" />\n            <el-table-column prop=\"pay_price\" label=\"订单金额\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.pay_price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"order_status\" label=\"订单状态\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getOrderStatusType(scope.row.order_status)\">\n                  {{ getOrderStatusText(scope.row.order_status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"create_time\" label=\"下单时间\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"orderTotal > 0\"\n            :total=\"orderTotal\"\n            :page.sync=\"orderQueryParams.pageNum\"\n            :limit.sync=\"orderQueryParams.pageSize\"\n            @pagination=\"getOrderList\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n\n    <!-- 关联服务对话框 -->\n    <el-dialog title=\"关联服务\" :visible.sync=\"showServiceDialog\" width=\"800px\" append-to-body>\n      <el-form :inline=\"true\" :model=\"serviceSearchForm\" class=\"demo-form-inline\">\n        <el-form-item label=\"服务名称\">\n          <el-input v-model=\"serviceSearchForm.title\" placeholder=\"请输入服务名称\" clearable />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"searchAvailableServices\">查询</el-button>\n          <el-button @click=\"resetServiceSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-table\n        ref=\"serviceTable\"\n        v-loading=\"availableServiceLoading\"\n        :data=\"availableServiceList\"\n        @selection-change=\"handleServiceSelectionChange\"\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n        <el-table-column prop=\"title\" label=\"服务名称\" />\n        <el-table-column prop=\"price\" label=\"价格\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.price }}元</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"time_long\" label=\"时长\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.time_long }}分钟</span>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"availableServiceTotal > 0\"\n        :total=\"availableServiceTotal\"\n        :page.sync=\"availableServiceQueryParams.pageNum\"\n        :limit.sync=\"availableServiceQueryParams.pageSize\"\n        @pagination=\"getAvailableServiceList\"\n      />\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showServiceDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addServices\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\nimport {\n  listCoachService,\n  listAvailableService,\n  addCoachService,\n  delCoachService,\n  getCoachWorkTime,\n  saveCoachWorkTime,\n  listCoachOrder\n} from \"@/api/massage/coachService\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 接单时间表单\n      timeForm: {\n        is_work: 1,\n        start_time: null,\n        end_time: null\n      },\n      // 表单校验\n      rules: {\n        true_user_name: [\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\n          { min: 2, max: 15, message: \"姓名长度在 2 到 15 个字符\", trigger: \"blur\" }\n        ],\n        id_card: [\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\n          { pattern: /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: \"请输入正确的身份证号\", trigger: \"blur\" }\n        ],\n        virtual_collect: [\n          { type: \"number\", min: 0, message: \"虚拟收藏量不能小于0\", trigger: \"blur\" }\n        ],\n        virtual_comment: [\n          { type: \"number\", min: 0, message: \"虚拟评论量不能小于0\", trigger: \"blur\" }\n        ],\n        pv: [\n          { type: \"number\", min: 0, message: \"访问量不能小于0\", trigger: \"blur\" }\n        ]\n      },\n      // 接单时间校验\n      timeRules: {\n        is_work: [\n          { required: true, message: \"请选择是否接单\", trigger: \"change\" }\n        ]\n      },\n      // 已关联服务\n      serviceList: [],\n      serviceLoading: false,\n      serviceTotal: 0,\n      serviceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      },\n      // 可关联服务\n      showServiceDialog: false,\n      availableServiceList: [],\n      availableServiceLoading: false,\n      availableServiceTotal: 0,\n      availableServiceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null\n      },\n      serviceSearchForm: {\n        title: null\n      },\n      selectedServices: [],\n      // 服务记录\n      orderList: [],\n      orderLoading: false,\n      orderTotal: 0,\n      orderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n      this.serviceQueryParams.coachId = id;\n      this.orderQueryParams.coachId = id;\n      this.getWorkTime(id);\n      this.getServiceList();\n      this.getOrderList();\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        coach_name: null,\n        mobile: null,\n        sex: 1,\n        work_img: null,\n        create_time: Math.floor(Date.now() / 1000),\n        balance_cash: 0,\n        total_order_num: 0,\n        service_price: 0,\n        auth_status: 0,\n        status: 1,\n        true_user_name: null,\n        id_card: null,\n        license: null,\n        self_img: null,\n        text: null,\n        virtual_collect: 0,\n        virtual_comment: 0,\n        pv: 0\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push({ path: \"/massage/coach\" });\n    },\n\n    /** 获取接单时间 */\n    getWorkTime(id) {\n      // 暂时使用模拟数据，避免404错误\n      this.timeForm = {\n        is_work: 1,\n        start_time: \"09:00\",\n        end_time: \"18:00\"\n      };\n\n      // TODO: 等后端接口完成后启用真实API调用\n      // getCoachWorkTime(id).then(response => {\n      //   this.timeForm = response.data || {\n      //     is_work: 1,\n      //     start_time: \"09:00\",\n      //     end_time: \"18:00\"\n      //   };\n      // }).catch(() => {\n      //   this.timeForm = {\n      //     is_work: 1,\n      //     start_time: \"09:00\",\n      //     end_time: \"18:00\"\n      //   };\n      // });\n    },\n\n    /** 保存接单时间 */\n    saveWorkTime() {\n      this.$refs[\"timeForm\"].validate(valid => {\n        if (valid) {\n          // 暂时使用模拟保存，避免404错误\n          this.$modal.msgSuccess(\"接单时间保存成功\");\n\n          // TODO: 等后端接口完成后启用真实API调用\n          // const data = {\n          //   coachId: this.form.id,\n          //   ...this.timeForm\n          // };\n          // saveCoachWorkTime(data).then(() => {\n          //   this.$modal.msgSuccess(\"接单时间保存成功\");\n          // });\n        }\n      });\n    },\n\n    /** 获取已关联服务列表 */\n    getServiceList() {\n      this.serviceLoading = true;\n      // 暂时使用模拟数据，避免404错误\n      setTimeout(() => {\n        this.serviceList = [\n          {\n            id: 1,\n            title: \"按摩服务\",\n            price: 100,\n            time_long: 60,\n            create_time: Math.floor(Date.now() / 1000) - 86400\n          },\n          {\n            id: 2,\n            title: \"足疗服务\",\n            price: 80,\n            time_long: 45,\n            create_time: Math.floor(Date.now() / 1000) - 172800\n          }\n        ];\n        this.serviceTotal = 2;\n        this.serviceLoading = false;\n      }, 500);\n\n      // TODO: 等后端接口完成后启用真实API调用\n      // listCoachService(this.serviceQueryParams).then(response => {\n      //   this.serviceList = response.rows;\n      //   this.serviceTotal = response.total;\n      //   this.serviceLoading = false;\n      // }).catch(() => {\n      //   this.serviceLoading = false;\n      // });\n    },\n\n    /** 获取可关联服务列表 */\n    getAvailableServiceList() {\n      this.availableServiceLoading = true;\n      // 暂时使用模拟数据，避免404错误\n      setTimeout(() => {\n        this.availableServiceList = [\n          {\n            id: 3,\n            title: \"SPA服务\",\n            price: 200,\n            time_long: 90\n          },\n          {\n            id: 4,\n            title: \"推拿服务\",\n            price: 120,\n            time_long: 60\n          },\n          {\n            id: 5,\n            title: \"艾灸服务\",\n            price: 150,\n            time_long: 75\n          }\n        ];\n        this.availableServiceTotal = 3;\n        this.availableServiceLoading = false;\n      }, 500);\n\n      // TODO: 等后端接口完成后启用真实API调用\n      // listAvailableService(this.availableServiceQueryParams).then(response => {\n      //   this.availableServiceList = response.rows;\n      //   this.availableServiceTotal = response.total;\n      //   this.availableServiceLoading = false;\n      // }).catch(() => {\n      //   this.availableServiceLoading = false;\n      // });\n    },\n\n    /** 搜索可关联服务 */\n    searchAvailableServices() {\n      this.availableServiceQueryParams.pageNum = 1;\n      this.availableServiceQueryParams.title = this.serviceSearchForm.title;\n      this.getAvailableServiceList();\n    },\n\n    /** 重置服务搜索 */\n    resetServiceSearch() {\n      this.serviceSearchForm.title = null;\n      this.availableServiceQueryParams.title = null;\n      this.getAvailableServiceList();\n    },\n\n    /** 服务选择变化 */\n    handleServiceSelectionChange(selection) {\n      this.selectedServices = selection;\n    },\n\n    /** 添加服务 */\n    addServices() {\n      if (this.selectedServices.length === 0) {\n        this.$modal.msgError(\"请选择要关联的服务\");\n        return;\n      }\n      // 暂时使用模拟保存，避免404错误\n      this.$modal.msgSuccess(\"服务关联成功\");\n      this.showServiceDialog = false;\n      this.getServiceList();\n\n      // TODO: 等后端接口完成后启用真实API调用\n      // const data = {\n      //   coachId: this.form.id,\n      //   serviceIds: this.selectedServices.map(item => item.id)\n      // };\n      // addCoachService(data).then(() => {\n      //   this.$modal.msgSuccess(\"服务关联成功\");\n      //   this.showServiceDialog = false;\n      //   this.getServiceList();\n      // });\n    },\n\n    /** 移除服务 */\n    removeService(serviceId) {\n      this.$modal.confirm('确认要移除该服务吗？').then(() => {\n        // 暂时使用模拟删除，避免404错误\n        this.$modal.msgSuccess(\"服务移除成功\");\n        this.getServiceList();\n\n        // TODO: 等后端接口完成后启用真实API调用\n        // delCoachService(this.form.id, serviceId).then(() => {\n        //   this.$modal.msgSuccess(\"服务移除成功\");\n        //   this.getServiceList();\n        // });\n      });\n    },\n\n    /** 获取服务记录列表 */\n    getOrderList() {\n      this.orderLoading = true;\n      // 暂时使用模拟数据，避免404错误\n      setTimeout(() => {\n        this.orderList = [\n          {\n            id: 1,\n            order_code: \"ORD202501010001\",\n            goods_info: \"按摩服务\",\n            pay_price: 100,\n            order_status: 1,\n            create_time: Math.floor(Date.now() / 1000) - 86400\n          },\n          {\n            id: 2,\n            order_code: \"ORD202501010002\",\n            goods_info: \"足疗服务\",\n            pay_price: 80,\n            order_status: 2,\n            create_time: Math.floor(Date.now() / 1000) - 172800\n          }\n        ];\n        this.orderTotal = 2;\n        this.orderLoading = false;\n      }, 500);\n\n      // TODO: 等后端接口完成后启用真实API调用\n      // listCoachOrder(this.orderQueryParams).then(response => {\n      //   this.orderList = response.rows;\n      //   this.orderTotal = response.total;\n      //   this.orderLoading = false;\n      // }).catch(() => {\n      //   this.orderLoading = false;\n      // });\n    },\n\n    /** 获取订单状态类型 */\n    getOrderStatusType(status) {\n      const statusMap = {\n        1: 'warning',\n        2: 'success',\n        3: 'danger',\n        4: 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n\n    /** 获取订单状态文本 */\n    getOrderStatusText(status) {\n      const statusMap = {\n        1: '待服务',\n        2: '已完成',\n        3: '已取消',\n        4: '已退款'\n      };\n      return statusMap[status] || '未知';\n    },\n\n    /** 获取认证状态类型 */\n    getAuthStatusType(status) {\n      const typeMap = {\n        0: 'info',     // 未认证\n        1: 'warning',  // 认证中\n        2: 'success',  // 已认证\n        3: 'danger'    // 认证失败\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取认证状态文本 */\n    getAuthStatusText(status) {\n      const textMap = {\n        0: '未认证',\n        1: '认证中',\n        2: '已认证',\n        3: '认证失败'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取状态类型 */\n    getStatusType(status) {\n      const typeMap = {\n        1: 'warning',  // 待审核\n        2: 'success',  // 已通过\n        3: 'info',     // 已禁用\n        4: 'danger'    // 已驳回\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const textMap = {\n        1: '待审核',\n        2: '已通过',\n        3: '已禁用',\n        4: '已驳回'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取性别文本 */\n    getSexText(sex) {\n      const textMap = {\n        1: '男',\n        2: '女'\n      };\n      return textMap[sex] || '未设置';\n    },\n\n    /** 计算服务时长 */\n    calculateServiceTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * 1.5;\n    },\n\n    /** 计算在线时长 */\n    calculateOnlineTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      const days = Math.floor((Date.now() / 1000 - (this.form.create_time || 0)) / 86400);\n      return Math.max(days * 8, 0);\n    },\n\n    /** 计算业绩 */\n    calculatePerformance() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * (this.form.service_price || 100);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.coach-info-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.coach-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20px;\n}\n\n.coach-avatar {\n  margin-right: 20px;\n}\n\n.coach-avatar img {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n\n.default-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.coach-basic-info {\n  flex: 1;\n}\n\n.coach-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.coach-name {\n  margin: 0;\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.coach-details p {\n  margin: 5px 0;\n  font-size: 14px;\n  color: #666;\n}\n\n.coach-details .label {\n  display: inline-block;\n  width: 60px;\n  color: #999;\n}\n\n.coach-details .value {\n  color: #333;\n  font-weight: 500;\n}\n\n.coach-stats {\n  border-top: 1px solid #f0f0f0;\n  padding-top: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n  background: #fafafa;\n  border-radius: 6px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n</style>\n"], "mappings": ";;;;;;;AA4TA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACA;MACAC,KAAA;QACAC,cAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,OAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,eAAA,GACA;UAAAC,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,eAAA,GACA;UAAAD,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,EAAA,GACA;UAAAF,IAAA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAS,SAAA;QACAhB,OAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAU,WAAA;MACAC,cAAA;MACAC,YAAA;MACAC,kBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACA;MACAC,iBAAA;MACAC,oBAAA;MACAC,uBAAA;MACAC,qBAAA;MACAC,2BAAA;QACAP,OAAA;QACAC,QAAA;QACA1B,KAAA;MACA;MACAiC,iBAAA;QACAjC,KAAA;MACA;MACAkC,gBAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,gBAAA;QACAb,OAAA;QACAC,QAAA;QACAC,OAAA;MACA;IACA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAF,EAAA;IACA,KAAAtC,IAAA,CAAAsC,EAAA,GAAAA,EAAA;IACA,IAAAA,EAAA;MACA,KAAAxC,KAAA;MACA,KAAA2C,OAAA,CAAAH,EAAA;MACA,KAAAhB,kBAAA,CAAAG,OAAA,GAAAa,EAAA;MACA,KAAAF,gBAAA,CAAAX,OAAA,GAAAa,EAAA;MACA,KAAAI,WAAA,CAAAJ,EAAA;MACA,KAAAK,cAAA;MACA,KAAAC,YAAA;IACA;MACA,KAAA9C,KAAA;MACA,KAAA+C,KAAA;IACA;EACA;EACAC,OAAA;IACA,aACAL,OAAA,WAAAA,QAAAH,EAAA;MAAA,IAAAS,KAAA;MACA,IAAAC,eAAA,EAAAV,EAAA,EAAAW,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA/C,IAAA,GAAAkD,QAAA,CAAArD,IAAA;MACA;IACA;IACA;IACAgD,KAAA,WAAAA,MAAA;MACA,KAAA7C,IAAA;QACAsC,EAAA;QACAa,UAAA;QACAC,MAAA;QACAC,GAAA;QACAC,QAAA;QACAC,WAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACAC,YAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACA1D,cAAA;QACAM,OAAA;QACAqD,OAAA;QACAC,QAAA;QACAC,IAAA;QACArD,eAAA;QACAE,eAAA;QACAC,EAAA;MACA;MACA,KAAAmD,SAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtE,IAAA,CAAAsC,EAAA;YACA,IAAAoC,kBAAA,EAAAJ,MAAA,CAAAtE,IAAA,EAAAiD,IAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;UACA;YACA,IAAAC,eAAA,EAAAR,MAAA,CAAAtE,IAAA,EAAAiD,IAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;UACA;QACA;MACA;IACA;IACA,SACAA,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IAEA,aACAvC,WAAA,WAAAA,YAAAJ,EAAA;MACA;MACA,KAAArC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,QAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,aACA8E,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAU,MAAA,CAAAR,MAAA,CAAAC,UAAA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA,gBACAjC,cAAA,WAAAA,eAAA;MAAA,IAAAyC,MAAA;MACA,KAAAhE,cAAA;MACA;MACAiE,UAAA;QACAD,MAAA,CAAAjE,WAAA,IACA;UACAmB,EAAA;UACAxC,KAAA;UACAwF,KAAA;UACAC,SAAA;UACAhC,WAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,GACA;UACArB,EAAA;UACAxC,KAAA;UACAwF,KAAA;UACAC,SAAA;UACAhC,WAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,EACA;QACAyB,MAAA,CAAA/D,YAAA;QACA+D,MAAA,CAAAhE,cAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,gBACAoE,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,KAAA7D,uBAAA;MACA;MACAyD,UAAA;QACAI,MAAA,CAAA9D,oBAAA,IACA;UACAW,EAAA;UACAxC,KAAA;UACAwF,KAAA;UACAC,SAAA;QACA,GACA;UACAjD,EAAA;UACAxC,KAAA;UACAwF,KAAA;UACAC,SAAA;QACA,GACA;UACAjD,EAAA;UACAxC,KAAA;UACAwF,KAAA;UACAC,SAAA;QACA,EACA;QACAE,MAAA,CAAA5D,qBAAA;QACA4D,MAAA,CAAA7D,uBAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,cACA8D,uBAAA,WAAAA,wBAAA;MACA,KAAA5D,2BAAA,CAAAP,OAAA;MACA,KAAAO,2BAAA,CAAAhC,KAAA,QAAAiC,iBAAA,CAAAjC,KAAA;MACA,KAAA0F,uBAAA;IACA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,KAAA5D,iBAAA,CAAAjC,KAAA;MACA,KAAAgC,2BAAA,CAAAhC,KAAA;MACA,KAAA0F,uBAAA;IACA;IAEA,aACAI,4BAAA,WAAAA,6BAAAC,SAAA;MACA,KAAA7D,gBAAA,GAAA6D,SAAA;IACA;IAEA,WACAC,WAAA,WAAAA,YAAA;MACA,SAAA9D,gBAAA,CAAA+D,MAAA;QACA,KAAApB,MAAA,CAAAqB,QAAA;QACA;MACA;MACA;MACA,KAAArB,MAAA,CAAAC,UAAA;MACA,KAAAlD,iBAAA;MACA,KAAAiB,cAAA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,WACAsD,aAAA,WAAAA,cAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,MAAA,CAAAyB,OAAA,eAAAnD,IAAA;QACA;QACAkD,MAAA,CAAAxB,MAAA,CAAAC,UAAA;QACAuB,MAAA,CAAAxD,cAAA;;QAEA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA,eACAC,YAAA,WAAAA,aAAA;MAAA,IAAAyD,MAAA;MACA,KAAAnE,YAAA;MACA;MACAmD,UAAA;QACAgB,MAAA,CAAApE,SAAA,IACA;UACAK,EAAA;UACAgE,UAAA;UACAC,UAAA;UACAC,SAAA;UACAC,YAAA;UACAlD,WAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,GACA;UACArB,EAAA;UACAgE,UAAA;UACAC,UAAA;UACAC,SAAA;UACAC,YAAA;UACAlD,WAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,EACA;QACA0C,MAAA,CAAAlE,UAAA;QACAkE,MAAA,CAAAnE,YAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,eACAwE,kBAAA,WAAAA,mBAAA1C,MAAA;MACA,IAAA2C,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA3C,MAAA;IACA;IAEA,eACA4C,kBAAA,WAAAA,mBAAA5C,MAAA;MACA,IAAA2C,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA3C,MAAA;IACA;IAEA,eACA6C,iBAAA,WAAAA,kBAAA7C,MAAA;MACA,IAAA8C,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAA9C,MAAA;IACA;IAEA,eACA+C,iBAAA,WAAAA,kBAAA/C,MAAA;MACA,IAAAgD,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAhD,MAAA;IACA;IAEA,aACAiD,aAAA,WAAAA,cAAAjD,MAAA;MACA,IAAA8C,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAA9C,MAAA;IACA;IAEA,aACAkD,aAAA,WAAAA,cAAAlD,MAAA;MACA,IAAAgD,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAhD,MAAA;IACA;IAEA,aACAmD,UAAA,WAAAA,WAAA9D,GAAA;MACA,IAAA2D,OAAA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA3D,GAAA;IACA;IAEA,aACA+D,oBAAA,WAAAA,qBAAA;MACA;MACA;MACA,aAAApH,IAAA,CAAA6D,eAAA;IACA;IAEA,aACAwD,mBAAA,WAAAA,oBAAA;MACA;MACA;MACA,IAAAC,IAAA,GAAA9D,IAAA,CAAAC,KAAA,EAAAC,IAAA,CAAAC,GAAA,kBAAA3D,IAAA,CAAAuD,WAAA;MACA,OAAAC,IAAA,CAAA7C,GAAA,CAAA2G,IAAA;IACA;IAEA,WACAC,oBAAA,WAAAA,qBAAA;MACA;MACA;MACA,aAAAvH,IAAA,CAAA6D,eAAA,eAAA7D,IAAA,CAAA8D,aAAA;IACA;EACA;AACA", "ignoreList": []}]}