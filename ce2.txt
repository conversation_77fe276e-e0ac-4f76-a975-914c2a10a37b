C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master>mvn clean install -DskipTests
[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO]
[INFO] ruoyi                                                              [pom]
[INFO] ruoyi-common                                                       [jar]
[INFO] ruoyi-system                                                       [jar]
[INFO] ruoyi-framework                                                    [jar]
[INFO] ruoyi-quartz                                                       [jar]
[INFO] ruoyi-generator                                                    [jar]
[INFO] ruoyi-massage                                                      [jar]
[INFO] ruoyi-admin                                                        [jar]
[INFO]
[INFO] --------------------------< com.ruoyi:ruoyi >---------------------------
[INFO] Building ruoyi 3.9.0                                               [1/8]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi ---
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi\3.9.0\ruoyi-3.9.0.pom
[INFO]
[INFO] -----------------------< com.ruoyi:ruoyi-common >-----------------------
[INFO] Building ruoyi-common 3.9.0                                        [2/8]
[INFO]   from ruoyi-common\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-common ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-common ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\src\main\resources
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-common ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 107 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\target\classes
[WARNING] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-common/src/main/java/com/ruoyi/common/annotation/Excel.java: 某些输入文件使用或覆盖了已过时的 API。
[WARNING] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-common/src/main/java/com/ruoyi/common/annotation/Excel.java: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
[INFO]
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-common ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\src\test\resources
[INFO]
[INFO] --- compiler:3.1:testCompile (default-testCompile) @ ruoyi-common ---
[INFO] No sources to compile
[INFO]
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-common ---
[INFO] Tests are skipped.
[INFO]
[INFO] --- jar:3.4.1:jar (default-jar) @ ruoyi-common ---
[INFO] Building jar: C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\target\ruoyi-common-3.9.0.jar
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi-common ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-common\3.9.0\ruoyi-common-3.9.0.pom
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-common\target\ruoyi-common-3.9.0.jar to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-common\3.9.0\ruoyi-common-3.9.0.jar
[INFO]
[INFO] -----------------------< com.ruoyi:ruoyi-system >-----------------------
[INFO] Building ruoyi-system 3.9.0                                        [3/8]
[INFO]   from ruoyi-system\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-system ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-system ---
[INFO] Copying 15 resources from src\main\resources to target\classes
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-system ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 52 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\target\classes
[INFO]
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-system ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\src\test\resources
[INFO]
[INFO] --- compiler:3.1:testCompile (default-testCompile) @ ruoyi-system ---
[INFO] No sources to compile
[INFO]
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-system ---
[INFO] Tests are skipped.
[INFO]
[INFO] --- jar:3.4.1:jar (default-jar) @ ruoyi-system ---
[INFO] Building jar: C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\target\ruoyi-system-3.9.0.jar
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi-system ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-system\3.9.0\ruoyi-system-3.9.0.pom
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-system\target\ruoyi-system-3.9.0.jar to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-system\3.9.0\ruoyi-system-3.9.0.jar
[INFO]
[INFO] ---------------------< com.ruoyi:ruoyi-framework >----------------------
[INFO] Building ruoyi-framework 3.9.0                                     [4/8]
[INFO]   from ruoyi-framework\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-framework ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-framework ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\src\main\resources
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-framework ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 45 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\target\classes
[INFO]
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-framework ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\src\test\resources
[INFO]
[INFO] --- compiler:3.1:testCompile (default-testCompile) @ ruoyi-framework ---
[INFO] No sources to compile
[INFO]
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-framework ---
[INFO] Tests are skipped.
[INFO]
[INFO] --- jar:3.4.1:jar (default-jar) @ ruoyi-framework ---
[INFO] Building jar: C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\target\ruoyi-framework-3.9.0.jar
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi-framework ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-framework\3.9.0\ruoyi-framework-3.9.0.pom
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-framework\target\ruoyi-framework-3.9.0.jar to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-framework\3.9.0\ruoyi-framework-3.9.0.jar
[INFO]
[INFO] -----------------------< com.ruoyi:ruoyi-quartz >-----------------------
[INFO] Building ruoyi-quartz 3.9.0                                        [5/8]
[INFO]   from ruoyi-quartz\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-quartz ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-quartz ---
[INFO] Copying 2 resources from src\main\resources to target\classes
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-quartz ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 18 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\target\classes
[INFO]
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-quartz ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\src\test\resources
[INFO]
[INFO] --- compiler:3.1:testCompile (default-testCompile) @ ruoyi-quartz ---
[INFO] No sources to compile
[INFO]
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-quartz ---
[INFO] Tests are skipped.
[INFO]
[INFO] --- jar:3.4.1:jar (default-jar) @ ruoyi-quartz ---
[INFO] Building jar: C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\target\ruoyi-quartz-3.9.0.jar
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi-quartz ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-quartz\3.9.0\ruoyi-quartz-3.9.0.pom
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-quartz\target\ruoyi-quartz-3.9.0.jar to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-quartz\3.9.0\ruoyi-quartz-3.9.0.jar
[INFO]
[INFO] ---------------------< com.ruoyi:ruoyi-generator >----------------------
[INFO] Building ruoyi-generator 3.9.0                                     [6/8]
[INFO]   from ruoyi-generator\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-generator ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-generator ---
[INFO] Copying 16 resources from src\main\resources to target\classes
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-generator ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 13 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\target\classes
[INFO]
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-generator ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\src\test\resources
[INFO]
[INFO] --- compiler:3.1:testCompile (default-testCompile) @ ruoyi-generator ---
[INFO] No sources to compile
[INFO]
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-generator ---
[INFO] Tests are skipped.
[INFO]
[INFO] --- jar:3.4.1:jar (default-jar) @ ruoyi-generator ---
[INFO] Building jar: C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\target\ruoyi-generator-3.9.0.jar
[INFO]
[INFO] --- install:3.1.2:install (default-install) @ ruoyi-generator ---
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\pom.xml to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-generator\3.9.0\ruoyi-generator-3.9.0.pom
[INFO] Installing C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-generator\target\ruoyi-generator-3.9.0.jar to C:\Users\<USER>\.m2\repository\com\ruoyi\ruoyi-generator\3.9.0\ruoyi-generator-3.9.0.jar
[INFO]
[INFO] ----------------------< com.ruoyi:ruoyi-massage >-----------------------
[INFO] Building ruoyi-massage 3.9.0                                       [7/8]
[INFO]   from ruoyi-massage\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-massage ---
[INFO] Deleting C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-massage\target
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-massage ---
[INFO] Copying 7 resources from src\main\resources to target\classes
[INFO]
[INFO] --- compiler:3.1:compile (default-compile) @ ruoyi-massage ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 43 source files to C:\Users\<USER>\Desktop\peiwan2\RuoYi-Vue-master\ruoyi-massage\target\classes
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR :
[INFO] -------------------------------------------------------------
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachServiceController.java:[20,33] 找不到符号
  符号:   类 IMassageCoachServiceService
  位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachServiceController.java:[35,13] 找不到符号
  符号:   类 IMassageCoachServiceService
  位置: 类 com.ruoyi.massage.controller.MassageCoachServiceController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachWorkTimeController.java:[16,33] 找不到符号
  符号:   类 IMassageCoachWorkTimeService
  位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachWorkTimeController.java:[29,13] 找不到符号
  符号:   类 IMassageCoachWorkTimeService
  位置: 类 com.ruoyi.massage.controller.MassageCoachWorkTimeController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachOrderController.java:[11,33] 找不到符号
  符号:   类 IMassageCoachOrderService
  位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachOrderController.java:[25,13] 找不到符号
  符号:   类 IMassageCoachOrderService
  位置: 类 com.ruoyi.massage.controller.MassageCoachOrderController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/domain/MassageCoachOrder.java:[130,17] com.ruoyi.massage.domain.MassageCoachOrder中的getCreateTime()无法覆盖com.ruoyi.common.core.domain.BaseEntity中的getCreateTime()
  返回类型java.lang.Long与java.util.Date不兼容
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/domain/MassageCoachService.java:[117,17] com.ruoyi.massage.domain.MassageCoachService中的getCreateTime()无法覆盖com.ruoyi.common.core.domain.BaseEntity中的getCreateTime()
  返回类型java.lang.Long与java.util.Date不兼容
[INFO] 8 errors
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for ruoyi 3.9.0:
[INFO]
[INFO] ruoyi .............................................. SUCCESS [  1.085 s]
[INFO] ruoyi-common ....................................... SUCCESS [ 29.889 s]
[INFO] ruoyi-system ....................................... SUCCESS [  6.911 s]
[INFO] ruoyi-framework .................................... SUCCESS [ 10.827 s]
[INFO] ruoyi-quartz ....................................... SUCCESS [  4.235 s]
[INFO] ruoyi-generator .................................... SUCCESS [  4.808 s]
[INFO] ruoyi-massage ...................................... FAILURE [  5.145 s]
[INFO] ruoyi-admin ........................................ SKIPPED
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:04 min
[INFO] Finished at: 2025-07-30T00:04:42+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.1:compile (default-compile) on project ruoyi-massage: Compilation failure: Compilation failure:
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachServiceController.java:[20,33] 找不到符号
[ERROR]   符号:   类 IMassageCoachServiceService
[ERROR]   位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachServiceController.java:[35,13] 找不到符号
[ERROR]   符号:   类 IMassageCoachServiceService
[ERROR]   位置: 类 com.ruoyi.massage.controller.MassageCoachServiceController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachWorkTimeController.java:[16,33] 找不到符号
[ERROR]   符号:   类 IMassageCoachWorkTimeService
[ERROR]   位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachWorkTimeController.java:[29,13] 找不到符号
[ERROR]   符号:   类 IMassageCoachWorkTimeService
[ERROR]   位置: 类 com.ruoyi.massage.controller.MassageCoachWorkTimeController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachOrderController.java:[11,33] 找不到符号
[ERROR]   符号:   类 IMassageCoachOrderService
[ERROR]   位置: 程序包 com.ruoyi.massage.service
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/controller/MassageCoachOrderController.java:[25,13] 找不到符号
[ERROR]   符号:   类 IMassageCoachOrderService
[ERROR]   位置: 类 com.ruoyi.massage.controller.MassageCoachOrderController
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/domain/MassageCoachOrder.java:[130,17] com.ruoyi.massage.domain.MassageCoachOrder中的getCreateTime()无法覆盖com.ruoyi.common.core.domain.BaseEntity中的getCreateTime()
[ERROR]   返回类型java.lang.Long与java.util.Date不兼容
[ERROR] /C:/Users/<USER>/Desktop/peiwan2/RuoYi-Vue-master/ruoyi-massage/src/main/java/com/ruoyi/massage/domain/MassageCoachService.java:[117,17] com.ruoyi.massage.domain.MassageCoachService中的getCreateTime()无法覆盖com.ruoyi.common.core.domain.BaseEntity中的getCreateTime()
[ERROR]   返回类型java.lang.Long与java.util.Date不兼容
[ERROR] -> [Help 1]
[ERROR]
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR]
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
[ERROR]
[ERROR] After correcting the problems, you can resume the build with the command
[ERROR]   mvn <args> -rf :ruoyi-massage
