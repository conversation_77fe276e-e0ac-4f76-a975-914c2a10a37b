package com.ruoyi.massage.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.massage.domain.MassageOrder;

/**
 * 按摩订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface IMassageOrderService 
{
    /**
     * 查询按摩订单
     * 
     * @param id 按摩订单主键
     * @return 按摩订单
     */
    public MassageOrder selectMassageOrderById(Long id);

    /**
     * 查询按摩订单列表
     * 
     * @param massageOrder 按摩订单
     * @return 按摩订单集合
     */
    public List<MassageOrder> selectMassageOrderList(MassageOrder massageOrder);

    /**
     * 新增按摩订单
     * 
     * @param massageOrder 按摩订单
     * @return 结果
     */
    public int insertMassageOrder(MassageOrder massageOrder);

    /**
     * 修改按摩订单
     * 
     * @param massageOrder 按摩订单
     * @return 结果
     */
    public int updateMassageOrder(MassageOrder massageOrder);

    /**
     * 批量删除按摩订单
     * 
     * @param ids 需要删除的按摩订单主键集合
     * @return 结果
     */
    public int deleteMassageOrderByIds(Long[] ids);

    /**
     * 删除按摩订单信息
     * 
     * @param id 按摩订单主键
     * @return 结果
     */
    public int deleteMassageOrderById(Long id);

    /**
     * 订单派单
     * 
     * @param massageOrder 订单信息（包含技师ID）
     * @return 结果
     */
    public int assignOrder(MassageOrder massageOrder);

    /**
     * 订单退款
     * 
     * @param massageOrder 订单信息（包含退款金额和退款原因）
     * @return 结果
     */
    public int refundOrder(MassageOrder massageOrder);

    /**
     * 完成订单
     * 
     * @param massageOrder 订单信息
     * @return 结果
     */
    public int completeOrder(MassageOrder massageOrder);

    /**
     * 根据订单编号查询订单
     * 
     * @param orderCode 订单编号
     * @return 订单信息
     */
    public MassageOrder selectMassageOrderByCode(String orderCode);

    /**
     * 获取订单统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    public Map<String, Object> getOrderStatistics(String startDate, String endDate);

    /**
     * 获取收入统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型（day/month/year）
     * @return 收入统计
     */
    public Map<String, Object> getIncomeStatistics(String startDate, String endDate, String type);

    /**
     * 获取待处理订单列表
     * 
     * @param massageOrder 查询条件
     * @return 待处理订单列表
     */
    public List<MassageOrder> selectPendingOrders(MassageOrder massageOrder);

    /**
     * 转派技师
     * 
     * @param massageOrder 订单信息（包含新技师ID）
     * @return 结果
     */
    public int changeCoach(MassageOrder massageOrder);

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    public int cancelOrder(Long orderId, String cancelReason);

    /**
     * 获取用户订单列表
     * 
     * @param userId 用户ID
     * @param status 订单状态
     * @return 订单列表
     */
    public List<MassageOrder> selectOrdersByUserId(Long userId, Integer status);

    /**
     * 获取技师订单列表
     * 
     * @param coachId 技师ID
     * @param status 订单状态
     * @return 订单列表
     */
    public List<MassageOrder> selectOrdersByCoachId(Long coachId, Integer status);

    /**
     * 订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param payType 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int paySuccess(String orderCode, Integer payType, String transactionId);

    /**
     * 充值订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int rechargePaySuccess(String orderCode, String transactionId);

    /**
     * 订单退款处理
     *
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @return 结果
     */
    public int processOrderRefund(Long orderId, Double refundAmount);

    /**
     * 升级订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param payType 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int upgradePaySuccess(String orderCode, Integer payType, String transactionId);
}
