{"name": "guzzle/plugin-cache", "description": "Guzzle HTTP cache plugin", "homepage": "http://guzzlephp.org/", "keywords": ["plugin", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/http": "self.version", "guzzle/cache": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Plugin\\Cache": ""}}, "target-dir": "Guzzle/Plugin/Cache", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}