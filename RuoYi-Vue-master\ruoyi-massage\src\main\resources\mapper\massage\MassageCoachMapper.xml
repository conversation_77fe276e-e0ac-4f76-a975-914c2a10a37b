<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageCoachMapper">
    
    <resultMap type="MassageCoach" id="MassageCoachResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="coach_name"    column="coach_name"    />
        <result property="user_id"    column="user_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="id_card"    column="id_card"    />
        <result property="sex"    column="sex"    />
        <result property="work_time"    column="work_time"    />
        <result property="city"    column="city"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="address"    column="address"    />
        <result property="text"    column="text"    />
        <result property="license"    column="license"    />
        <result property="work_img"    column="work_img"    />
        <result property="status"    column="status"    />
        <result property="auth_status"    column="auth_status"    />
        <result property="sh_time"    column="sh_time"    />
        <result property="sh_text"    column="sh_text"    />
        <result property="create_time"    column="create_time"    />
    </resultMap>

    <sql id="selectMassageCoachVo">
        select id, uniacid, coach_name, user_id, mobile, id_card, sex, work_time, city, lng, lat, address, text, license, work_img, status, auth_status, sh_time, sh_text, create_time from ims_massage_service_coach_list
    </sql>

    <select id="selectMassageCoachList" parameterType="MassageCoach" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        <where>  
            <if test="coach_name != null  and coach_name != ''"> and coach_name like concat('%', #{coach_name}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="auth_status != null "> and auth_status = #{auth_status}</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageCoachById" parameterType="Long" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMassageCoach" parameterType="MassageCoach" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_service_coach_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">uniacid,</if>
            <if test="coach_name != null and coach_name != ''">coach_name,</if>
            <if test="user_id != null">user_id,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="id_card != null">id_card,</if>
            <if test="sex != null">sex,</if>
            <if test="work_time != null">work_time,</if>
            <if test="city != null">city,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="address != null">address,</if>
            <if test="text != null">text,</if>
            <if test="license != null">license,</if>
            <if test="work_img != null">work_img,</if>
            <if test="status != null">status,</if>
            <if test="auth_status != null">auth_status,</if>
            <if test="sh_time != null">sh_time,</if>
            <if test="sh_text != null">sh_text,</if>
            <if test="create_time != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">#{uniacid},</if>
            <if test="coach_name != null and coach_name != ''">#{coach_name},</if>
            <if test="user_id != null">#{user_id},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="id_card != null">#{id_card},</if>
            <if test="sex != null">#{sex},</if>
            <if test="work_time != null">#{work_time},</if>
            <if test="city != null">#{city},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="address != null">#{address},</if>
            <if test="text != null">#{text},</if>
            <if test="license != null">#{license},</if>
            <if test="work_img != null">#{work_img},</if>
            <if test="status != null">#{status},</if>
            <if test="auth_status != null">#{auth_status},</if>
            <if test="sh_time != null">#{sh_time},</if>
            <if test="sh_text != null">#{sh_text},</if>
            <if test="create_time != null">#{create_time},</if>
         </trim>
    </insert>

    <update id="updateMassageCoach" parameterType="MassageCoach">
        update ims_massage_service_coach_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="coach_name != null and coach_name != ''">coach_name = #{coach_name},</if>
            <if test="user_id != null">user_id = #{user_id},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="id_card != null">id_card = #{id_card},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="work_time != null">work_time = #{work_time},</if>
            <if test="city != null">city = #{city},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="address != null">address = #{address},</if>
            <if test="text != null">text = #{text},</if>
            <if test="license != null">license = #{license},</if>
            <if test="work_img != null">work_img = #{work_img},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auth_status != null">auth_status = #{auth_status},</if>
            <if test="sh_time != null">sh_time = #{sh_time},</if>
            <if test="sh_text != null">sh_text = #{sh_text},</if>
            <if test="create_time != null">create_time = #{create_time},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageCoachById" parameterType="Long">
        delete from ims_massage_service_coach_list where id = #{id}
    </delete>

    <delete id="deleteMassageCoachByIds" parameterType="String">
        delete from ims_massage_service_coach_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMassageCoachByPhone" parameterType="String" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where mobile = #{phone}
    </select>

    <select id="selectMassageCoachByIdCard" parameterType="String" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where id_card = #{idCard}
    </select>

    <select id="getCoachStatistics" resultType="java.util.Map">
        select
            count(*) as totalCoaches,
            count(case when status = 1 then 1 end) as pendingAudit,
            count(case when status = 2 then 1 end) as normalCoaches,
            count(case when status = 3 then 1 end) as disabledCoaches
        from ims_massage_service_coach_list
    </select>

    <select id="getOnlineCoaches" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where status = 2
        <if test="city != null and city != ''">
            and city = #{city}
        </if>
        order by create_time desc
    </select>

    <select id="getCoachIncomeStats" resultType="java.util.Map">
        select 
            c.id,
            c.coach_name,
            c.mobile,
            coalesce(sum(o.coach_income), 0) as totalIncome,
            count(o.id) as orderCount
        from ims_massage_service_coach_list c
        left join ims_massage_service_order_list o on c.id = o.coach_id 
            and o.pay_type in (2,3,4,5,6,8)
            and date_format(from_unixtime(o.pay_time),'%Y-%m-%d') between #{startDate} and #{endDate}
        where c.status = 2
        group by c.id
        order by totalIncome desc
    </select>

    <select id="getCoachServiceStats" parameterType="Long" resultType="java.util.Map">
        select 
            count(*) as totalOrders,
            coalesce(sum(coach_income), 0) as totalIncome,
            coalesce(avg(star), 0) as avgRating,
            count(case when star >= 4 then 1 end) as goodComments
        from ims_massage_service_order_list
        where coach_id = #{coachId} and pay_type in (2,3,4,5,6,8)
    </select>

    <select id="getTopCoaches" parameterType="Integer" resultType="java.util.Map">
        select
            c.id,
            c.coach_name,
            c.work_img,
            c.status
        from ims_massage_service_coach_list c
        where c.status = 2
        order by c.create_time desc
        limit #{limit}
    </select>

    <select id="getCoachLevelDistribution" resultType="java.util.Map">
        select
            status,
            count(*) as count
        from ims_massage_service_coach_list
        where status = 2
        group by status
        order by status
    </select>

    <select id="getCoachCityDistribution" resultType="java.util.Map">
        select 
            city,
            count(*) as count
        from ims_massage_service_coach_list
        where status = 2 and city is not null and city != ''
        group by city
        order by count desc
        limit 10
    </select>

    <update id="batchAuditCoach">
        update ims_massage_service_coach_list
        set status = #{status}, sh_time = unix_timestamp(), sh_text = #{auditRemark}
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getPendingAuditCount" resultType="Long">
        select count(*) from ims_massage_service_coach_list where status = 1
    </select>

    <select id="getTodayNewCoaches" resultType="Long">
        select count(*) from ims_massage_service_coach_list 
        where date_format(from_unixtime(create_time),'%Y-%m-%d') = curdate()
    </select>

    <select id="getCoachWorkTimeStats" resultType="java.util.Map">
        select 
            c.id,
            c.coach_name,
            count(o.id) as orderCount,
            sum(s.service_duration) as totalWorkMinutes
        from ims_massage_service_coach_list c
        left join ims_massage_service_order_list o on c.id = o.coach_id 
            and o.pay_type in (2,3,4,5,6,8)
            and date_format(from_unixtime(o.pay_time),'%Y-%m-%d') between #{startDate} and #{endDate}
        left join ims_massage_service_service_list s on o.service_id = s.id
        where c.status = 2
        group by c.id
        order by totalWorkMinutes desc
    </select>

</mapper>
