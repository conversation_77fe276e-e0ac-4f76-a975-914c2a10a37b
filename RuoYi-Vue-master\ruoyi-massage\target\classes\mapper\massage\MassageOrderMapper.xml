<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageOrderMapper">
    
    <resultMap type="MassageOrder" id="MassageOrderResult">
        <result property="id"    column="id"    />
        <result property="orderCode"    column="order_code"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="coachId"    column="coach_id"    />
        <result property="coachName"    column="coach_name"    />
        <result property="coachPhone"    column="coach_phone"    />
        <result property="serviceId"    column="service_id"    />
        <result property="serviceName"    column="service_name"    />
        <result property="serviceDuration"    column="service_duration"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="balance"    column="balance"    />
        <result property="coachIncome"    column="coach_income"    />
        <result property="platformIncome"    column="platform_income"    />
        <result property="agentIncome"    column="agent_income"    />
        <result property="status"    column="status"    />
        <result property="payType"    column="pay_type"    />
        <result property="payTime"    column="pay_time"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="appointmentTime"    column="appointment_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="serviceAddress"    column="service_address"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="lat"    column="lat"    />
        <result property="lng"    column="lng"    />
        <result property="remark"    column="remark"    />
        <result property="commentStar"    column="comment_star"    />
        <result property="commentContent"    column="comment_content"    />
        <result property="commentTime"    column="comment_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="uniacid"    column="uniacid"    />
    </resultMap>

    <sql id="selectMassageOrderVo">
        select id, order_code, user_id, user_name, user_phone, coach_id, coach_name, coach_phone, 
               service_id, service_name, service_duration, total_price, pay_price, balance, 
               coach_income, platform_income, agent_income, status, pay_type, pay_time, 
               transaction_id, appointment_time, start_time, end_time, service_address, 
               contact_name, contact_phone, lat, lng, remark, comment_star, comment_content, 
               comment_time, create_time, uniacid 
        from ims_massage_order_list
    </sql>

    <select id="selectMassageOrderList" parameterType="MassageOrder" resultMap="MassageOrderResult">
        <include refid="selectMassageOrderVo"/>
        <where>  
            <if test="orderCode != null  and orderCode != ''"> and order_code = #{orderCode}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and user_phone = #{userPhone}</if>
            <if test="coachId != null "> and coach_id = #{coachId}</if>
            <if test="coachName != null  and coachName != ''"> and coach_name like concat('%', #{coachName}, '%')</if>
            <if test="serviceId != null "> and service_id = #{serviceId}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="payType != null "> and pay_type = #{payType}</if>
            <if test="uniacid != null  and uniacid != ''"> and uniacid = #{uniacid}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageOrderById" parameterType="Long" resultMap="MassageOrderResult">
        <include refid="selectMassageOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectMassageOrderByCode" parameterType="String" resultMap="MassageOrderResult">
        <include refid="selectMassageOrderVo"/>
        where order_code = #{orderCode}
    </select>
        
    <insert id="insertMassageOrder" parameterType="MassageOrder" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_order_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderCode != null">order_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="coachId != null">coach_id,</if>
            <if test="coachName != null">coach_name,</if>
            <if test="coachPhone != null">coach_phone,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="serviceDuration != null">service_duration,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="balance != null">balance,</if>
            <if test="coachIncome != null">coach_income,</if>
            <if test="platformIncome != null">platform_income,</if>
            <if test="agentIncome != null">agent_income,</if>
            <if test="status != null">status,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="appointmentTime != null">appointment_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="serviceAddress != null">service_address,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="remark != null">remark,</if>
            <if test="commentStar != null">comment_star,</if>
            <if test="commentContent != null">comment_content,</if>
            <if test="commentTime != null">comment_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="uniacid != null">uniacid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderCode != null">#{orderCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="coachId != null">#{coachId},</if>
            <if test="coachName != null">#{coachName},</if>
            <if test="coachPhone != null">#{coachPhone},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="serviceDuration != null">#{serviceDuration},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="balance != null">#{balance},</if>
            <if test="coachIncome != null">#{coachIncome},</if>
            <if test="platformIncome != null">#{platformIncome},</if>
            <if test="agentIncome != null">#{agentIncome},</if>
            <if test="status != null">#{status},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="appointmentTime != null">#{appointmentTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="serviceAddress != null">#{serviceAddress},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="remark != null">#{remark},</if>
            <if test="commentStar != null">#{commentStar},</if>
            <if test="commentContent != null">#{commentContent},</if>
            <if test="commentTime != null">#{commentTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="uniacid != null">#{uniacid},</if>
         </trim>
    </insert>

    <update id="updateMassageOrder" parameterType="MassageOrder">
        update ims_massage_order_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderCode != null">order_code = #{orderCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userPhone != null">user_phone = #{userPhone},</if>
            <if test="coachId != null">coach_id = #{coachId},</if>
            <if test="coachName != null">coach_name = #{coachName},</if>
            <if test="coachPhone != null">coach_phone = #{coachPhone},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="serviceDuration != null">service_duration = #{serviceDuration},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="coachIncome != null">coach_income = #{coachIncome},</if>
            <if test="platformIncome != null">platform_income = #{platformIncome},</if>
            <if test="agentIncome != null">agent_income = #{agentIncome},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="appointmentTime != null">appointment_time = #{appointmentTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="serviceAddress != null">service_address = #{serviceAddress},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="commentStar != null">comment_star = #{commentStar},</if>
            <if test="commentContent != null">comment_content = #{commentContent},</if>
            <if test="commentTime != null">comment_time = #{commentTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="uniacid != null">uniacid = #{uniacid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageOrderById" parameterType="Long">
        delete from ims_massage_order_list where id = #{id}
    </delete>

    <delete id="deleteMassageOrderByIds" parameterType="String">
        delete from ims_massage_order_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
