<template>
  <div class="recharge-record-tab">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="充值方式" prop="payMethod">
        <el-select v-model="queryParams.payMethod" placeholder="请选择充值方式" clearable>
          <el-option label="微信支付" value="1" />
          <el-option label="支付宝" value="2" />
          <el-option label="银行卡" value="3" />
          <el-option label="现金" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="充值时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ totalRecharge }}</div>
            <div class="stat-label">总充值金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ rechargeCount }}</div>
            <div class="stat-label">充值次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ avgRecharge }}</div>
            <div class="stat-label">平均充值</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ lastRechargeDate }}</div>
            <div class="stat-label">最近充值</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 充值记录表格 -->
    <el-table v-loading="loading" :data="recordList">
      <el-table-column label="操作者" align="center" prop="control_name" width="120" />
      <el-table-column label="操作记录" align="center" min-width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>
          <span class="ml-md">
            <span :class="[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]">
              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}
            </span>
          </span>
          ，现余额<span class="ml-sm c-success">¥{{ scope.row.after_balance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="text">
        <template slot-scope="scope">
          <div class="ellipsis-2" v-html="scope.row.text"></div>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center" prop="create_time" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getUserBalanceList } from "@/api/massage/user";

export default {
  name: "RechargeRecordTab",
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      recordList: [],
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: this.userId,
        payMethod: null
      },
      totalRecharge: 0,
      rechargeCount: 0,
      avgRecharge: 0,
      lastRechargeDate: '-'
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        id: this.userId
      };
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }

      getUserBalanceList(params).then(response => {
        console.log('余额记录API响应:', response);
        this.recordList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
        this.getStatistics();
      }).catch((error) => {
        console.error('余额记录API错误:', error);
        this.loading = false;
      });
    },
    getStatistics() {
      if (this.recordList.length > 0) {
        // 只统计充值记录（add=1的记录）
        const rechargeRecords = this.recordList.filter(item => item.add === 1);
        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);
        this.rechargeCount = rechargeRecords.length;
        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;

        // 获取最近充值时间
        if (rechargeRecords.length > 0) {
          const latestRecharge = rechargeRecords.reduce((latest, current) => {
            return (current.create_time > latest.create_time) ? current : latest;
          });
          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');
        } else {
          this.lastRechargeDate = '-';
        }
      } else {
        this.totalRecharge = 0;
        this.rechargeCount = 0;
        this.avgRecharge = 0;
        this.lastRechargeDate = '-';
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.recharge-record-tab {
  padding: 20px 0;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}
</style>
