package com.ruoyi.massage.mapper;

import java.util.List;
import com.ruoyi.massage.domain.MassageRechargeOrder;

/**
 * 充值订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface MassageRechargeOrderMapper 
{
    /**
     * 查询充值订单
     * 
     * @param id 充值订单主键
     * @return 充值订单
     */
    public MassageRechargeOrder selectMassageRechargeOrderById(Long id);

    /**
     * 根据订单编号查询充值订单
     * 
     * @param orderCode 订单编号
     * @return 充值订单
     */
    public MassageRechargeOrder selectMassageRechargeOrderByCode(String orderCode);

    /**
     * 查询充值订单列表
     * 
     * @param massageRechargeOrder 充值订单
     * @return 充值订单集合
     */
    public List<MassageRechargeOrder> selectMassageRechargeOrderList(MassageRechargeOrder massageRechargeOrder);

    /**
     * 新增充值订单
     * 
     * @param massageRechargeOrder 充值订单
     * @return 结果
     */
    public int insertMassageRechargeOrder(MassageRechargeOrder massageRechargeOrder);

    /**
     * 修改充值订单
     * 
     * @param massageRechargeOrder 充值订单
     * @return 结果
     */
    public int updateMassageRechargeOrder(MassageRechargeOrder massageRechargeOrder);

    /**
     * 删除充值订单
     * 
     * @param id 充值订单主键
     * @return 结果
     */
    public int deleteMassageRechargeOrderById(Long id);

    /**
     * 批量删除充值订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMassageRechargeOrderByIds(Long[] ids);
}
