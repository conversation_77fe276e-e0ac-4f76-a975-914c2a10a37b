{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue", "mtime": 1753764978961}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_comment", "require", "name", "dicts", "props", "userId", "type", "String", "Number", "required", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "commentList", "title", "detailOpen", "edit<PERSON><PERSON>", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "star", "status", "detailForm", "editForm", "editRules", "message", "trigger", "statistics", "totalComments", "avgStar", "goodComments", "goodRate", "created", "getList", "methods", "_this", "params", "addDateRange", "getUserCommentList", "then", "response", "rows", "getStatistics", "length", "totalStar", "reduce", "sum", "item", "toFixed", "filter", "handleSelectionChange", "selection", "map", "id", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleView", "row", "_objectSpread2", "default", "handleUpdate", "top", "submitEdit", "_this2", "$refs", "validate", "valid", "updateComment", "$modal", "msgSuccess", "refresh"], "sources": ["src/views/massage/user/components/CommentListTab.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"评价星级\" prop=\"star\">\n        <el-select v-model=\"queryParams.star\" placeholder=\"请选择评价星级\" clearable>\n          <el-option label=\"5星\" value=\"5\" />\n          <el-option label=\"4星\" value=\"4\" />\n          <el-option label=\"3星\" value=\"3\" />\n          <el-option label=\"2星\" value=\"2\" />\n          <el-option label=\"1星\" value=\"1\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择评价状态\" clearable>\n          <el-option label=\"正常\" value=\"1\" />\n          <el-option label=\"隐藏\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"mb8\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>总评价数</span>\n            <div class=\"number\">{{ statistics.totalComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>平均星级</span>\n            <div class=\"number\">{{ statistics.avgStar }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评数(4-5星)</span>\n            <div class=\"number\">{{ statistics.goodComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评率</span>\n            <div class=\"number\">{{ statistics.goodRate }}%</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 评价列表 -->\n    <el-table v-loading=\"loading\" :data=\"commentList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"orderCode\" width=\"120\" />\n      <el-table-column label=\"评价星级\" align=\"center\" prop=\"star\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            disabled\n            show-score\n            text-color=\"#ff9900\"\n            score-template=\"{value}星\">\n          </el-rate>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"text\" :show-overflow-tooltip=\"true\" min-width=\"200\" />\n      <el-table-column label=\"评价标签\" align=\"center\" prop=\"lableList\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"tag in scope.row.lableList\"\n            :key=\"tag\"\n            size=\"mini\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"技师\" align=\"center\" prop=\"coachName\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.massage_comment_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:comment:query']\"\n          >详情</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['massage:comment:edit']\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 评价详情对话框 -->\n    <el-dialog title=\"评价详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\n      <el-form ref=\"detailForm\" :model=\"detailForm\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"订单编号\">\n              <span>{{ detailForm.orderCode }}</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"技师\">\n              <span>{{ detailForm.coachName }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价星级\">\n              <el-rate\n                v-model=\"detailForm.star\"\n                disabled\n                show-score\n                text-color=\"#ff9900\"\n                score-template=\"{value}星\">\n              </el-rate>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价时间\">\n              <span>{{ parseTime(detailForm.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"评价内容\">\n          <span>{{ detailForm.text }}</span>\n        </el-form-item>\n        <el-form-item label=\"评价标签\" v-if=\"detailForm.lableList && detailForm.lableList.length > 0\">\n          <el-tag\n            v-for=\"tag in detailForm.lableList\"\n            :key=\"tag\"\n            size=\"small\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"服务评价\" v-if=\"detailForm.serviceList && detailForm.serviceList.length > 0\">\n          <div v-for=\"service in detailForm.serviceList\" :key=\"service.serviceId\" style=\"margin-bottom: 10px;\">\n            <span>{{ service.serviceName }}：</span>\n            <el-rate\n              v-model=\"service.star\"\n              disabled\n              show-score\n              text-color=\"#ff9900\"\n              score-template=\"{value}星\">\n            </el-rate>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 编辑评价对话框 -->\n    <el-dialog title=\"编辑评价\" :visible.sync=\"editOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"editForm\" :model=\"editForm\" :rules=\"editRules\" label-width=\"80px\">\n        <el-form-item label=\"评价状态\" prop=\"status\">\n          <el-radio-group v-model=\"editForm.status\">\n            <el-radio :label=\"1\">正常</el-radio>\n            <el-radio :label=\"0\">隐藏</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"是否置顶\" prop=\"top\">\n          <el-radio-group v-model=\"editForm.top\">\n            <el-radio :label=\"1\">是</el-radio>\n            <el-radio :label=\"0\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitEdit\">确 定</el-button>\n        <el-button @click=\"editOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUserCommentList, updateComment } from \"@/api/massage/comment\";\n\nexport default {\n  name: \"CommentListTab\",\n  dicts: ['massage_comment_status'],\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 评价表格数据\n      commentList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      detailOpen: false,\n      editOpen: false,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        star: null,\n        status: null\n      },\n      // 表单参数\n      detailForm: {},\n      editForm: {},\n      // 表单校验\n      editRules: {\n        status: [\n          { required: true, message: \"评价状态不能为空\", trigger: \"change\" }\n        ]\n      },\n      // 统计信息\n      statistics: {\n        totalComments: 0,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询评价列表 */\n    getList() {\n      this.loading = true;\n      const params = this.addDateRange(this.queryParams, this.dateRange);\n      getUserCommentList(params).then(response => {\n        this.commentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n        this.getStatistics();\n      });\n    },\n    /** 计算统计信息 */\n    getStatistics() {\n      this.statistics = {\n        totalComments: this.commentList.length,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      };\n      \n      if (this.commentList.length > 0) {\n        const totalStar = this.commentList.reduce((sum, item) => sum + (item.star || 0), 0);\n        this.statistics.avgStar = (totalStar / this.commentList.length).toFixed(1);\n        this.statistics.goodComments = this.commentList.filter(item => item.star >= 4).length;\n        this.statistics.goodRate = ((this.statistics.goodComments / this.commentList.length) * 100).toFixed(1);\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 查看评价详情 */\n    handleView(row) {\n      this.detailForm = { ...row };\n      this.detailOpen = true;\n    },\n    /** 编辑评价 */\n    handleUpdate(row) {\n      this.editForm = {\n        id: row.id,\n        status: row.status,\n        top: row.top\n      };\n      this.editOpen = true;\n    },\n    /** 提交编辑 */\n    submitEdit() {\n      this.$refs[\"editForm\"].validate(valid => {\n        if (valid) {\n          updateComment(this.editForm).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.editOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  text-align: center;\n}\n.text.item {\n  padding: 10px;\n}\n.number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-top: 5px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAqOA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAnB,MAAA,OAAAA,MAAA;QACAoB,IAAA;QACAC,MAAA;MACA;MACA;MACAC,UAAA;MACAC,QAAA;MACA;MACAC,SAAA;QACAH,MAAA,GACA;UAAAjB,QAAA;UAAAqB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,UAAA;QACAC,aAAA;QACAC,OAAA;QACAC,YAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,MAAA,QAAAC,YAAA,MAAApB,WAAA,OAAAD,SAAA;MACA,IAAAsB,2BAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAvB,WAAA,GAAA4B,QAAA,CAAAC,IAAA;QACAN,KAAA,CAAAxB,KAAA,GAAA6B,QAAA,CAAA7B,KAAA;QACAwB,KAAA,CAAA7B,OAAA;QACA6B,KAAA,CAAAO,aAAA;MACA;IACA;IACA,aACAA,aAAA,WAAAA,cAAA;MACA,KAAAf,UAAA;QACAC,aAAA,OAAAhB,WAAA,CAAA+B,MAAA;QACAd,OAAA;QACAC,YAAA;QACAC,QAAA;MACA;MAEA,SAAAnB,WAAA,CAAA+B,MAAA;QACA,IAAAC,SAAA,QAAAhC,WAAA,CAAAiC,MAAA,WAAAC,GAAA,EAAAC,IAAA;UAAA,OAAAD,GAAA,IAAAC,IAAA,CAAA3B,IAAA;QAAA;QACA,KAAAO,UAAA,CAAAE,OAAA,IAAAe,SAAA,QAAAhC,WAAA,CAAA+B,MAAA,EAAAK,OAAA;QACA,KAAArB,UAAA,CAAAG,YAAA,QAAAlB,WAAA,CAAAqC,MAAA,WAAAF,IAAA;UAAA,OAAAA,IAAA,CAAA3B,IAAA;QAAA,GAAAuB,MAAA;QACA,KAAAhB,UAAA,CAAAI,QAAA,SAAAJ,UAAA,CAAAG,YAAA,QAAAlB,WAAA,CAAA+B,MAAA,QAAAK,OAAA;MACA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5C,GAAA,GAAA4C,SAAA,CAAAC,GAAA,WAAAL,IAAA;QAAA,OAAAA,IAAA,CAAAM,EAAA;MAAA;MACA,KAAA7C,MAAA,GAAA2C,SAAA,CAAAR,MAAA;MACA,KAAAlC,QAAA,IAAA0C,SAAA,CAAAR,MAAA;IACA;IACA,aACAW,WAAA,WAAAA,YAAA;MACA,KAAArC,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAsB,UAAA,WAAAA,WAAA;MACA,KAAAvC,SAAA;MACA,KAAAwC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,aACAG,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAApC,UAAA,OAAAqC,cAAA,CAAAC,OAAA,MAAAF,GAAA;MACA,KAAA5C,UAAA;IACA;IACA,WACA+C,YAAA,WAAAA,aAAAH,GAAA;MACA,KAAAnC,QAAA;QACA8B,EAAA,EAAAK,GAAA,CAAAL,EAAA;QACAhC,MAAA,EAAAqC,GAAA,CAAArC,MAAA;QACAyC,GAAA,EAAAJ,GAAA,CAAAI;MACA;MACA,KAAA/C,QAAA;IACA;IACA,WACAgD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,sBAAA,EAAAJ,MAAA,CAAAzC,QAAA,EAAAgB,IAAA,WAAAC,QAAA;YACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;YACAN,MAAA,CAAAjD,QAAA;YACAiD,MAAA,CAAA/B,OAAA;UACA;QACA;MACA;IACA;IACA;IACAsC,OAAA,WAAAA,QAAA;MACA,KAAAtC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}