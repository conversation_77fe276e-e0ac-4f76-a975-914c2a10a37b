<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageGrowthRecordMapper">
    
    <resultMap type="MassageGrowthRecord" id="MassageGrowthRecordResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="userId"    column="user_id"    />
        <result property="growth"    column="growth"    />
        <result property="isAdd"    column="is_add"    />
        <result property="beforeGrowth"    column="before_growth"    />
        <result property="afterGrowth"    column="after_growth"    />
        <result property="type"    column="type"    />
        <result property="orderId"    column="order_id"    />
        <result property="createTimestamp"    column="create_time"    />
    </resultMap>

    <sql id="selectMassageGrowthRecordVo">
        select id, uniacid, user_id, growth, is_add, before_growth, after_growth, type, order_id, create_time from ims_massage_member_growth
    </sql>

    <select id="selectMassageGrowthRecordList" parameterType="MassageGrowthRecord" resultMap="MassageGrowthRecordResult">
        <include refid="selectMassageGrowthRecordVo"/>
        <where>
            <if test="uniacid != null "> and uniacid = #{uniacid}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="growth != null "> and growth = #{growth}</if>
            <if test="isAdd != null "> and is_add = #{isAdd}</if>
            <if test="beforeGrowth != null "> and before_growth = #{beforeGrowth}</if>
            <if test="afterGrowth != null "> and after_growth = #{afterGrowth}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageGrowthRecordById" parameterType="Long" resultMap="MassageGrowthRecordResult">
        <include refid="selectMassageGrowthRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMassageGrowthRecord" parameterType="MassageGrowthRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_member_growth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">uniacid,</if>
            <if test="userId != null">user_id,</if>
            <if test="growth != null">growth,</if>
            <if test="isAdd != null">is_add,</if>
            <if test="beforeGrowth != null">before_growth,</if>
            <if test="afterGrowth != null">after_growth,</if>
            <if test="type != null">type,</if>
            <if test="orderId != null">order_id,</if>
            <if test="createTimestamp != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">#{uniacid},</if>
            <if test="userId != null">#{userId},</if>
            <if test="growth != null">#{growth},</if>
            <if test="isAdd != null">#{isAdd},</if>
            <if test="beforeGrowth != null">#{beforeGrowth},</if>
            <if test="afterGrowth != null">#{afterGrowth},</if>
            <if test="type != null">#{type},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="createTimestamp != null">#{createTimestamp},</if>
         </trim>
    </insert>

    <update id="updateMassageGrowthRecord" parameterType="MassageGrowthRecord">
        update ims_massage_member_growth
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="growth != null">growth = #{growth},</if>
            <if test="isAdd != null">is_add = #{isAdd},</if>
            <if test="beforeGrowth != null">before_growth = #{beforeGrowth},</if>
            <if test="afterGrowth != null">after_growth = #{afterGrowth},</if>
            <if test="type != null">type = #{type},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="createTimestamp != null">create_time = #{createTimestamp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageGrowthRecordById" parameterType="Long">
        delete from ims_massage_member_growth where id = #{id}
    </delete>

    <delete id="deleteMassageGrowthRecordByIds" parameterType="String">
        delete from ims_massage_member_growth where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取用户成长值记录列表（用于明细显示） -->
    <select id="selectUserGrowthRecordList" parameterType="Long" resultType="java.util.Map">
        select
            r.id,
            '系统' as create_user,
            CASE
                WHEN r.type = 1 THEN '消费获得成长值'
                WHEN r.type = 2 THEN '管理员调整'
                WHEN r.type = 3 THEN '系统扣除成长值'
                ELSE '未知操作'
            END as type_text,
            r.is_add,
            r.growth,
            r.after_growth,
            UNIX_TIMESTAMP(r.create_time) as create_time
        from ims_massage_member_growth r
        where r.user_id = #{userId}
        order by r.create_time desc
    </select>
</mapper>
