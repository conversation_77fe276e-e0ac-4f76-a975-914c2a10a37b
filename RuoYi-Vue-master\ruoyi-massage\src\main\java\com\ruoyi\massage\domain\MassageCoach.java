package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 达人对象 ims_massage_service_coach_list
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageCoach extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 达人ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 达人姓名 */
    @Excel(name = "达人姓名")
    private String coach_name;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long user_id;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String id_card;

    /** 性别 1男 2女 */
    @Excel(name = "性别", readConverterExp = "1=男,2=女")
    private Integer sex;



    /** 从业时间(年) */
    @Excel(name = "从业时间")
    private Integer work_time;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 个人简介 */
    @Excel(name = "个人简介")
    private String text;

    /** 执照 */
    @Excel(name = "执照")
    private String license;

    /** 工作照片 */
    @Excel(name = "工作照片")
    private String work_img;

    /** 达人状态 1待审核 2正常 3禁用 4审核失败 */
    @Excel(name = "达人状态", readConverterExp = "1=待审核,2=正常,3=禁用,4=审核失败")
    private Integer status;

    /** 认证状态 0未认证 1待审核 2已认证 3认证失败 */
    @Excel(name = "认证状态", readConverterExp = "0=未认证,1=待审核,2=已认证,3=认证失败")
    private Integer auth_status;

    /** 审核时间 */
    @Excel(name = "审核时间")
    private Long sh_time;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String sh_text;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long create_time;

    /** 个人照片 */
    @Excel(name = "个人照片")
    private String self_img;

    /** 是否工作 1工作 0不工作 */
    @Excel(name = "是否工作", readConverterExp = "1=工作,0=不工作")
    private Integer is_work;

    /** 开始工作时间 */
    @Excel(name = "开始工作时间")
    private String start_time;

    /** 结束工作时间 */
    @Excel(name = "结束工作时间")
    private String end_time;

    /** 服务价格 */
    @Excel(name = "服务价格")
    private Double service_price;

    /** 车费 */
    @Excel(name = "车费")
    private Double car_price;

    /** 身份证编码 */
    @Excel(name = "身份证编码")
    private String id_code;

    /** 评分 */
    @Excel(name = "评分")
    private Double star;

    /** 管理员添加 */
    @Excel(name = "管理员添加")
    private Integer admin_add;

    /** 城市ID */
    @Excel(name = "城市ID")
    private Integer city_id;

    /** 视频 */
    @Excel(name = "视频")
    private String video;

    /** 是否更新 */
    @Excel(name = "是否更新")
    private Integer is_update;

    /** 积分 */
    @Excel(name = "积分")
    private Double integral;

    /** 虚拟订单数量 */
    @Excel(name = "虚拟订单数量")
    private Integer order_num;

    /** 是否推荐技师 */
    @Excel(name = "是否推荐技师", readConverterExp = "0=否,1=是")
    private Integer recommend;

    /** 邀请充值余额返回佣金 */
    @Excel(name = "邀请充值余额返回佣金")
    private Double balance_cash;

    /** 虚拟排序 */
    @Excel(name = "虚拟排序")
    private Integer index_top;

    /** 实时定位 */
    @Excel(name = "实时定位")
    private Integer coach_position;

    /** 附近时间 */
    @Excel(name = "附近时间")
    private Long near_time;

    /** 检查现金 */
    @Excel(name = "检查现金")
    private Double check_cash;

    /** 代理类型 1代理商 2合伙人 */
    @Excel(name = "代理类型", readConverterExp = "1=代理商,2=合伙人")
    private Integer agent_type;

    /** 合伙人ID */
    @Excel(name = "合伙人ID")
    private Integer partner_id;

    /** 合伙人绑定时间 */
    @Excel(name = "合伙人绑定时间")
    private Long partner_time;

    /** 总订单量 */
    @Excel(name = "总订单量")
    private Integer total_order_num;

    /** 生日 */
    @Excel(name = "生日")
    private Long birthday;

    /** 信用分 */
    @Excel(name = "信用分")
    private Double credit_value;

    /** 信用分排序 */
    @Excel(name = "信用分排序")
    private Integer credit_top;

    /** 推荐挂件 */
    @Excel(name = "推荐挂件")
    private Integer recommend_icon;

    /** 免车费距离 */
    @Excel(name = "免车费距离")
    private Double free_fare_distance;

    /** 是否显示销量 */
    @Excel(name = "是否显示销量", readConverterExp = "0=否,1=是")
    private Integer show_salenum;

    /** 技师图标ID */
    @Excel(name = "技师图标ID")
    private Integer coach_icon;

    /** 真实经度 */
    @Excel(name = "真实经度")
    private String true_lng;

    /** 真实纬度 */
    @Excel(name = "真实纬度")
    private String true_lat;

    /** 技师类型 */
    @Excel(name = "技师类型")
    private Integer type_id;

    /** 版本号 */
    @Excel(name = "版本号")
    private Integer version;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String true_user_name;

    /** 行业类型 */
    @Excel(name = "行业类型")
    private Integer industry_type;

    /** 身高 */
    @Excel(name = "身高")
    private String height;

    /** 体重 */
    @Excel(name = "体重")
    private String weight;

    /** 星座 */
    @Excel(name = "星座")
    private String constellation;

    /** 车费免费承担方 */
    @Excel(name = "车费免费承担方", readConverterExp = "0=不开启,1=平台,2=技师")
    private Integer free_fare_bear;

    /** 个性标签 */
    @Excel(name = "个性标签")
    private Integer personality_icon;

    /** 岗位标签 */
    @Excel(name = "岗位标签")
    private Integer station_icon;

    /** 地址更新时间 */
    @Excel(name = "地址更新时间")
    private Long address_update_time;

    /** 访问量 */
    @Excel(name = "访问量")
    private Integer pv;

    /** 虚拟收藏量 */
    @Excel(name = "虚拟收藏量")
    private Integer virtual_collect;

    /** 虚拟评论量 */
    @Excel(name = "虚拟评论量")
    private Integer virtual_comment;

    /** 工作类型 1可服务 2服务中 3可预约 */
    @Excel(name = "工作类型", readConverterExp = "1=可服务,2=服务中,3=可预约")
    private Integer work_type;

    /** 数据 */
    @Excel(name = "数据")
    private String data;



    // Getter and Setter methods
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }

    public void setCoach_name(String coach_name) 
    {
        this.coach_name = coach_name;
    }

    public String getCoach_name() 
    {
        return coach_name;
    }

    public void setUser_id(Long user_id) 
    {
        this.user_id = user_id;
    }

    public Long getUser_id() 
    {
        return user_id;
    }

    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }

    public void setId_card(String id_card) 
    {
        this.id_card = id_card;
    }

    public String getId_card() 
    {
        return id_card;
    }

    public void setSex(Integer sex) 
    {
        this.sex = sex;
    }

    public Integer getSex() 
    {
        return sex;
    }



    public void setWork_time(Integer work_time) 
    {
        this.work_time = work_time;
    }

    public Integer getWork_time() 
    {
        return work_time;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setText(String text) 
    {
        this.text = text;
    }

    public String getText() 
    {
        return text;
    }

    public void setLicense(String license) 
    {
        this.license = license;
    }

    public String getLicense() 
    {
        return license;
    }

    public void setWork_img(String work_img) 
    {
        this.work_img = work_img;
    }

    public String getWork_img() 
    {
        return work_img;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setAuth_status(Integer auth_status) 
    {
        this.auth_status = auth_status;
    }

    public Integer getAuth_status() 
    {
        return auth_status;
    }

    public void setSh_time(Long sh_time) 
    {
        this.sh_time = sh_time;
    }

    public Long getSh_time() 
    {
        return sh_time;
    }

    public void setSh_text(String sh_text) 
    {
        this.sh_text = sh_text;
    }

    public String getSh_text() 
    {
        return sh_text;
    }

    public void setCreate_time(Long create_time) 
    {
        this.create_time = create_time;
    }

    public Long getCreate_time()
    {
        return create_time;
    }

    public void setSelf_img(String self_img)
    {
        this.self_img = self_img;
    }

    public String getSelf_img()
    {
        return self_img;
    }

    public void setIs_work(Integer is_work)
    {
        this.is_work = is_work;
    }

    public Integer getIs_work()
    {
        return is_work;
    }

    public void setStart_time(String start_time)
    {
        this.start_time = start_time;
    }

    public String getStart_time()
    {
        return start_time;
    }

    public void setEnd_time(String end_time)
    {
        this.end_time = end_time;
    }

    public String getEnd_time()
    {
        return end_time;
    }

    public void setService_price(Double service_price)
    {
        this.service_price = service_price;
    }

    public Double getService_price()
    {
        return service_price;
    }

    public void setCar_price(Double car_price)
    {
        this.car_price = car_price;
    }

    public Double getCar_price()
    {
        return car_price;
    }

    public void setId_code(String id_code)
    {
        this.id_code = id_code;
    }

    public String getId_code()
    {
        return id_code;
    }

    public void setStar(Double star)
    {
        this.star = star;
    }

    public Double getStar()
    {
        return star;
    }

    public void setAdmin_add(Integer admin_add)
    {
        this.admin_add = admin_add;
    }

    public Integer getAdmin_add()
    {
        return admin_add;
    }

    public void setCity_id(Integer city_id)
    {
        this.city_id = city_id;
    }

    public Integer getCity_id()
    {
        return city_id;
    }

    public void setVideo(String video)
    {
        this.video = video;
    }

    public String getVideo()
    {
        return video;
    }

    public void setIs_update(Integer is_update)
    {
        this.is_update = is_update;
    }

    public Integer getIs_update()
    {
        return is_update;
    }

    public void setIntegral(Double integral)
    {
        this.integral = integral;
    }

    public Double getIntegral()
    {
        return integral;
    }

    public void setOrder_num(Integer order_num)
    {
        this.order_num = order_num;
    }

    public Integer getOrder_num()
    {
        return order_num;
    }

    public void setRecommend(Integer recommend)
    {
        this.recommend = recommend;
    }

    public Integer getRecommend()
    {
        return recommend;
    }

    public void setBalance_cash(Double balance_cash)
    {
        this.balance_cash = balance_cash;
    }

    public Double getBalance_cash()
    {
        return balance_cash;
    }

    public void setIndex_top(Integer index_top)
    {
        this.index_top = index_top;
    }

    public Integer getIndex_top()
    {
        return index_top;
    }

    public void setCoach_position(Integer coach_position)
    {
        this.coach_position = coach_position;
    }

    public Integer getCoach_position()
    {
        return coach_position;
    }

    public void setNear_time(Long near_time)
    {
        this.near_time = near_time;
    }

    public Long getNear_time()
    {
        return near_time;
    }

    public void setCheck_cash(Double check_cash)
    {
        this.check_cash = check_cash;
    }

    public Double getCheck_cash()
    {
        return check_cash;
    }

    public void setAgent_type(Integer agent_type)
    {
        this.agent_type = agent_type;
    }

    public Integer getAgent_type()
    {
        return agent_type;
    }

    public void setPartner_id(Integer partner_id)
    {
        this.partner_id = partner_id;
    }

    public Integer getPartner_id()
    {
        return partner_id;
    }

    public void setPartner_time(Long partner_time)
    {
        this.partner_time = partner_time;
    }

    public Long getPartner_time()
    {
        return partner_time;
    }

    public void setTotal_order_num(Integer total_order_num)
    {
        this.total_order_num = total_order_num;
    }

    public Integer getTotal_order_num()
    {
        return total_order_num;
    }

    public void setBirthday(Long birthday)
    {
        this.birthday = birthday;
    }

    public Long getBirthday()
    {
        return birthday;
    }

    public void setCredit_value(Double credit_value)
    {
        this.credit_value = credit_value;
    }

    public Double getCredit_value()
    {
        return credit_value;
    }

    public void setCredit_top(Integer credit_top)
    {
        this.credit_top = credit_top;
    }

    public Integer getCredit_top()
    {
        return credit_top;
    }

    public void setRecommend_icon(Integer recommend_icon)
    {
        this.recommend_icon = recommend_icon;
    }

    public Integer getRecommend_icon()
    {
        return recommend_icon;
    }

    public void setFree_fare_distance(Double free_fare_distance)
    {
        this.free_fare_distance = free_fare_distance;
    }

    public Double getFree_fare_distance()
    {
        return free_fare_distance;
    }

    public void setShow_salenum(Integer show_salenum)
    {
        this.show_salenum = show_salenum;
    }

    public Integer getShow_salenum()
    {
        return show_salenum;
    }

    public void setCoach_icon(Integer coach_icon)
    {
        this.coach_icon = coach_icon;
    }

    public Integer getCoach_icon()
    {
        return coach_icon;
    }

    public void setTrue_lng(String true_lng)
    {
        this.true_lng = true_lng;
    }

    public String getTrue_lng()
    {
        return true_lng;
    }

    public void setTrue_lat(String true_lat)
    {
        this.true_lat = true_lat;
    }

    public String getTrue_lat()
    {
        return true_lat;
    }

    public void setType_id(Integer type_id)
    {
        this.type_id = type_id;
    }

    public Integer getType_id()
    {
        return type_id;
    }

    public void setVersion(Integer version)
    {
        this.version = version;
    }

    public Integer getVersion()
    {
        return version;
    }

    public void setTrue_user_name(String true_user_name)
    {
        this.true_user_name = true_user_name;
    }

    public String getTrue_user_name()
    {
        return true_user_name;
    }

    public void setIndustry_type(Integer industry_type)
    {
        this.industry_type = industry_type;
    }

    public Integer getIndustry_type()
    {
        return industry_type;
    }

    public void setHeight(String height)
    {
        this.height = height;
    }

    public String getHeight()
    {
        return height;
    }

    public void setWeight(String weight)
    {
        this.weight = weight;
    }

    public String getWeight()
    {
        return weight;
    }

    public void setConstellation(String constellation)
    {
        this.constellation = constellation;
    }

    public String getConstellation()
    {
        return constellation;
    }

    public void setFree_fare_bear(Integer free_fare_bear)
    {
        this.free_fare_bear = free_fare_bear;
    }

    public Integer getFree_fare_bear()
    {
        return free_fare_bear;
    }

    public void setPersonality_icon(Integer personality_icon)
    {
        this.personality_icon = personality_icon;
    }

    public Integer getPersonality_icon()
    {
        return personality_icon;
    }

    public void setStation_icon(Integer station_icon)
    {
        this.station_icon = station_icon;
    }

    public Integer getStation_icon()
    {
        return station_icon;
    }

    public void setAddress_update_time(Long address_update_time)
    {
        this.address_update_time = address_update_time;
    }

    public Long getAddress_update_time()
    {
        return address_update_time;
    }

    public void setPv(Integer pv)
    {
        this.pv = pv;
    }

    public Integer getPv()
    {
        return pv;
    }

    public void setVirtual_collect(Integer virtual_collect)
    {
        this.virtual_collect = virtual_collect;
    }

    public Integer getVirtual_collect()
    {
        return virtual_collect;
    }

    public void setVirtual_comment(Integer virtual_comment)
    {
        this.virtual_comment = virtual_comment;
    }

    public Integer getVirtual_comment()
    {
        return virtual_comment;
    }

    public void setWork_type(Integer work_type)
    {
        this.work_type = work_type;
    }

    public Integer getWork_type()
    {
        return work_type;
    }

    public void setData(String data)
    {
        this.data = data;
    }

    public String getData()
    {
        return data;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("coach_name", getCoach_name())
            .append("user_id", getUser_id())
            .append("mobile", getMobile())
            .append("id_card", getId_card())
            .append("sex", getSex())
            .append("work_time", getWork_time())
            .append("city", getCity())
            .append("lng", getLng())
            .append("lat", getLat())
            .append("address", getAddress())
            .append("text", getText())
            .append("license", getLicense())
            .append("work_img", getWork_img())
            .append("status", getStatus())
            .append("auth_status", getAuth_status())
            .append("sh_time", getSh_time())
            .append("sh_text", getSh_text())
            .append("create_time", getCreate_time())
            .append("self_img", getSelf_img())
            .append("is_work", getIs_work())
            .append("start_time", getStart_time())
            .append("end_time", getEnd_time())
            .append("service_price", getService_price())
            .append("car_price", getCar_price())
            .append("id_code", getId_code())
            .append("star", getStar())
            .append("admin_add", getAdmin_add())
            .append("city_id", getCity_id())
            .append("video", getVideo())
            .append("is_update", getIs_update())
            .append("integral", getIntegral())
            .append("order_num", getOrder_num())
            .append("recommend", getRecommend())
            .append("balance_cash", getBalance_cash())
            .append("index_top", getIndex_top())
            .append("coach_position", getCoach_position())
            .append("near_time", getNear_time())
            .append("check_cash", getCheck_cash())
            .append("agent_type", getAgent_type())
            .append("partner_id", getPartner_id())
            .append("partner_time", getPartner_time())
            .append("total_order_num", getTotal_order_num())
            .append("birthday", getBirthday())
            .append("credit_value", getCredit_value())
            .append("credit_top", getCredit_top())
            .append("recommend_icon", getRecommend_icon())
            .append("free_fare_distance", getFree_fare_distance())
            .append("show_salenum", getShow_salenum())
            .append("coach_icon", getCoach_icon())
            .append("true_lng", getTrue_lng())
            .append("true_lat", getTrue_lat())
            .append("type_id", getType_id())
            .append("version", getVersion())
            .append("true_user_name", getTrue_user_name())
            .append("industry_type", getIndustry_type())
            .append("height", getHeight())
            .append("weight", getWeight())
            .append("constellation", getConstellation())
            .append("free_fare_bear", getFree_fare_bear())
            .append("personality_icon", getPersonality_icon())
            .append("station_icon", getStation_icon())
            .append("address_update_time", getAddress_update_time())
            .append("pv", getPv())
            .append("virtual_collect", getVirtual_collect())
            .append("virtual_comment", getVirtual_comment())
            .append("work_type", getWork_type())
            .append("data", getData())
            .toString();
    }
}
