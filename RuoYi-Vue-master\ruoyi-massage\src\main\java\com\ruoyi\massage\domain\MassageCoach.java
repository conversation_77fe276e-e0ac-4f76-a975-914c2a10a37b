package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 达人对象 ims_massage_service_coach_list
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageCoach extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 达人ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 达人姓名 */
    @Excel(name = "达人姓名")
    private String coach_name;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long user_id;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String id_card;

    /** 性别 1男 2女 */
    @Excel(name = "性别", readConverterExp = "1=男,2=女")
    private Integer sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private Integer age;

    /** 从业时间(年) */
    @Excel(name = "从业时间")
    private Integer work_time;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 个人简介 */
    @Excel(name = "个人简介")
    private String text;

    /** 执照 */
    @Excel(name = "执照")
    private String license;

    /** 工作照片 */
    @Excel(name = "工作照片")
    private String work_img;

    /** 达人状态 1待审核 2正常 3禁用 4审核失败 */
    @Excel(name = "达人状态", readConverterExp = "1=待审核,2=正常,3=禁用,4=审核失败")
    private Integer status;

    /** 认证状态 0未认证 1待审核 2已认证 3认证失败 */
    @Excel(name = "认证状态", readConverterExp = "0=未认证,1=待审核,2=已认证,3=认证失败")
    private Integer auth_status;

    /** 审核时间 */
    @Excel(name = "审核时间")
    private Long sh_time;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String sh_text;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long create_time;

    // Getter and Setter methods
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }

    public void setCoach_name(String coach_name) 
    {
        this.coach_name = coach_name;
    }

    public String getCoach_name() 
    {
        return coach_name;
    }

    public void setUser_id(Long user_id) 
    {
        this.user_id = user_id;
    }

    public Long getUser_id() 
    {
        return user_id;
    }

    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }

    public void setId_card(String id_card) 
    {
        this.id_card = id_card;
    }

    public String getId_card() 
    {
        return id_card;
    }

    public void setSex(Integer sex) 
    {
        this.sex = sex;
    }

    public Integer getSex() 
    {
        return sex;
    }

    public void setAge(Integer age) 
    {
        this.age = age;
    }

    public Integer getAge() 
    {
        return age;
    }

    public void setWork_time(Integer work_time) 
    {
        this.work_time = work_time;
    }

    public Integer getWork_time() 
    {
        return work_time;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setText(String text) 
    {
        this.text = text;
    }

    public String getText() 
    {
        return text;
    }

    public void setLicense(String license) 
    {
        this.license = license;
    }

    public String getLicense() 
    {
        return license;
    }

    public void setWork_img(String work_img) 
    {
        this.work_img = work_img;
    }

    public String getWork_img() 
    {
        return work_img;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setAuth_status(Integer auth_status) 
    {
        this.auth_status = auth_status;
    }

    public Integer getAuth_status() 
    {
        return auth_status;
    }

    public void setSh_time(Long sh_time) 
    {
        this.sh_time = sh_time;
    }

    public Long getSh_time() 
    {
        return sh_time;
    }

    public void setSh_text(String sh_text) 
    {
        this.sh_text = sh_text;
    }

    public String getSh_text() 
    {
        return sh_text;
    }

    public void setCreate_time(Long create_time) 
    {
        this.create_time = create_time;
    }

    public Long getCreate_time() 
    {
        return create_time;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("coach_name", getCoach_name())
            .append("user_id", getUser_id())
            .append("mobile", getMobile())
            .append("id_card", getId_card())
            .append("sex", getSex())
            .append("age", getAge())
            .append("work_time", getWork_time())
            .append("city", getCity())
            .append("lng", getLng())
            .append("lat", getLat())
            .append("address", getAddress())
            .append("text", getText())
            .append("license", getLicense())
            .append("work_img", getWork_img())
            .append("status", getStatus())
            .append("auth_status", getAuth_status())
            .append("sh_time", getSh_time())
            .append("sh_text", getSh_text())
            .append("create_time", getCreate_time())
            .toString();
    }
}
