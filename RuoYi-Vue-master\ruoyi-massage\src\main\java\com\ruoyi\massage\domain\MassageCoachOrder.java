package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 达人服务记录对象 ims_massage_service_order_list
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public class MassageCoachOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Long uniacid;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderCode;

    /** 达人ID */
    @Excel(name = "达人ID")
    private Long coachId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 服务项目 */
    @Excel(name = "服务项目")
    private String goodsInfo;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private Double payPrice;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private Integer orderStatus;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Long uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Long getUniacid() 
    {
        return uniacid;
    }
    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }
    public void setCoachId(Long coachId) 
    {
        this.coachId = coachId;
    }

    public Long getCoachId() 
    {
        return coachId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setGoodsInfo(String goodsInfo) 
    {
        this.goodsInfo = goodsInfo;
    }

    public String getGoodsInfo() 
    {
        return goodsInfo;
    }
    public void setPayPrice(Double payPrice) 
    {
        this.payPrice = payPrice;
    }

    public Double getPayPrice() 
    {
        return payPrice;
    }
    public void setOrderStatus(Integer orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderStatus() 
    {
        return orderStatus;
    }
    public void setCreateTime(Long createTime) 
    {
        this.createTime = createTime;
    }

    public Long getCreateTime() 
    {
        return createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("orderCode", getOrderCode())
            .append("coachId", getCoachId())
            .append("userId", getUserId())
            .append("goodsInfo", getGoodsInfo())
            .append("payPrice", getPayPrice())
            .append("orderStatus", getOrderStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
