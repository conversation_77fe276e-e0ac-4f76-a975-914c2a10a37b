package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 服务项目评价对象 ims_massage_service_order_comment_goods
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MassageCommentGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 服务评价ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 评价ID */
    @Excel(name = "评价ID")
    private Long commentId;

    /** 服务项目ID */
    @Excel(name = "服务项目ID")
    private Long serviceId;

    /** 服务评价星级(1-5) */
    @Excel(name = "服务评价星级", readConverterExp = "1=1星,2=2星,3=3星,4=4星,5=5星")
    private Integer star;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    // 关联信息
    /** 服务名称 */
    private String serviceName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }
    public void setCommentId(Long commentId) 
    {
        this.commentId = commentId;
    }

    public Long getCommentId() 
    {
        return commentId;
    }
    public void setServiceId(Long serviceId) 
    {
        this.serviceId = serviceId;
    }

    public Long getServiceId() 
    {
        return serviceId;
    }
    public void setStar(Integer star) 
    {
        this.star = star;
    }

    public Integer getStar() 
    {
        return star;
    }
    public void setCreateTimestamp(Long createTimestamp) 
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp() 
    {
        return createTimestamp;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("commentId", getCommentId())
            .append("serviceId", getServiceId())
            .append("star", getStar())
            .append("createTimestamp", getCreateTimestamp())
            .toString();
    }
}
