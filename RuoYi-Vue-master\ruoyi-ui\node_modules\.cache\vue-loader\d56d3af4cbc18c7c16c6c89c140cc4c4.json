{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\ConsumptionRecordTab.vue?vue&type=style&index=0&id=2c4aa57c&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\ConsumptionRecordTab.vue", "mtime": 1753763412993}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753582855261}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753582864848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753582856704}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jb25zdW1wdGlvbi1yZWNvcmQtdGFiIHsKICBwYWRkaW5nOiAyMHB4IDA7Cn0KCi5zdGF0LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5zdGF0LWl0ZW0gewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAxMHB4Owp9Cgouc3RhdC12YWx1ZSB7CiAgZm9udC1zaXplOiAyNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjNDA5ZWZmOwogIG1hcmdpbi1ib3R0b206IDVweDsKfQoKLnN0YXQtbGFiZWwgewogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQo="}, {"version": 3, "sources": ["ConsumptionRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ConsumptionRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"consumption-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"订单类型\" prop=\"orderType\">\n        <el-select v-model=\"queryParams.orderType\" placeholder=\"请选择订单类型\" clearable>\n          <el-option label=\"按摩服务\" value=\"1\" />\n          <el-option label=\"余额充值\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"支付方式\" prop=\"payType\">\n        <el-select v-model=\"queryParams.payType\" placeholder=\"请选择支付方式\" clearable>\n          <el-option label=\"微信支付\" value=\"1\" />\n          <el-option label=\"支付宝支付\" value=\"2\" />\n          <el-option label=\"余额支付\" value=\"3\" />\n          <el-option label=\"线下支付\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"消费时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalAmount }}</div>\n            <div class=\"stat-label\">总消费金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ totalCount }}</div>\n            <div class=\"stat-label\">消费次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgAmount }}</div>\n            <div class=\"stat-label\">平均消费</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastConsumptionDate }}</div>\n            <div class=\"stat-label\">最近消费</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 消费记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单号\" align=\"center\" prop=\"order_code\" width=\"180\" />\n      <el-table-column label=\"消费金额\" align=\"center\" prop=\"pay_price\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #f56c6c; font-weight: bold;\">¥{{ scope.row.pay_price || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"支付方式\" align=\"center\" prop=\"pay_type\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.pay_model === 2\" type=\"success\">余额支付</el-tag>\n          <el-tag v-else-if=\"scope.row.pay_type === 1\" type=\"primary\">微信支付</el-tag>\n          <el-tag v-else-if=\"scope.row.pay_type === 2\" type=\"warning\">支付宝</el-tag>\n          <el-tag v-else type=\"info\">其他</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"消费时间\" align=\"center\" prop=\"create_time\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserOrderList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"ConsumptionRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 消费记录表格数据\n      recordList: [],\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        orderType: null,\n        payType: null\n      },\n      // 统计数据\n      totalAmount: 0,\n      totalCount: 0,\n      avgAmount: 0,\n      lastConsumptionDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    /** 查询消费记录列表 */\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        userId: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserOrderList(params).then(response => {\n        this.recordList = response.data.list || [];\n        this.total = response.data.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    /** 获取统计数据 */\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        this.totalAmount = this.recordList.reduce((sum, item) => sum + (parseFloat(item.pay_price) || 0), 0);\n        this.totalCount = this.recordList.length;\n        this.avgAmount = this.totalCount > 0 ? (this.totalAmount / this.totalCount) : 0;\n\n        // 获取最近消费时间\n        const latestOrder = this.recordList.reduce((latest, current) => {\n          return (current.create_time > latest.create_time) ? current : latest;\n        });\n        this.lastConsumptionDate = this.parseTime(latestOrder.create_time * 1000, '{y}-{m}-{d}');\n      } else {\n        this.totalAmount = 0;\n        this.totalCount = 0;\n        this.avgAmount = 0;\n        this.lastConsumptionDate = '-';\n      }\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n      this.getStatistics();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 查看详情 */\n    handleView(row) {\n      this.$message.info('查看订单详情功能开发中...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.consumption-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}