<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageBalanceRecordMapper">
    
    <resultMap type="MassageBalanceRecord" id="MassageBalanceRecordResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="userId"    column="user_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="type"    column="type"    />
        <result property="add"    column="add"    />
        <result property="price"    column="price"    />
        <result property="beforeBalance"    column="before_balance"    />
        <result property="afterBalance"    column="after_balance"    />
        <result property="createTimestamp"    column="create_time"    />
    </resultMap>

    <sql id="selectMassageBalanceRecordVo">
        select id, uniacid, user_id, order_id, type, `add`, price, before_balance, after_balance, create_time from ims_massage_service_balance_water
    </sql>

    <select id="selectMassageBalanceRecordList" parameterType="MassageBalanceRecord" resultMap="MassageBalanceRecordResult">
        <include refid="selectMassageBalanceRecordVo"/>
        <where>
            <if test="uniacid != null "> and uniacid = #{uniacid}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="add != null "> and `add` = #{add}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="beforeBalance != null "> and before_balance = #{beforeBalance}</if>
            <if test="afterBalance != null "> and after_balance = #{afterBalance}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageBalanceRecordById" parameterType="Long" resultMap="MassageBalanceRecordResult">
        <include refid="selectMassageBalanceRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMassageBalanceRecord" parameterType="MassageBalanceRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_service_balance_water
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">uniacid,</if>
            <if test="userId != null">user_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="type != null">type,</if>
            <if test="add != null">`add`,</if>
            <if test="price != null">price,</if>
            <if test="beforeBalance != null">before_balance,</if>
            <if test="afterBalance != null">after_balance,</if>
            <if test="createTimestamp != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">#{uniacid},</if>
            <if test="userId != null">#{userId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="type != null">#{type},</if>
            <if test="add != null">#{add},</if>
            <if test="price != null">#{price},</if>
            <if test="beforeBalance != null">#{beforeBalance},</if>
            <if test="afterBalance != null">#{afterBalance},</if>
            <if test="createTimestamp != null">#{createTimestamp},</if>
         </trim>
    </insert>

    <update id="updateMassageBalanceRecord" parameterType="MassageBalanceRecord">
        update ims_massage_service_balance_water
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="add != null">`add` = #{add},</if>
            <if test="price != null">price = #{price},</if>
            <if test="beforeBalance != null">before_balance = #{beforeBalance},</if>
            <if test="afterBalance != null">after_balance = #{afterBalance},</if>
            <if test="createTimestamp != null">create_time = #{createTimestamp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageBalanceRecordById" parameterType="Long">
        delete from ims_massage_service_balance_water where id = #{id}
    </delete>

    <delete id="deleteMassageBalanceRecordByIds" parameterType="String">
        delete from ims_massage_service_balance_water where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取用户余额记录列表（用于明细显示） -->
    <select id="selectUserBalanceRecordList" parameterType="Long" resultType="java.util.Map">
        select
            r.id,
            '系统' as control_name,
            CASE
                WHEN r.type = 1 THEN '用户充值'
                WHEN r.type = 2 THEN '下单消费'
                WHEN r.type = 3 THEN '订单退款'
                WHEN r.type = 4 THEN '升级消费'
                WHEN r.type = 5 THEN '管理员调整'
                ELSE '其他操作'
            END as type_text,
            '' as goods_title,
            r.`add`,
            r.price,
            r.after_balance,
            CASE
                WHEN r.type = 1 THEN '用户充值到账'
                WHEN r.type = 2 THEN '订单消费扣减'
                WHEN r.type = 3 THEN '订单退款到账'
                WHEN r.type = 4 THEN '升级服务扣减'
                WHEN r.type = 5 THEN '管理员手动调整'
                ELSE '系统操作'
            END as text,
            r.create_time
        from ims_massage_service_balance_water r
        where r.user_id = #{userId}
        order by r.create_time desc
    </select>
</mapper>
