import request from '@/utils/request'

// 查询用户评价列表
export function getUserCommentList(query) {
  return request({
    url: '/massage/user/commentList',
    method: 'get',
    params: query
  })
}

// 查询评价详细
export function getComment(id) {
  return request({
    url: '/massage/comment/' + id,
    method: 'get'
  })
}

// 新增评价
export function addComment(data) {
  return request({
    url: '/massage/comment',
    method: 'post',
    data: data
  })
}

// 修改评价
export function updateComment(data) {
  return request({
    url: '/massage/comment',
    method: 'put',
    data: data
  })
}

// 删除评价
export function delComment(id) {
  return request({
    url: '/massage/comment/' + id,
    method: 'delete'
  })
}

// 查询评价标签列表
export function getLableList(query) {
  return request({
    url: '/massage/lable/list',
    method: 'get',
    params: query
  })
}

// 新增评价标签
export function addLable(data) {
  return request({
    url: '/massage/lable',
    method: 'post',
    data: data
  })
}

// 修改评价标签
export function updateLable(data) {
  return request({
    url: '/massage/lable',
    method: 'put',
    data: data
  })
}

// 删除评价标签
export function delLable(id) {
  return request({
    url: '/massage/lable/' + id,
    method: 'delete'
  })
}
