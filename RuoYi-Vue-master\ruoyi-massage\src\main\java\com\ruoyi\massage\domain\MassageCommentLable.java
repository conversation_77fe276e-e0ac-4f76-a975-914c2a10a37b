package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 评价标签关联对象 ims_massage_service_comment_lable
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MassageCommentLable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 评价ID */
    @Excel(name = "评价ID")
    private Long commentId;

    /** 标签ID */
    @Excel(name = "标签ID")
    private Long lableId;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String lableTitle;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }
    public void setCommentId(Long commentId) 
    {
        this.commentId = commentId;
    }

    public Long getCommentId() 
    {
        return commentId;
    }
    public void setLableId(Long lableId) 
    {
        this.lableId = lableId;
    }

    public Long getLableId() 
    {
        return lableId;
    }
    public void setLableTitle(String lableTitle) 
    {
        this.lableTitle = lableTitle;
    }

    public String getLableTitle() 
    {
        return lableTitle;
    }
    public void setCreateTimestamp(Long createTimestamp) 
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp() 
    {
        return createTimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("commentId", getCommentId())
            .append("lableId", getLableId())
            .append("lableTitle", getLableTitle())
            .append("createTimestamp", getCreateTimestamp())
            .toString();
    }
}
