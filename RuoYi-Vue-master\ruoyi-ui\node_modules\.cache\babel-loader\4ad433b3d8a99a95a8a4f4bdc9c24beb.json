{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue", "mtime": 1753765086018}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_UserInfoTab", "_interopRequireDefault", "_ConsumptionRecordTab", "_RechargeRecordTab", "_GrowthDetailTab", "_UserCouponsTab", "_UserDiscountsTab", "_BlockedUsersTab", "_CommentListTab", "name", "components", "UserInfoTab", "ConsumptionRecordTab", "RechargeRecordTab", "GrowthDetailTab", "UserCouponsTab", "UserDiscountsTab", "BlockedUsersTab", "CommentListTab", "data", "userId", "userInfo", "activeTab", "loading", "created", "$route", "params", "id", "getUserInfo", "mounted", "_this", "$eventBus", "$on", "refreshBalanceRecords", "refreshGrowthRecords", "refreshAllRecords", "<PERSON><PERSON><PERSON><PERSON>", "$off", "methods", "_this2", "getUser", "then", "response", "catch", "handleTabClick", "tab", "goBack", "$router", "go", "$refs", "rechargeRecordTab", "refresh", "growthDetailTab", "refreshConsumptionRecords", "consumptionRecordTab", "refreshCommentRecords", "commentListTab"], "sources": ["src/views/massage/user/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 用户基本信息 -->\n    <el-card class=\"user-info-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>用户详情</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"user-avatar-section\">\n            <img :src=\"userInfo.avatarUrl || '/static/default-avatar.png'\" alt=\"用户头像\" class=\"user-avatar\">\n            <h3>{{ userInfo.nickName || '未设置昵称' }}</h3>\n            <p class=\"user-id\">用户ID: {{ userInfo.id }}</p>\n          </div>\n        </el-col>\n        <el-col :span=\"18\">\n          <div class=\"user-stats\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.balance || 0 }}</div>\n                  <div class=\"stat-label\">用户余额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.cash || 0 }}</div>\n                  <div class=\"stat-label\">现金余额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.totalConsumption || 0 }}</div>\n                  <div class=\"stat-label\">消费总金额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">{{ userInfo.growth || 0 }}</div>\n                  <div class=\"stat-label\">成长值</div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n          \n          <el-descriptions :column=\"3\" border style=\"margin-top: 20px;\">\n            <el-descriptions-item label=\"手机号\">{{ userInfo.phone || '未绑定' }}</el-descriptions-item>\n            <el-descriptions-item label=\"性别\">\n              <span v-if=\"userInfo.gender === 1\">男</span>\n              <span v-else-if=\"userInfo.gender === 2\">女</span>\n              <span v-else>未知</span>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"城市\">{{ userInfo.city || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"省份\">{{ userInfo.province || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"国家\">{{ userInfo.country || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"用户状态\">\n              <el-tag :type=\"userInfo.status === 1 ? 'success' : 'danger'\">\n                {{ userInfo.status === 1 ? '正常' : '禁用' }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"注册时间\">{{ parseTime(userInfo.createTime * 1000) }}</el-descriptions-item>\n            <el-descriptions-item label=\"应用ID\">{{ userInfo.uniacid || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"来源类型\">{{ userInfo.sourceType || '未知' }}</el-descriptions-item>\n          </el-descriptions>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 标签页内容 -->\n    <el-card style=\"margin-top: 20px;\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"用户信息\" name=\"userInfo\">\n          <user-info-tab ref=\"userInfoTab\" :user-info=\"userInfo\" @refresh=\"getUserInfo\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"消费记录\" name=\"consumption\">\n          <consumption-record-tab ref=\"consumptionRecordTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"余额明细\" name=\"recharge\">\n          <recharge-record-tab ref=\"rechargeRecordTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"成长值明细\" name=\"growth\">\n          <growth-detail-tab ref=\"growthDetailTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"评价内容\" name=\"comment\">\n          <comment-list-tab ref=\"commentListTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"持有优惠券\" name=\"coupons\">\n          <user-coupons-tab :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"持有折扣卡\" name=\"discounts\">\n          <user-discounts-tab :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"屏蔽达人\" name=\"blocked\">\n          <blocked-users-tab :user-id=\"userId\" />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getUser } from \"@/api/massage/user\";\nimport UserInfoTab from './components/UserInfoTab.vue';\nimport ConsumptionRecordTab from './components/ConsumptionRecordTab.vue';\nimport RechargeRecordTab from './components/RechargeRecordTab.vue';\nimport GrowthDetailTab from './components/GrowthDetailTab.vue';\nimport UserCouponsTab from './components/UserCouponsTab.vue';\nimport UserDiscountsTab from './components/UserDiscountsTab.vue';\nimport BlockedUsersTab from './components/BlockedUsersTab.vue';\nimport CommentListTab from './components/CommentListTab.vue';\n\nexport default {\n  name: \"UserDetail\",\n  components: {\n    UserInfoTab,\n    ConsumptionRecordTab,\n    RechargeRecordTab,\n    GrowthDetailTab,\n    UserCouponsTab,\n    UserDiscountsTab,\n    BlockedUsersTab,\n    CommentListTab\n  },\n  data() {\n    return {\n      userId: null,\n      userInfo: {},\n      activeTab: 'userInfo',\n      loading: false\n    };\n  },\n  created() {\n    this.userId = this.$route.params.id;\n    this.getUserInfo();\n  },\n  mounted() {\n    // 监听余额调整事件\n    this.$eventBus.$on('userBalanceAdjusted', (userId) => {\n      if (userId == this.userId) {\n        this.refreshBalanceRecords();\n        // 重新获取用户信息以更新余额显示\n        this.getUserInfo();\n      }\n    });\n\n    // 监听成长值调整事件\n    this.$eventBus.$on('userGrowthAdjusted', (userId) => {\n      if (userId == this.userId) {\n        this.refreshGrowthRecords();\n        // 重新获取用户信息以更新成长值显示\n        this.getUserInfo();\n      }\n    });\n\n    // 监听订单支付成功事件\n    this.$eventBus.$on('orderPaymentSuccess', (userId) => {\n      if (userId == this.userId) {\n        this.refreshAllRecords();\n        // 重新获取用户信息以更新余额和成长值显示\n        this.getUserInfo();\n      }\n    });\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    this.$eventBus.$off('userBalanceAdjusted');\n    this.$eventBus.$off('userGrowthAdjusted');\n    this.$eventBus.$off('orderPaymentSuccess');\n  },\n  methods: {\n    /** 获取用户信息 */\n    getUserInfo() {\n      this.loading = true;\n      getUser(this.userId).then(response => {\n        this.userInfo = response.data;\n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    /** 标签页切换 */\n    handleTabClick(tab) {\n      this.activeTab = tab.name;\n    },\n    /** 返回列表 */\n    goBack() {\n      this.$router.go(-1);\n    },\n    /** 刷新余额明细 */\n    refreshBalanceRecords() {\n      if (this.$refs.rechargeRecordTab) {\n        this.$refs.rechargeRecordTab.refresh();\n      }\n    },\n    /** 刷新成长值明细 */\n    refreshGrowthRecords() {\n      if (this.$refs.growthDetailTab) {\n        this.$refs.growthDetailTab.refresh();\n      }\n    },\n    /** 刷新消费记录 */\n    refreshConsumptionRecords() {\n      if (this.$refs.consumptionRecordTab) {\n        this.$refs.consumptionRecordTab.refresh();\n      }\n    },\n    /** 刷新评价记录 */\n    refreshCommentRecords() {\n      if (this.$refs.commentListTab) {\n        this.$refs.commentListTab.refresh();\n      }\n    },\n    /** 刷新所有明细记录 */\n    refreshAllRecords() {\n      this.refreshBalanceRecords();\n      this.refreshGrowthRecords();\n      this.refreshConsumptionRecords();\n      this.refreshCommentRecords();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.user-info-card {\n  margin-bottom: 20px;\n}\n\n.user-avatar-section {\n  text-align: center;\n  padding: 20px;\n}\n\n.user-avatar {\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 3px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar-section h3 {\n  margin: 15px 0 5px 0;\n  color: #333;\n  font-weight: 500;\n}\n\n.user-id {\n  color: #666;\n  font-size: 14px;\n}\n\n.user-stats {\n  padding: 20px 0;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n.el-descriptions {\n  margin-top: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAuGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,kBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,gBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,eAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,iBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,gBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,eAAA,GAAAP,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,oBAAA,EAAAA,6BAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,cAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAJ,MAAA,QAAAK,MAAA,CAAAC,MAAA,CAAAC,EAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAAC,SAAA,CAAAC,GAAA,kCAAAZ,MAAA;MACA,IAAAA,MAAA,IAAAU,KAAA,CAAAV,MAAA;QACAU,KAAA,CAAAG,qBAAA;QACA;QACAH,KAAA,CAAAF,WAAA;MACA;IACA;;IAEA;IACA,KAAAG,SAAA,CAAAC,GAAA,iCAAAZ,MAAA;MACA,IAAAA,MAAA,IAAAU,KAAA,CAAAV,MAAA;QACAU,KAAA,CAAAI,oBAAA;QACA;QACAJ,KAAA,CAAAF,WAAA;MACA;IACA;;IAEA;IACA,KAAAG,SAAA,CAAAC,GAAA,kCAAAZ,MAAA;MACA,IAAAA,MAAA,IAAAU,KAAA,CAAAV,MAAA;QACAU,KAAA,CAAAK,iBAAA;QACA;QACAL,KAAA,CAAAF,WAAA;MACA;IACA;EACA;EACAQ,aAAA,WAAAA,cAAA;IACA;IACA,KAAAL,SAAA,CAAAM,IAAA;IACA,KAAAN,SAAA,CAAAM,IAAA;IACA,KAAAN,SAAA,CAAAM,IAAA;EACA;EACAC,OAAA;IACA,aACAV,WAAA,WAAAA,YAAA;MAAA,IAAAW,MAAA;MACA,KAAAhB,OAAA;MACA,IAAAiB,aAAA,OAAApB,MAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAAlB,QAAA,GAAAqB,QAAA,CAAAvB,IAAA;QACAoB,MAAA,CAAAhB,OAAA;MACA,GAAAoB,KAAA;QACAJ,MAAA,CAAAhB,OAAA;MACA;IACA;IACA,YACAqB,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAvB,SAAA,GAAAuB,GAAA,CAAApC,IAAA;IACA;IACA,WACAqC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACA,aACAf,qBAAA,WAAAA,sBAAA;MACA,SAAAgB,KAAA,CAAAC,iBAAA;QACA,KAAAD,KAAA,CAAAC,iBAAA,CAAAC,OAAA;MACA;IACA;IACA,cACAjB,oBAAA,WAAAA,qBAAA;MACA,SAAAe,KAAA,CAAAG,eAAA;QACA,KAAAH,KAAA,CAAAG,eAAA,CAAAD,OAAA;MACA;IACA;IACA,aACAE,yBAAA,WAAAA,0BAAA;MACA,SAAAJ,KAAA,CAAAK,oBAAA;QACA,KAAAL,KAAA,CAAAK,oBAAA,CAAAH,OAAA;MACA;IACA;IACA,aACAI,qBAAA,WAAAA,sBAAA;MACA,SAAAN,KAAA,CAAAO,cAAA;QACA,KAAAP,KAAA,CAAAO,cAAA,CAAAL,OAAA;MACA;IACA;IACA,eACAhB,iBAAA,WAAAA,kBAAA;MACA,KAAAF,qBAAA;MACA,KAAAC,oBAAA;MACA,KAAAmB,yBAAA;MACA,KAAAE,qBAAA;IACA;EACA;AACA", "ignoreList": []}]}