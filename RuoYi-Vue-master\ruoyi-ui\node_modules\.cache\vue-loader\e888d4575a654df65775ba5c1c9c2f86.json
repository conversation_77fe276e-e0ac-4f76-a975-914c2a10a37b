{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=style&index=0&id=6cee415a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753755759233}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753582855261}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753582864848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753582856704}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5yZWNoYXJnZS1yZWNvcmQtdGFiIHsKICBwYWRkaW5nOiAyMHB4IDA7Cn0KCi5zdGF0LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5zdGF0LWl0ZW0gewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAxMHB4Owp9Cgouc3RhdC12YWx1ZSB7CiAgZm9udC1zaXplOiAyNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjNDA5ZWZmOwogIG1hcmdpbi1ib3R0b206IDVweDsKfQoKLnN0YXQtbGFiZWwgewogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQo="}, {"version": 3, "sources": ["RechargeRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "RechargeRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"recharge-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"充值方式\" prop=\"payMethod\">\n        <el-select v-model=\"queryParams.payMethod\" placeholder=\"请选择充值方式\" clearable>\n          <el-option label=\"微信支付\" value=\"1\" />\n          <el-option label=\"支付宝\" value=\"2\" />\n          <el-option label=\"银行卡\" value=\"3\" />\n          <el-option label=\"现金\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"充值时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalRecharge }}</div>\n            <div class=\"stat-label\">总充值金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ rechargeCount }}</div>\n            <div class=\"stat-label\">充值次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgRecharge }}</div>\n            <div class=\"stat-label\">平均充值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastRechargeDate }}</div>\n            <div class=\"stat-label\">最近充值</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 充值记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"control_name\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>\n          <span class=\"ml-md\">\n            <span :class=\"[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]\">\n              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}\n            </span>\n          </span>\n          ，现余额<span class=\"ml-sm c-success\">¥{{ scope.row.after_balance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"text\">\n        <template slot-scope=\"scope\">\n          <div class=\"ellipsis-2\" v-html=\"scope.row.text\"></div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserBalanceList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"RechargeRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        payMethod: null\n      },\n      totalRecharge: 0,\n      rechargeCount: 0,\n      avgRecharge: 0,\n      lastRechargeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserBalanceList(params).then(response => {\n        console.log('余额记录API响应:', response);\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch((error) => {\n        console.error('余额记录API错误:', error);\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 只统计充值记录（add=1的记录）\n        const rechargeRecords = this.recordList.filter(item => item.add === 1);\n        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\n        this.rechargeCount = rechargeRecords.length;\n        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;\n\n        // 获取最近充值时间\n        if (rechargeRecords.length > 0) {\n          const latestRecharge = rechargeRecords.reduce((latest, current) => {\n            return (current.create_time > latest.create_time) ? current : latest;\n          });\n          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');\n        } else {\n          this.lastRechargeDate = '-';\n        }\n      } else {\n        this.totalRecharge = 0;\n        this.rechargeCount = 0;\n        this.avgRecharge = 0;\n        this.lastRechargeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.recharge-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}