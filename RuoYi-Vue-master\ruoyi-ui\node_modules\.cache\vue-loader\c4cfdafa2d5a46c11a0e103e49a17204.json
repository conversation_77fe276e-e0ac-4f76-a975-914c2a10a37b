{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753800216449}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENvYWNoLCBhZGRDb2FjaCwgdXBkYXRlQ29hY2ggfSBmcm9tICJAL2FwaS9tYXNzYWdlL2NvYWNoIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ29hY2hFZGl0IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5r+A5rS755qE5qCH562+6aG1CiAgICAgIGFjdGl2ZU5hbWU6ICJiYXNpYyIsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgY29hY2hfbmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui+vuS6uuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDIsIG1heDogMTUsIG1lc3NhZ2U6ICLlp5PlkI3plb/luqblnKggMiDliLAgMTUg5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIG1vYmlsZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJi+acuuWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgewogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLAogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIHNleDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaAp+WIqyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHdvcmtfdGltZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeS7juS4muaXtumXtCIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyB0eXBlOiAibnVtYmVyIiwgbWluOiAwLCBtYXg6IDUwLCBtZXNzYWdlOiAi5LuO5Lia5pe26Ze05bqU5ZyoMC01MOW5tOS5i+mXtCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBoZWlnaHQ6IFsKICAgICAgICAgIHsgcGF0dGVybjogL15cZHsyLDN9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTouqvpq5goY20pIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHdlaWdodDogWwogICAgICAgICAgeyBwYXR0ZXJuOiAvXlxkezIsM30oXC5cZCk/JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTkvZPph40oa2cpIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGlkX2NhcmQ6IFsKICAgICAgICAgIHsgcGF0dGVybjogL15bMS05XVxkezV9KDE4fDE5fDIwKVxkezJ9KCgwWzEtOV0pfCgxWzAtMl0pKSgoWzAtMl1bMS05XSl8MTB8MjB8MzB8MzEpXGR7M31bMC05WHhdJC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTouqvku73or4Hlj7ciLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgc2VydmljZV9wcmljZTogWwogICAgICAgICAgeyB0eXBlOiAibnVtYmVyIiwgbWluOiAwLCBtZXNzYWdlOiAi5pyN5Yqh5Lu35qC85LiN6IO95bCP5LqOMCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBjYXJfcHJpY2U6IFsKICAgICAgICAgIHsgdHlwZTogIm51bWJlciIsIG1pbjogMCwgbWVzc2FnZTogIui9pui0ueS4jeiDveWwj+S6jjAiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICBjb25zdCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOwogICAgdGhpcy5mb3JtLmlkID0gaWQgfHwgbnVsbDsKICAgIGlmIChpZCkgewogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuei+vuS6uiI7CiAgICAgIHRoaXMuZ2V0SW5mbyhpZCk7CiAgICB9IGVsc2UgewogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinui+vuS6uiI7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lovr7kurror6bnu4YgKi8KICAgIGdldEluZm8oaWQpIHsKICAgICAgZ2V0Q29hY2goaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICB1bmlhY2lkOiAxLAogICAgICAgIGNvYWNoX25hbWU6IG51bGwsCiAgICAgICAgdXNlcl9pZDogbnVsbCwKICAgICAgICBtb2JpbGU6IG51bGwsCiAgICAgICAgaWRfY2FyZDogbnVsbCwKICAgICAgICBzZXg6IDEsCiAgICAgICAgd29ya190aW1lOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgbG5nOiBudWxsLAogICAgICAgIGxhdDogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIHRleHQ6IG51bGwsCiAgICAgICAgbGljZW5zZTogbnVsbCwKICAgICAgICB3b3JrX2ltZzogbnVsbCwKICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgYXV0aF9zdGF0dXM6IDAsCiAgICAgICAgc2hfdGltZTogbnVsbCwKICAgICAgICBzaF90ZXh0OiBudWxsLAogICAgICAgIGNyZWF0ZV90aW1lOiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKSwKICAgICAgICBzZWxmX2ltZzogbnVsbCwKICAgICAgICBpc193b3JrOiAxLAogICAgICAgIHN0YXJ0X3RpbWU6IG51bGwsCiAgICAgICAgZW5kX3RpbWU6IG51bGwsCiAgICAgICAgc2VydmljZV9wcmljZTogbnVsbCwKICAgICAgICBjYXJfcHJpY2U6IG51bGwsCiAgICAgICAgaWRfY29kZTogbnVsbCwKICAgICAgICBzdGFyOiA1LjAsCiAgICAgICAgYWRtaW5fYWRkOiAxLAogICAgICAgIGNpdHlfaWQ6IG51bGwsCiAgICAgICAgdmlkZW86IG51bGwsCiAgICAgICAgaXNfdXBkYXRlOiAwLAogICAgICAgIGludGVncmFsOiAwLAogICAgICAgIG9yZGVyX251bTogMCwKICAgICAgICByZWNvbW1lbmQ6IDAsCiAgICAgICAgYmFsYW5jZV9jYXNoOiAwLAogICAgICAgIGluZGV4X3RvcDogMCwKICAgICAgICBjb2FjaF9wb3NpdGlvbjogMCwKICAgICAgICBuZWFyX3RpbWU6IG51bGwsCiAgICAgICAgY2hlY2tfY2FzaDogMCwKICAgICAgICBhZ2VudF90eXBlOiAxLAogICAgICAgIHBhcnRuZXJfaWQ6IG51bGwsCiAgICAgICAgcGFydG5lcl90aW1lOiBudWxsLAogICAgICAgIHRvdGFsX29yZGVyX251bTogMCwKICAgICAgICBiaXJ0aGRheTogbnVsbCwKICAgICAgICBjcmVkaXRfdmFsdWU6IDEwMCwKICAgICAgICBjcmVkaXRfdG9wOiAwLAogICAgICAgIHJlY29tbWVuZF9pY29uOiBudWxsLAogICAgICAgIGZyZWVfZmFyZV9kaXN0YW5jZTogMCwKICAgICAgICBzaG93X3NhbGVudW06IDEsCiAgICAgICAgY29hY2hfaWNvbjogbnVsbCwKICAgICAgICB0cnVlX2xuZzogbnVsbCwKICAgICAgICB0cnVlX2xhdDogbnVsbCwKICAgICAgICB0eXBlX2lkOiBudWxsLAogICAgICAgIHZlcnNpb246IDEsCiAgICAgICAgdHJ1ZV91c2VyX25hbWU6IG51bGwsCiAgICAgICAgaW5kdXN0cnlfdHlwZTogbnVsbCwKICAgICAgICBoZWlnaHQ6IG51bGwsCiAgICAgICAgd2VpZ2h0OiBudWxsLAogICAgICAgIGNvbnN0ZWxsYXRpb246IG51bGwsCiAgICAgICAgZnJlZV9mYXJlX2JlYXI6IDAsCiAgICAgICAgcGVyc29uYWxpdHlfaWNvbjogbnVsbCwKICAgICAgICBzdGF0aW9uX2ljb246IG51bGwsCiAgICAgICAgYWRkcmVzc191cGRhdGVfdGltZTogbnVsbCwKICAgICAgICBwdjogMCwKICAgICAgICB2aXJ0dWFsX2NvbGxlY3Q6IDAsCiAgICAgICAgdmlydHVhbF9jb21tZW50OiAwLAogICAgICAgIHdvcmtfdHlwZTogMSwKICAgICAgICBkYXRhOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVDb2FjaCh0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkQ29hY2godGhpcy5mb3JtKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLmdvQmFjaygpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDov5Tlm54gKi8KICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9tYXNzYWdlL2NvYWNoIik7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuXA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/massage/coach", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-tabs v-model=\"activeName\" type=\"card\">\n          <el-tab-pane label=\"基础信息\" name=\"basic\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"达人姓名\" prop=\"coach_name\">\n                  <el-input v-model=\"form.coach_name\" maxlength=\"15\" show-word-limit placeholder=\"请输入达人姓名\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"手机号\" prop=\"mobile\">\n                  <el-input v-model=\"form.mobile\" maxlength=\"11\" show-word-limit placeholder=\"请输入手机号\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"性别\" prop=\"sex\">\n                  <el-radio-group v-model=\"form.sex\">\n                    <el-radio :label=\"1\">男</el-radio>\n                    <el-radio :label=\"2\">女</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"从业时间\" prop=\"work_time\">\n                  <el-input-number v-model=\"form.work_time\" :min=\"0\" :max=\"50\" placeholder=\"请输入从业年限\">\n                    <template slot=\"append\">年</template>\n                  </el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"身高\" prop=\"height\">\n                  <el-input v-model=\"form.height\" placeholder=\"请输入身高\">\n                    <template slot=\"append\">cm</template>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"体重\" prop=\"weight\">\n                  <el-input v-model=\"form.weight\" placeholder=\"请输入体重\">\n                    <template slot=\"append\">kg</template>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"生日\" prop=\"birthday\">\n                  <el-date-picker\n                    v-model=\"form.birthday\"\n                    type=\"date\"\n                    placeholder=\"请选择生日\"\n                    value-format=\"timestamp\"\n                    style=\"width: 100%\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"星座\" prop=\"constellation\">\n                  <el-input v-model=\"form.constellation\" placeholder=\"请输入星座\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"身份证号\" prop=\"id_card\">\n                  <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"城市\" prop=\"city\">\n                  <el-input v-model=\"form.city\" placeholder=\"请输入城市\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"服务地址\" prop=\"address\">\n              <el-input v-model=\"form.address\" placeholder=\"请输入服务地址\" />\n            </el-form-item>\n            <el-form-item label=\"个人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入个人简介\" />\n            </el-form-item>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"工作信息\" name=\"work\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"工作状态\" prop=\"is_work\">\n                  <el-radio-group v-model=\"form.is_work\">\n                    <el-radio :label=\"1\">工作中</el-radio>\n                    <el-radio :label=\"0\">休息中</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"工作类型\" prop=\"work_type\">\n                  <el-select v-model=\"form.work_type\" placeholder=\"请选择工作类型\">\n                    <el-option label=\"可服务\" :value=\"1\" />\n                    <el-option label=\"服务中\" :value=\"2\" />\n                    <el-option label=\"可预约\" :value=\"3\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否推荐\" prop=\"recommend\">\n                  <el-radio-group v-model=\"form.recommend\">\n                    <el-radio :label=\"0\">否</el-radio>\n                    <el-radio :label=\"1\">是</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"评分\" prop=\"star\">\n                  <el-rate v-model=\"form.star\" :max=\"5\" show-score text-color=\"#ff9900\" score-template=\"{value}\"></el-rate>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"服务价格\" prop=\"service_price\">\n                  <el-input-number v-model=\"form.service_price\" :min=\"0\" :precision=\"2\" placeholder=\"请输入服务价格\">\n                    <template slot=\"append\">元</template>\n                  </el-input-number>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"车费\" prop=\"car_price\">\n                  <el-input-number v-model=\"form.car_price\" :min=\"0\" :precision=\"2\" placeholder=\"请输入车费\">\n                    <template slot=\"append\">元</template>\n                  </el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开始时间\" prop=\"start_time\">\n                  <el-time-picker\n                    v-model=\"form.start_time\"\n                    placeholder=\"请选择开始工作时间\"\n                    format=\"HH:mm\"\n                    value-format=\"HH:mm\"\n                    style=\"width: 100%\"\n                  ></el-time-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"结束时间\" prop=\"end_time\">\n                  <el-time-picker\n                    v-model=\"form.end_time\"\n                    placeholder=\"请选择结束工作时间\"\n                    format=\"HH:mm\"\n                    value-format=\"HH:mm\"\n                    style=\"width: 100%\"\n                  ></el-time-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"免车费距离\" prop=\"free_fare_distance\">\n                  <el-input-number v-model=\"form.free_fare_distance\" :min=\"0\" :precision=\"2\" placeholder=\"请输入免车费距离\">\n                    <template slot=\"append\">km</template>\n                  </el-input-number>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"车费承担方\" prop=\"free_fare_bear\">\n                  <el-select v-model=\"form.free_fare_bear\" placeholder=\"请选择车费承担方\">\n                    <el-option label=\"不开启\" :value=\"0\" />\n                    <el-option label=\"平台\" :value=\"1\" />\n                    <el-option label=\"技师\" :value=\"2\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"虚拟订单数\" prop=\"order_num\">\n                  <el-input-number v-model=\"form.order_num\" :min=\"0\" placeholder=\"请输入虚拟订单数量\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"虚拟排序\" prop=\"index_top\">\n                  <el-input-number v-model=\"form.index_top\" :min=\"0\" placeholder=\"请输入排序值\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"图片信息\" name=\"images\">\n            <el-form-item label=\"工作照片\" prop=\"work_img\">\n              <image-upload v-model=\"form.work_img\" :limit=\"1\"/>\n              <div class=\"el-upload__tip\">建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n            <el-form-item label=\"个人照片\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\" :limit=\"9\"/>\n              <div class=\"el-upload__tip\">最多上传9张，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n            <el-form-item label=\"执照证书\" prop=\"license\">\n              <image-upload v-model=\"form.license\" :limit=\"3\"/>\n              <div class=\"el-upload__tip\">最多上传3张，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n            <el-form-item label=\"个人视频\" prop=\"video\">\n              <file-upload v-model=\"form.video\" :limit=\"1\" :file-type=\"['mp4', 'avi', 'mov']\"/>\n              <div class=\"el-upload__tip\">支持mp4、avi、mov格式，大小不超过50M</div>\n            </el-form-item>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"位置信息\" name=\"location\">\n            <el-form-item label=\"所在地址\" prop=\"address\">\n              <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\n            </el-form-item>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"显示经度\" prop=\"lng\">\n                  <el-input v-model=\"form.lng\" placeholder=\"请输入显示经度\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"显示纬度\" prop=\"lat\">\n                  <el-input v-model=\"form.lat\" placeholder=\"请输入显示纬度\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"真实经度\" prop=\"true_lng\">\n                  <el-input v-model=\"form.true_lng\" placeholder=\"请输入真实经度\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"真实纬度\" prop=\"true_lat\">\n                  <el-input v-model=\"form.true_lat\" placeholder=\"请输入真实纬度\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"城市ID\" prop=\"city_id\">\n                  <el-input-number v-model=\"form.city_id\" :min=\"0\" placeholder=\"请输入城市ID\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"实时定位\" prop=\"coach_position\">\n                  <el-radio-group v-model=\"form.coach_position\">\n                    <el-radio :label=\"0\">关闭</el-radio>\n                    <el-radio :label=\"1\">开启</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"地址更新时间\" prop=\"address_update_time\">\n              <el-date-picker\n                v-model=\"form.address_update_time\"\n                type=\"datetime\"\n                placeholder=\"请选择地址更新时间\"\n                value-format=\"timestamp\"\n                style=\"width: 100%\"\n              ></el-date-picker>\n            </el-form-item>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"其他设置\" name=\"other\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"行业类型\" prop=\"industry_type\">\n                  <el-input-number v-model=\"form.industry_type\" :min=\"0\" placeholder=\"请输入行业类型\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"技师类型\" prop=\"type_id\">\n                  <el-input-number v-model=\"form.type_id\" :min=\"0\" placeholder=\"请输入技师类型\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"个性标签\" prop=\"personality_icon\">\n                  <el-input-number v-model=\"form.personality_icon\" :min=\"0\" placeholder=\"请输入个性标签ID\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"岗位标签\" prop=\"station_icon\">\n                  <el-input-number v-model=\"form.station_icon\" :min=\"0\" placeholder=\"请输入岗位标签ID\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"技师图标\" prop=\"coach_icon\">\n                  <el-input-number v-model=\"form.coach_icon\" :min=\"0\" placeholder=\"请输入技师图标ID\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"推荐挂件\" prop=\"recommend_icon\">\n                  <el-input-number v-model=\"form.recommend_icon\" :min=\"0\" placeholder=\"请输入推荐挂件ID\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"显示销量\" prop=\"show_salenum\">\n                  <el-radio-group v-model=\"form.show_salenum\">\n                    <el-radio :label=\"0\">否</el-radio>\n                    <el-radio :label=\"1\">是</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"管理员添加\" prop=\"admin_add\">\n                  <el-radio-group v-model=\"form.admin_add\">\n                    <el-radio :label=\"0\">否</el-radio>\n                    <el-radio :label=\"1\">是</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"虚拟收藏量\" prop=\"virtual_collect\">\n                  <el-input-number v-model=\"form.virtual_collect\" :min=\"0\" placeholder=\"请输入虚拟收藏量\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"虚拟评论量\" prop=\"virtual_comment\">\n                  <el-input-number v-model=\"form.virtual_comment\" :min=\"0\" placeholder=\"请输入虚拟评论量\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"访问量\" prop=\"pv\">\n                  <el-input-number v-model=\"form.pv\" :min=\"0\" placeholder=\"请输入访问量\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"版本号\" prop=\"version\">\n                  <el-input-number v-model=\"form.version\" :min=\"0\" placeholder=\"请输入版本号\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"真实姓名\" prop=\"true_user_name\">\n              <el-input v-model=\"form.true_user_name\" placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n            <el-form-item label=\"身份证编码\" prop=\"id_code\">\n              <el-input v-model=\"form.id_code\" placeholder=\"请输入身份证编码\" />\n            </el-form-item>\n            <el-form-item label=\"扩展数据\" prop=\"data\">\n              <el-input v-model=\"form.data\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入扩展数据(JSON格式)\" />\n            </el-form-item>\n          </el-tab-pane>\n        </el-tabs>\n        \n        <div style=\"text-align: center; margin-top: 20px;\">\n          <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n          <el-button @click=\"goBack\">取 消</el-button>\n        </div>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        coach_name: [\n          { required: true, message: \"达人姓名不能为空\", trigger: \"blur\" },\n          { min: 2, max: 15, message: \"姓名长度在 2 到 15 个字符\", trigger: \"blur\" }\n        ],\n        mobile: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ],\n        sex: [\n          { required: true, message: \"请选择性别\", trigger: \"change\" }\n        ],\n        work_time: [\n          { required: true, message: \"请输入从业时间\", trigger: \"blur\" },\n          { type: \"number\", min: 0, max: 50, message: \"从业时间应在0-50年之间\", trigger: \"blur\" }\n        ],\n        height: [\n          { pattern: /^\\d{2,3}$/, message: \"请输入正确的身高(cm)\", trigger: \"blur\" }\n        ],\n        weight: [\n          { pattern: /^\\d{2,3}(\\.\\d)?$/, message: \"请输入正确的体重(kg)\", trigger: \"blur\" }\n        ],\n        id_card: [\n          { pattern: /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: \"请输入正确的身份证号\", trigger: \"blur\" }\n        ],\n        service_price: [\n          { type: \"number\", min: 0, message: \"服务价格不能小于0\", trigger: \"blur\" }\n        ],\n        car_price: [\n          { type: \"number\", min: 0, message: \"车费不能小于0\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        uniacid: 1,\n        coach_name: null,\n        user_id: null,\n        mobile: null,\n        id_card: null,\n        sex: 1,\n        work_time: null,\n        city: null,\n        lng: null,\n        lat: null,\n        address: null,\n        text: null,\n        license: null,\n        work_img: null,\n        status: 1,\n        auth_status: 0,\n        sh_time: null,\n        sh_text: null,\n        create_time: Math.floor(Date.now() / 1000),\n        self_img: null,\n        is_work: 1,\n        start_time: null,\n        end_time: null,\n        service_price: null,\n        car_price: null,\n        id_code: null,\n        star: 5.0,\n        admin_add: 1,\n        city_id: null,\n        video: null,\n        is_update: 0,\n        integral: 0,\n        order_num: 0,\n        recommend: 0,\n        balance_cash: 0,\n        index_top: 0,\n        coach_position: 0,\n        near_time: null,\n        check_cash: 0,\n        agent_type: 1,\n        partner_id: null,\n        partner_time: null,\n        total_order_num: 0,\n        birthday: null,\n        credit_value: 100,\n        credit_top: 0,\n        recommend_icon: null,\n        free_fare_distance: 0,\n        show_salenum: 1,\n        coach_icon: null,\n        true_lng: null,\n        true_lat: null,\n        type_id: null,\n        version: 1,\n        true_user_name: null,\n        industry_type: null,\n        height: null,\n        weight: null,\n        constellation: null,\n        free_fare_bear: 0,\n        personality_icon: null,\n        station_icon: null,\n        address_update_time: null,\n        pv: 0,\n        virtual_collect: 0,\n        virtual_comment: 0,\n        work_type: 1,\n        data: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push(\"/massage/coach\");\n    }\n  }\n};\n</script>\n"]}]}