{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753804856744}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENvYWNoLCBhZGRDb2FjaCwgdXBkYXRlQ29hY2ggfSBmcm9tICJAL2FwaS9tYXNzYWdlL2NvYWNoIjsKaW1wb3J0IHsKICBsaXN0Q29hY2hTZXJ2aWNlLAogIGxpc3RBdmFpbGFibGVTZXJ2aWNlLAogIGFkZENvYWNoU2VydmljZSwKICBkZWxDb2FjaFNlcnZpY2UsCiAgZ2V0Q29hY2hXb3JrVGltZSwKICBzYXZlQ29hY2hXb3JrVGltZSwKICBsaXN0Q29hY2hPcmRlcgp9IGZyb20gIkAvYXBpL21hc3NhZ2UvY29hY2hTZXJ2aWNlIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ29hY2hFZGl0IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5r+A5rS755qE5qCH562+6aG1CiAgICAgIGFjdGl2ZU5hbWU6ICJiYXNpYyIsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5o6l5Y2V5pe26Ze06KGo5Y2VCiAgICAgIHRpbWVGb3JtOiB7CiAgICAgICAgaXNfd29yazogMSwKICAgICAgICBzdGFydF90aW1lOiBudWxsLAogICAgICAgIGVuZF90aW1lOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHRydWVfdXNlcl9uYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55yf5a6e5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAxNSwgbWVzc2FnZTogIuWnk+WQjemVv+W6puWcqCAyIOWIsCAxNSDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgaWRfY2FyZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui6q+S7veivgeWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXlsxLTldXGR7NX0oMTh8MTl8MjApXGR7Mn0oKDBbMS05XSl8KDFbMC0yXSkpKChbMC0yXVsxLTldKXwxMHwyMHwzMHwzMSlcZHszfVswLTlYeF0kLywgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOi6q+S7veivgeWPtyIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB2aXJ0dWFsX2NvbGxlY3Q6IFsKICAgICAgICAgIHsgdHlwZTogIm51bWJlciIsIG1pbjogMCwgbWVzc2FnZTogIuiZmuaLn+aUtuiXj+mHj+S4jeiDveWwj+S6jjAiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdmlydHVhbF9jb21tZW50OiBbCiAgICAgICAgICB7IHR5cGU6ICJudW1iZXIiLCBtaW46IDAsIG1lc3NhZ2U6ICLomZrmi5/or4Torrrph4/kuI3og73lsI/kuo4wIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHB2OiBbCiAgICAgICAgICB7IHR5cGU6ICJudW1iZXIiLCBtaW46IDAsIG1lc3NhZ2U6ICLorr/pl67ph4/kuI3og73lsI/kuo4wIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOaOpeWNleaXtumXtOagoemqjAogICAgICB0aW1lUnVsZXM6IHsKICAgICAgICBpc193b3JrOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5piv5ZCm5o6l5Y2VIiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g5bey5YWz6IGU5pyN5YqhCiAgICAgIHNlcnZpY2VMaXN0OiBbXSwKICAgICAgc2VydmljZUxvYWRpbmc6IGZhbHNlLAogICAgICBzZXJ2aWNlVG90YWw6IDAsCiAgICAgIHNlcnZpY2VRdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGNvYWNoSWQ6IG51bGwKICAgICAgfSwKICAgICAgLy8g5Y+v5YWz6IGU5pyN5YqhCiAgICAgIHNob3dTZXJ2aWNlRGlhbG9nOiBmYWxzZSwKICAgICAgYXZhaWxhYmxlU2VydmljZUxpc3Q6IFtdLAogICAgICBhdmFpbGFibGVTZXJ2aWNlTG9hZGluZzogZmFsc2UsCiAgICAgIGF2YWlsYWJsZVNlcnZpY2VUb3RhbDogMCwKICAgICAgYXZhaWxhYmxlU2VydmljZVF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdGl0bGU6IG51bGwKICAgICAgfSwKICAgICAgc2VydmljZVNlYXJjaEZvcm06IHsKICAgICAgICB0aXRsZTogbnVsbAogICAgICB9LAogICAgICBzZWxlY3RlZFNlcnZpY2VzOiBbXSwKICAgICAgLy8g5pyN5Yqh6K6w5b2VCiAgICAgIG9yZGVyTGlzdDogW10sCiAgICAgIG9yZGVyTG9hZGluZzogZmFsc2UsCiAgICAgIG9yZGVyVG90YWw6IDAsCiAgICAgIG9yZGVyUXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBjb2FjaElkOiBudWxsCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgY29uc3QgaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsKICAgIHRoaXMuZm9ybS5pZCA9IGlkIHx8IG51bGw7CiAgICBpZiAoaWQpIHsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnovr7kuroiOwogICAgICB0aGlzLmdldEluZm8oaWQpOwogICAgICB0aGlzLnNlcnZpY2VRdWVyeVBhcmFtcy5jb2FjaElkID0gaWQ7CiAgICAgIHRoaXMub3JkZXJRdWVyeVBhcmFtcy5jb2FjaElkID0gaWQ7CiAgICAgIHRoaXMuZ2V0V29ya1RpbWUoaWQpOwogICAgICB0aGlzLmdldFNlcnZpY2VMaXN0KCk7CiAgICAgIHRoaXMuZ2V0T3JkZXJMaXN0KCk7CiAgICB9IGVsc2UgewogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinui+vuS6uiI7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lovr7kurror6bnu4YgKi8KICAgIGdldEluZm8oaWQpIHsKICAgICAgZ2V0Q29hY2goaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBjb2FjaF9uYW1lOiBudWxsLAogICAgICAgIG1vYmlsZTogbnVsbCwKICAgICAgICBzZXg6IDEsCiAgICAgICAgd29ya19pbWc6IG51bGwsCiAgICAgICAgYXZhdGFyVXJsOiBudWxsLCAvLyDlvq7kv6HlpLTlg48KICAgICAgICBjcmVhdGVfdGltZTogTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCksCiAgICAgICAgYmFsYW5jZV9jYXNoOiAwLAogICAgICAgIHRvdGFsX29yZGVyX251bTogMCwKICAgICAgICBzZXJ2aWNlX3ByaWNlOiAwLAogICAgICAgIGF1dGhfc3RhdHVzOiAwLAogICAgICAgIHN0YXR1czogMSwKICAgICAgICB0cnVlX3VzZXJfbmFtZTogbnVsbCwKICAgICAgICBpZF9jYXJkOiBudWxsLAogICAgICAgIGxpY2Vuc2U6IG51bGwsCiAgICAgICAgc2VsZl9pbWc6IG51bGwsCiAgICAgICAgdGV4dDogbnVsbCwKICAgICAgICB2aXJ0dWFsX2NvbGxlY3Q6IDAsCiAgICAgICAgdmlydHVhbF9jb21tZW50OiAwLAogICAgICAgIHB2OiAwCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVDb2FjaCh0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkQ29hY2godGhpcy5mb3JtKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLmdvQmFjaygpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDov5Tlm54gKi8KICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL21hc3NhZ2UvY29hY2giIH0pOwogICAgfSwKCiAgICAvKiog6I635Y+W5o6l5Y2V5pe26Ze0ICovCiAgICBnZXRXb3JrVGltZShpZCkgewogICAgICBnZXRDb2FjaFdvcmtUaW1lKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnRpbWVGb3JtID0gcmVzcG9uc2UuZGF0YSB8fCB7CiAgICAgICAgICBpc193b3JrOiAxLAogICAgICAgICAgc3RhcnRfdGltZTogIjA5OjAwIiwKICAgICAgICAgIGVuZF90aW1lOiAiMTg6MDAiCiAgICAgICAgfTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOWmguaenOiOt+WPluWksei0pe+8jOS9v+eUqOm7mOiupOWAvAogICAgICAgIHRoaXMudGltZUZvcm0gPSB7CiAgICAgICAgICBpc193b3JrOiAxLAogICAgICAgICAgc3RhcnRfdGltZTogIjA5OjAwIiwKICAgICAgICAgIGVuZF90aW1lOiAiMTg6MDAiCiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDkv53lrZjmjqXljZXml7bpl7QgKi8KICAgIHNhdmVXb3JrVGltZSgpIHsKICAgICAgdGhpcy4kcmVmc1sidGltZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBjb25zdCBkYXRhID0gewogICAgICAgICAgICBjb2FjaElkOiB0aGlzLmZvcm0uaWQsCiAgICAgICAgICAgIC4uLnRoaXMudGltZUZvcm0KICAgICAgICAgIH07CiAgICAgICAgICBzYXZlQ29hY2hXb3JrVGltZShkYXRhKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6l5Y2V5pe26Ze05L+d5a2Y5oiQ5YqfIik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog6I635Y+W5bey5YWz6IGU5pyN5Yqh5YiX6KGoICovCiAgICBnZXRTZXJ2aWNlTGlzdCgpIHsKICAgICAgdGhpcy5zZXJ2aWNlTG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RDb2FjaFNlcnZpY2UodGhpcy5zZXJ2aWNlUXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuc2VydmljZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMuc2VydmljZVRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5zZXJ2aWNlTG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5zZXJ2aWNlTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOiOt+WPluWPr+WFs+iBlOacjeWKoeWIl+ihqCAqLwogICAgZ2V0QXZhaWxhYmxlU2VydmljZUxpc3QoKSB7CiAgICAgIHRoaXMuYXZhaWxhYmxlU2VydmljZUxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0QXZhaWxhYmxlU2VydmljZSh0aGlzLmF2YWlsYWJsZVNlcnZpY2VRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5hdmFpbGFibGVTZXJ2aWNlTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy5hdmFpbGFibGVTZXJ2aWNlVG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmF2YWlsYWJsZVNlcnZpY2VMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmF2YWlsYWJsZVNlcnZpY2VMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog5pCc57Si5Y+v5YWz6IGU5pyN5YqhICovCiAgICBzZWFyY2hBdmFpbGFibGVTZXJ2aWNlcygpIHsKICAgICAgdGhpcy5hdmFpbGFibGVTZXJ2aWNlUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuYXZhaWxhYmxlU2VydmljZVF1ZXJ5UGFyYW1zLnRpdGxlID0gdGhpcy5zZXJ2aWNlU2VhcmNoRm9ybS50aXRsZTsKICAgICAgdGhpcy5nZXRBdmFpbGFibGVTZXJ2aWNlTGlzdCgpOwogICAgfSwKCiAgICAvKiog6YeN572u5pyN5Yqh5pCc57SiICovCiAgICByZXNldFNlcnZpY2VTZWFyY2goKSB7CiAgICAgIHRoaXMuc2VydmljZVNlYXJjaEZvcm0udGl0bGUgPSBudWxsOwogICAgICB0aGlzLmF2YWlsYWJsZVNlcnZpY2VRdWVyeVBhcmFtcy50aXRsZSA9IG51bGw7CiAgICAgIHRoaXMuZ2V0QXZhaWxhYmxlU2VydmljZUxpc3QoKTsKICAgIH0sCgogICAgLyoqIOacjeWKoemAieaLqeWPmOWMliAqLwogICAgaGFuZGxlU2VydmljZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5zZWxlY3RlZFNlcnZpY2VzID0gc2VsZWN0aW9uOwogICAgfSwKCiAgICAvKiog5re75Yqg5pyN5YqhICovCiAgICBhZGRTZXJ2aWNlcygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRTZXJ2aWNlcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6KaB5YWz6IGU55qE5pyN5YqhIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgY29hY2hJZDogdGhpcy5mb3JtLmlkLAogICAgICAgIHNlcnZpY2VJZHM6IHRoaXMuc2VsZWN0ZWRTZXJ2aWNlcy5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB9OwogICAgICBhZGRDb2FjaFNlcnZpY2UoZGF0YSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pyN5Yqh5YWz6IGU5oiQ5YqfIik7CiAgICAgICAgdGhpcy5zaG93U2VydmljZURpYWxvZyA9IGZhbHNlOwogICAgICAgIHRoaXMuZ2V0U2VydmljZUxpc3QoKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDnp7vpmaTmnI3liqEgKi8KICAgIHJlbW92ZVNlcnZpY2Uoc2VydmljZUlkKSB7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgeenu+mZpOivpeacjeWKoeWQl++8nycpLnRoZW4oKCkgPT4gewogICAgICAgIGRlbENvYWNoU2VydmljZSh0aGlzLmZvcm0uaWQsIHNlcnZpY2VJZCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmnI3liqHnp7vpmaTmiJDlip8iKTsKICAgICAgICAgIHRoaXMuZ2V0U2VydmljZUxpc3QoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDojrflj5bmnI3liqHorrDlvZXliJfooaggKi8KICAgIGdldE9yZGVyTGlzdCgpIHsKICAgICAgdGhpcy5vcmRlckxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Q29hY2hPcmRlcih0aGlzLm9yZGVyUXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMub3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLm9yZGVyVG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLm9yZGVyTG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5vcmRlckxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDojrflj5borqLljZXnirbmgIHnsbvlnosgKi8KICAgIGdldE9yZGVyU3RhdHVzVHlwZShzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgIDE6ICd3YXJuaW5nJywKICAgICAgICAyOiAnc3VjY2VzcycsCiAgICAgICAgMzogJ2RhbmdlcicsCiAgICAgICAgNDogJ2luZm8nCiAgICAgIH07CiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAnaW5mbyc7CiAgICB9LAoKICAgIC8qKiDojrflj5borqLljZXnirbmgIHmlofmnKwgKi8KICAgIGdldE9yZGVyU3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgIDE6ICflvoXmnI3liqEnLAogICAgICAgIDI6ICflt7LlrozmiJAnLAogICAgICAgIDM6ICflt7Llj5bmtognLAogICAgICAgIDQ6ICflt7LpgIDmrL4nCiAgICAgIH07CiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+lJzsKICAgIH0sCgogICAgLyoqIOiOt+WPluiupOivgeeKtuaAgeexu+WeiyAqLwogICAgZ2V0QXV0aFN0YXR1c1R5cGUoc3RhdHVzKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgMDogJ2luZm8nLCAgICAgLy8g5pyq6K6k6K+BCiAgICAgICAgMTogJ3dhcm5pbmcnLCAgLy8g6K6k6K+B5LitCiAgICAgICAgMjogJ3N1Y2Nlc3MnLCAgLy8g5bey6K6k6K+BCiAgICAgICAgMzogJ2RhbmdlcicgICAgLy8g6K6k6K+B5aSx6LSlCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKCiAgICAvKiog6I635Y+W6K6k6K+B54q25oCB5paH5pysICovCiAgICBnZXRBdXRoU3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3QgdGV4dE1hcCA9IHsKICAgICAgICAwOiAn5pyq6K6k6K+BJywKICAgICAgICAxOiAn6K6k6K+B5LitJywKICAgICAgICAyOiAn5bey6K6k6K+BJywKICAgICAgICAzOiAn6K6k6K+B5aSx6LSlJwogICAgICB9OwogICAgICByZXR1cm4gdGV4dE1hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOwogICAgfSwKCiAgICAvKiog6I635Y+W54q25oCB57G75Z6LICovCiAgICBnZXRTdGF0dXNUeXBlKHN0YXR1cykgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgIDE6ICd3YXJuaW5nJywgIC8vIOW+heWuoeaguAogICAgICAgIDI6ICdzdWNjZXNzJywgIC8vIOW3sumAmui/hwogICAgICAgIDM6ICdpbmZvJywgICAgIC8vIOW3suemgeeUqAogICAgICAgIDQ6ICdkYW5nZXInICAgIC8vIOW3sumps+WbngogICAgICB9OwogICAgICByZXR1cm4gdHlwZU1hcFtzdGF0dXNdIHx8ICdpbmZvJzsKICAgIH0sCgogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3QgdGV4dE1hcCA9IHsKICAgICAgICAxOiAn5b6F5a6h5qC4JywKICAgICAgICAyOiAn5bey6YCa6L+HJywKICAgICAgICAzOiAn5bey56aB55SoJywKICAgICAgICA0OiAn5bey6amz5ZueJwogICAgICB9OwogICAgICByZXR1cm4gdGV4dE1hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOwogICAgfSwKCiAgICAvKiog6I635Y+W5oCn5Yir5paH5pysICovCiAgICBnZXRTZXhUZXh0KHNleCkgewogICAgICBjb25zdCB0ZXh0TWFwID0gewogICAgICAgIDE6ICfnlLcnLAogICAgICAgIDI6ICflpbMnCiAgICAgIH07CiAgICAgIHJldHVybiB0ZXh0TWFwW3NleF0gfHwgJ+acquiuvue9ric7CiAgICB9LAoKICAgIC8qKiDorqHnrpfmnI3liqHml7bplb8gKi8KICAgIGNhbGN1bGF0ZVNlcnZpY2VUaW1lKCkgewogICAgICAvLyDov5nph4zlj6/ku6XmoLnmja7lrp7pmYXkuJrliqHpgLvovpHorqHnrpcKICAgICAgLy8g5pqC5pe26L+U5Zue5qih5ouf5pWw5o2uCiAgICAgIHJldHVybiAodGhpcy5mb3JtLnRvdGFsX29yZGVyX251bSB8fCAwKSAqIDEuNTsKICAgIH0sCgogICAgLyoqIOiuoeeul+WcqOe6v+aXtumVvyAqLwogICAgY2FsY3VsYXRlT25saW5lVGltZSgpIHsKICAgICAgLy8g6L+Z6YeM5Y+v5Lul5qC55o2u5a6e6ZmF5Lia5Yqh6YC76L6R6K6h566XCiAgICAgIC8vIOaaguaXtui/lOWbnuaooeaLn+aVsOaNrgogICAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcigoRGF0ZS5ub3coKSAvIDEwMDAgLSAodGhpcy5mb3JtLmNyZWF0ZV90aW1lIHx8IDApKSAvIDg2NDAwKTsKICAgICAgcmV0dXJuIE1hdGgubWF4KGRheXMgKiA4LCAwKTsKICAgIH0sCgogICAgLyoqIOiuoeeul+S4mue7qSAqLwogICAgY2FsY3VsYXRlUGVyZm9ybWFuY2UoKSB7CiAgICAgIC8vIOi/memHjOWPr+S7peagueaNruWunumZheS4muWKoemAu+i+keiuoeeulwogICAgICAvLyDmmoLml7bov5Tlm57mqKHmi5/mlbDmja4KICAgICAgcmV0dXJuICh0aGlzLmZvcm0udG90YWxfb3JkZXJfbnVtIHx8IDApICogKHRoaXMuZm9ybS5zZXJ2aWNlX3ByaWNlIHx8IDEwMCk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/massage/coach", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <!-- 达人基础信息展示 -->\n      <el-card class=\"coach-info-card\" v-if=\"form.id\" style=\"margin-bottom: 20px;\">\n        <div class=\"coach-header\">\n          <div class=\"coach-avatar\">\n            <img :src=\"form.avatarUrl || form.work_img\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover;\" v-if=\"form.avatarUrl || form.work_img\"/>\n            <div v-else class=\"default-avatar\">\n              <i class=\"el-icon-user\" style=\"font-size: 40px; color: #ccc;\"></i>\n            </div>\n          </div>\n          <div class=\"coach-basic-info\">\n            <div class=\"coach-name-row\">\n              <h3 class=\"coach-name\">{{ form.coach_name || '未设置' }}</h3>\n              <el-tag\n                :type=\"getAuthStatusType(form.auth_status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getAuthStatusText(form.auth_status) }}\n              </el-tag>\n              <el-tag\n                :type=\"getStatusType(form.status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getStatusText(form.status) }}\n              </el-tag>\n            </div>\n            <div class=\"coach-details\">\n              <p><span class=\"label\">ID：</span><span class=\"value\">{{ form.id }}</span></p>\n              <p><span class=\"label\">性别：</span><span class=\"value\">{{ getSexText(form.sex) }}</span></p>\n              <p><span class=\"label\">手机号：</span><span class=\"value\">{{ form.mobile || '未设置' }}</span></p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"coach-stats\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">申请时间</div>\n                <div class=\"stat-value\">{{ parseTime(form.create_time * 1000, '{y}-{m}-{d}') }}</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">账号余额</div>\n                <div class=\"stat-value\">{{ form.balance_cash || 0 }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">服务时长</div>\n                <div class=\"stat-value\">{{ calculateServiceTime() }}小时</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">在线时长</div>\n                <div class=\"stat-value\">{{ calculateOnlineTime() }}小时</div>\n              </div>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">业绩</div>\n                <div class=\"stat-value\">{{ calculatePerformance() }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否认证</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.auth_status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.auth_status == 2 ? '已认证' : '未认证' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否授权</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.status == 2 ? '已授权' : '未授权' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">总订单数</div>\n                <div class=\"stat-value\">{{ form.total_order_num || 0 }}单</div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-card>\n\n      <el-tabs v-model=\"activeName\" type=\"card\">\n        <!-- 基础信息 -->\n        <el-tab-pane label=\"基础信息\" name=\"basic\">\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"真实姓名\" prop=\"true_user_name\">\n              <el-input v-model=\"form.true_user_name\" maxlength=\"15\" show-word-limit placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证号\" prop=\"id_card\">\n              <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证照片\" prop=\"license\">\n              <image-upload v-model=\"form.license\" :limit=\"2\"/>\n              <div class=\"el-upload__tip\">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"生活照\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\" :limit=\"9\"/>\n              <div class=\"el-upload__tip\">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"达人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入达人简介\" />\n            </el-form-item>\n\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟收藏量\" prop=\"virtual_collect\">\n                  <el-input-number v-model=\"form.virtual_collect\" :min=\"0\" placeholder=\"请输入虚拟收藏量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟评论量\" prop=\"virtual_comment\">\n                  <el-input-number v-model=\"form.virtual_comment\" :min=\"0\" placeholder=\"请输入虚拟评论量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"访问量\" prop=\"pv\">\n                  <el-input-number v-model=\"form.pv\" :min=\"0\" placeholder=\"请输入访问量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n            <div style=\"text-align: center; margin-top: 20px;\">\n              <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n              <el-button @click=\"goBack\">取 消</el-button>\n            </div>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 接单时间 -->\n        <el-tab-pane label=\"接单时间\" name=\"workTime\" v-if=\"form.id\">\n          <el-form ref=\"timeForm\" :model=\"timeForm\" :rules=\"timeRules\" label-width=\"120px\">\n            <el-form-item label=\"是否接单\" prop=\"is_work\">\n              <el-radio-group v-model=\"timeForm.is_work\">\n                <el-radio :label=\"1\">接单</el-radio>\n                <el-radio :label=\"0\">休息</el-radio>\n              </el-radio-group>\n            </el-form-item>\n\n            <el-form-item label=\"接单时间\" prop=\"start_time\" v-if=\"timeForm.is_work\">\n              <div style=\"display: flex; align-items: center;\">\n                <el-time-picker\n                  v-model=\"timeForm.start_time\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n                <span style=\"margin: 0 10px;\">至</span>\n                <el-time-picker\n                  v-model=\"timeForm.end_time\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n              </div>\n            </el-form-item>\n\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"saveWorkTime\">保存接单时间</el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 已关联服务 -->\n        <el-tab-pane label=\"已关联服务\" name=\"services\" v-if=\"form.id\">\n          <div style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" @click=\"showServiceDialog = true\">关联服务</el-button>\n          </div>\n\n          <el-table v-loading=\"serviceLoading\" :data=\"serviceList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n            <el-table-column prop=\"title\" label=\"服务名称\" />\n            <el-table-column prop=\"price\" label=\"服务价格\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"time_long\" label=\"服务时长\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.time_long }}分钟</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"danger\" @click=\"removeService(scope.row.id)\">移除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"serviceTotal > 0\"\n            :total=\"serviceTotal\"\n            :page.sync=\"serviceQueryParams.pageNum\"\n            :limit.sync=\"serviceQueryParams.pageSize\"\n            @pagination=\"getServiceList\"\n          />\n        </el-tab-pane>\n\n        <!-- 服务记录 -->\n        <el-tab-pane label=\"服务记录\" name=\"orders\" v-if=\"form.id\">\n          <el-table v-loading=\"orderLoading\" :data=\"orderList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"订单ID\" width=\"80\" />\n            <el-table-column prop=\"order_code\" label=\"订单号\" width=\"180\" />\n            <el-table-column prop=\"goods_info\" label=\"服务项目\" />\n            <el-table-column prop=\"pay_price\" label=\"订单金额\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.pay_price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"order_status\" label=\"订单状态\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getOrderStatusType(scope.row.order_status)\">\n                  {{ getOrderStatusText(scope.row.order_status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"create_time\" label=\"下单时间\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"orderTotal > 0\"\n            :total=\"orderTotal\"\n            :page.sync=\"orderQueryParams.pageNum\"\n            :limit.sync=\"orderQueryParams.pageSize\"\n            @pagination=\"getOrderList\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n\n    <!-- 关联服务对话框 -->\n    <el-dialog title=\"关联服务\" :visible.sync=\"showServiceDialog\" width=\"800px\" append-to-body>\n      <el-form :inline=\"true\" :model=\"serviceSearchForm\" class=\"demo-form-inline\">\n        <el-form-item label=\"服务名称\">\n          <el-input v-model=\"serviceSearchForm.title\" placeholder=\"请输入服务名称\" clearable />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"searchAvailableServices\">查询</el-button>\n          <el-button @click=\"resetServiceSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-table\n        ref=\"serviceTable\"\n        v-loading=\"availableServiceLoading\"\n        :data=\"availableServiceList\"\n        @selection-change=\"handleServiceSelectionChange\"\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n        <el-table-column prop=\"title\" label=\"服务名称\" />\n        <el-table-column prop=\"price\" label=\"价格\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.price }}元</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"time_long\" label=\"时长\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.time_long }}分钟</span>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"availableServiceTotal > 0\"\n        :total=\"availableServiceTotal\"\n        :page.sync=\"availableServiceQueryParams.pageNum\"\n        :limit.sync=\"availableServiceQueryParams.pageSize\"\n        @pagination=\"getAvailableServiceList\"\n      />\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showServiceDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addServices\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\nimport {\n  listCoachService,\n  listAvailableService,\n  addCoachService,\n  delCoachService,\n  getCoachWorkTime,\n  saveCoachWorkTime,\n  listCoachOrder\n} from \"@/api/massage/coachService\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 接单时间表单\n      timeForm: {\n        is_work: 1,\n        start_time: null,\n        end_time: null\n      },\n      // 表单校验\n      rules: {\n        true_user_name: [\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\n          { min: 2, max: 15, message: \"姓名长度在 2 到 15 个字符\", trigger: \"blur\" }\n        ],\n        id_card: [\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\n          { pattern: /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: \"请输入正确的身份证号\", trigger: \"blur\" }\n        ],\n        virtual_collect: [\n          { type: \"number\", min: 0, message: \"虚拟收藏量不能小于0\", trigger: \"blur\" }\n        ],\n        virtual_comment: [\n          { type: \"number\", min: 0, message: \"虚拟评论量不能小于0\", trigger: \"blur\" }\n        ],\n        pv: [\n          { type: \"number\", min: 0, message: \"访问量不能小于0\", trigger: \"blur\" }\n        ]\n      },\n      // 接单时间校验\n      timeRules: {\n        is_work: [\n          { required: true, message: \"请选择是否接单\", trigger: \"change\" }\n        ]\n      },\n      // 已关联服务\n      serviceList: [],\n      serviceLoading: false,\n      serviceTotal: 0,\n      serviceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      },\n      // 可关联服务\n      showServiceDialog: false,\n      availableServiceList: [],\n      availableServiceLoading: false,\n      availableServiceTotal: 0,\n      availableServiceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null\n      },\n      serviceSearchForm: {\n        title: null\n      },\n      selectedServices: [],\n      // 服务记录\n      orderList: [],\n      orderLoading: false,\n      orderTotal: 0,\n      orderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n      this.serviceQueryParams.coachId = id;\n      this.orderQueryParams.coachId = id;\n      this.getWorkTime(id);\n      this.getServiceList();\n      this.getOrderList();\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        coach_name: null,\n        mobile: null,\n        sex: 1,\n        work_img: null,\n        avatarUrl: null, // 微信头像\n        create_time: Math.floor(Date.now() / 1000),\n        balance_cash: 0,\n        total_order_num: 0,\n        service_price: 0,\n        auth_status: 0,\n        status: 1,\n        true_user_name: null,\n        id_card: null,\n        license: null,\n        self_img: null,\n        text: null,\n        virtual_collect: 0,\n        virtual_comment: 0,\n        pv: 0\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push({ path: \"/massage/coach\" });\n    },\n\n    /** 获取接单时间 */\n    getWorkTime(id) {\n      getCoachWorkTime(id).then(response => {\n        this.timeForm = response.data || {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      }).catch(() => {\n        // 如果获取失败，使用默认值\n        this.timeForm = {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      });\n    },\n\n    /** 保存接单时间 */\n    saveWorkTime() {\n      this.$refs[\"timeForm\"].validate(valid => {\n        if (valid) {\n          const data = {\n            coachId: this.form.id,\n            ...this.timeForm\n          };\n          saveCoachWorkTime(data).then(() => {\n            this.$modal.msgSuccess(\"接单时间保存成功\");\n          });\n        }\n      });\n    },\n\n    /** 获取已关联服务列表 */\n    getServiceList() {\n      this.serviceLoading = true;\n      listCoachService(this.serviceQueryParams).then(response => {\n        this.serviceList = response.rows;\n        this.serviceTotal = response.total;\n        this.serviceLoading = false;\n      }).catch(() => {\n        this.serviceLoading = false;\n      });\n    },\n\n    /** 获取可关联服务列表 */\n    getAvailableServiceList() {\n      this.availableServiceLoading = true;\n      listAvailableService(this.availableServiceQueryParams).then(response => {\n        this.availableServiceList = response.rows;\n        this.availableServiceTotal = response.total;\n        this.availableServiceLoading = false;\n      }).catch(() => {\n        this.availableServiceLoading = false;\n      });\n    },\n\n    /** 搜索可关联服务 */\n    searchAvailableServices() {\n      this.availableServiceQueryParams.pageNum = 1;\n      this.availableServiceQueryParams.title = this.serviceSearchForm.title;\n      this.getAvailableServiceList();\n    },\n\n    /** 重置服务搜索 */\n    resetServiceSearch() {\n      this.serviceSearchForm.title = null;\n      this.availableServiceQueryParams.title = null;\n      this.getAvailableServiceList();\n    },\n\n    /** 服务选择变化 */\n    handleServiceSelectionChange(selection) {\n      this.selectedServices = selection;\n    },\n\n    /** 添加服务 */\n    addServices() {\n      if (this.selectedServices.length === 0) {\n        this.$modal.msgError(\"请选择要关联的服务\");\n        return;\n      }\n      const data = {\n        coachId: this.form.id,\n        serviceIds: this.selectedServices.map(item => item.id)\n      };\n      addCoachService(data).then(() => {\n        this.$modal.msgSuccess(\"服务关联成功\");\n        this.showServiceDialog = false;\n        this.getServiceList();\n      });\n    },\n\n    /** 移除服务 */\n    removeService(serviceId) {\n      this.$modal.confirm('确认要移除该服务吗？').then(() => {\n        delCoachService(this.form.id, serviceId).then(() => {\n          this.$modal.msgSuccess(\"服务移除成功\");\n          this.getServiceList();\n        });\n      });\n    },\n\n    /** 获取服务记录列表 */\n    getOrderList() {\n      this.orderLoading = true;\n      listCoachOrder(this.orderQueryParams).then(response => {\n        this.orderList = response.rows;\n        this.orderTotal = response.total;\n        this.orderLoading = false;\n      }).catch(() => {\n        this.orderLoading = false;\n      });\n    },\n\n    /** 获取订单状态类型 */\n    getOrderStatusType(status) {\n      const statusMap = {\n        1: 'warning',\n        2: 'success',\n        3: 'danger',\n        4: 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n\n    /** 获取订单状态文本 */\n    getOrderStatusText(status) {\n      const statusMap = {\n        1: '待服务',\n        2: '已完成',\n        3: '已取消',\n        4: '已退款'\n      };\n      return statusMap[status] || '未知';\n    },\n\n    /** 获取认证状态类型 */\n    getAuthStatusType(status) {\n      const typeMap = {\n        0: 'info',     // 未认证\n        1: 'warning',  // 认证中\n        2: 'success',  // 已认证\n        3: 'danger'    // 认证失败\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取认证状态文本 */\n    getAuthStatusText(status) {\n      const textMap = {\n        0: '未认证',\n        1: '认证中',\n        2: '已认证',\n        3: '认证失败'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取状态类型 */\n    getStatusType(status) {\n      const typeMap = {\n        1: 'warning',  // 待审核\n        2: 'success',  // 已通过\n        3: 'info',     // 已禁用\n        4: 'danger'    // 已驳回\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const textMap = {\n        1: '待审核',\n        2: '已通过',\n        3: '已禁用',\n        4: '已驳回'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取性别文本 */\n    getSexText(sex) {\n      const textMap = {\n        1: '男',\n        2: '女'\n      };\n      return textMap[sex] || '未设置';\n    },\n\n    /** 计算服务时长 */\n    calculateServiceTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * 1.5;\n    },\n\n    /** 计算在线时长 */\n    calculateOnlineTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      const days = Math.floor((Date.now() / 1000 - (this.form.create_time || 0)) / 86400);\n      return Math.max(days * 8, 0);\n    },\n\n    /** 计算业绩 */\n    calculatePerformance() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * (this.form.service_price || 100);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.coach-info-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.coach-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20px;\n}\n\n.coach-avatar {\n  margin-right: 20px;\n}\n\n.coach-avatar img {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n\n.default-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.coach-basic-info {\n  flex: 1;\n}\n\n.coach-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.coach-name {\n  margin: 0;\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.coach-details p {\n  margin: 5px 0;\n  font-size: 14px;\n  color: #666;\n}\n\n.coach-details .label {\n  display: inline-block;\n  width: 60px;\n  color: #999;\n}\n\n.coach-details .value {\n  color: #333;\n  font-weight: 500;\n}\n\n.coach-stats {\n  border-top: 1px solid #f0f0f0;\n  padding-top: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n  background: #fafafa;\n  border-radius: 6px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n</style>\n"]}]}