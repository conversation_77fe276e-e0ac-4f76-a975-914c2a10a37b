{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753796095165}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENvYWNoLCBhZGRDb2FjaCwgdXBkYXRlQ29hY2ggfSBmcm9tICJAL2FwaS9tYXNzYWdlL2NvYWNoIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ29hY2hFZGl0IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5r+A5rS755qE5qCH562+6aG1CiAgICAgIGFjdGl2ZU5hbWU6ICJiYXNpYyIsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgY29hY2hfbmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui+vuS6uuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBtb2JpbGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYvmnLrlj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcGF0dGVybjogL14xWzN8NHw1fDZ8N3w4fDldWzAtOV1cZHs4fSQvLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwKICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIGNvbnN0IGlkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICB0aGlzLmZvcm0uaWQgPSBpZCB8fCBudWxsOwogICAgaWYgKGlkKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56L6+5Lq6IjsKICAgICAgdGhpcy5nZXRJbmZvKGlkKTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKe6L6+5Lq6IjsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoui+vuS6uuivpue7hiAqLwogICAgZ2V0SW5mbyhpZCkgewogICAgICBnZXRDb2FjaChpZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIGNvYWNoX25hbWU6IG51bGwsCiAgICAgICAgbW9iaWxlOiBudWxsLAogICAgICAgIHNleDogbnVsbCwKICAgICAgICB3b3JrX3RpbWU6IG51bGwsCiAgICAgICAgaWRfY2FyZDogbnVsbCwKICAgICAgICBjaXR5OiBudWxsLAogICAgICAgIGxuZzogbnVsbCwKICAgICAgICBsYXQ6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICB0ZXh0OiBudWxsLAogICAgICAgIGxpY2Vuc2U6IG51bGwsCiAgICAgICAgd29ya19pbWc6IG51bGwsCiAgICAgICAgc2VsZl9pbWc6IG51bGwsCiAgICAgICAgaXNfd29yazogMSwKICAgICAgICBzdGFydF90aW1lOiBudWxsLAogICAgICAgIGVuZF90aW1lOiBudWxsLAogICAgICAgIHNlcnZpY2VfcHJpY2U6IG51bGwsCiAgICAgICAgY2FyX3ByaWNlOiBudWxsLAogICAgICAgIHJlY29tbWVuZDogMCwKICAgICAgICBzdGFyOiA1LjAsCiAgICAgICAgdmlkZW86IG51bGwsCiAgICAgICAgdHJ1ZV9sbmc6IG51bGwsCiAgICAgICAgdHJ1ZV9sYXQ6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZUNvYWNoKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRDb2FjaCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOi/lOWbniAqLwogICAgZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL21hc3NhZ2UvY29hY2giKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/massage/coach", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-tabs v-model=\"activeName\" type=\"card\">\n          <el-tab-pane label=\"基础信息\" name=\"basic\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"达人姓名\" prop=\"coach_name\">\n                  <el-input v-model=\"form.coach_name\" placeholder=\"请输入达人姓名\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"手机号\" prop=\"mobile\">\n                  <el-input v-model=\"form.mobile\" placeholder=\"请输入手机号\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"性别\" prop=\"sex\">\n                  <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\n                    <el-option label=\"男\" :value=\"1\" />\n                    <el-option label=\"女\" :value=\"2\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"从业时间\" prop=\"work_time\">\n                  <el-input-number v-model=\"form.work_time\" :min=\"0\" :max=\"50\" placeholder=\"请输入从业年限\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"身份证号\" prop=\"id_card\">\n                  <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"城市\" prop=\"city\">\n                  <el-input v-model=\"form.city\" placeholder=\"请输入城市\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"服务地址\" prop=\"address\">\n              <el-input v-model=\"form.address\" placeholder=\"请输入服务地址\" />\n            </el-form-item>\n            <el-form-item label=\"个人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" placeholder=\"请输入个人简介\" />\n            </el-form-item>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"工作信息\" name=\"work\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否工作\" prop=\"is_work\">\n                  <el-select v-model=\"form.is_work\" placeholder=\"请选择工作状态\">\n                    <el-option label=\"工作中\" :value=\"1\" />\n                    <el-option label=\"休息中\" :value=\"0\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否推荐\" prop=\"recommend\">\n                  <el-select v-model=\"form.recommend\" placeholder=\"请选择是否推荐\">\n                    <el-option label=\"否\" :value=\"0\" />\n                    <el-option label=\"是\" :value=\"1\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"服务价格\" prop=\"service_price\">\n                  <el-input-number v-model=\"form.service_price\" :min=\"0\" :precision=\"2\" placeholder=\"请输入服务价格\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"车费\" prop=\"car_price\">\n                  <el-input-number v-model=\"form.car_price\" :min=\"0\" :precision=\"2\" placeholder=\"请输入车费\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开始时间\" prop=\"start_time\">\n                  <el-input v-model=\"form.start_time\" placeholder=\"请输入开始工作时间\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"结束时间\" prop=\"end_time\">\n                  <el-input v-model=\"form.end_time\" placeholder=\"请输入结束工作时间\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"图片信息\" name=\"images\">\n            <el-form-item label=\"工作照片\" prop=\"work_img\">\n              <image-upload v-model=\"form.work_img\"/>\n            </el-form-item>\n            <el-form-item label=\"个人照片\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\"/>\n            </el-form-item>\n            <el-form-item label=\"执照\" prop=\"license\">\n              <image-upload v-model=\"form.license\"/>\n            </el-form-item>\n            <el-form-item label=\"视频\" prop=\"video\">\n              <image-upload v-model=\"form.video\"/>\n            </el-form-item>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"位置信息\" name=\"location\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"经度\" prop=\"lng\">\n                  <el-input v-model=\"form.lng\" placeholder=\"请输入经度\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"纬度\" prop=\"lat\">\n                  <el-input v-model=\"form.lat\" placeholder=\"请输入纬度\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"真实经度\" prop=\"true_lng\">\n                  <el-input v-model=\"form.true_lng\" placeholder=\"请输入真实经度\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"真实纬度\" prop=\"true_lat\">\n                  <el-input v-model=\"form.true_lat\" placeholder=\"请输入真实纬度\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-tab-pane>\n        </el-tabs>\n        \n        <div style=\"text-align: center; margin-top: 20px;\">\n          <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n          <el-button @click=\"goBack\">取 消</el-button>\n        </div>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        coach_name: [\n          { required: true, message: \"达人姓名不能为空\", trigger: \"blur\" }\n        ],\n        mobile: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        coach_name: null,\n        mobile: null,\n        sex: null,\n        work_time: null,\n        id_card: null,\n        city: null,\n        lng: null,\n        lat: null,\n        address: null,\n        text: null,\n        license: null,\n        work_img: null,\n        self_img: null,\n        is_work: 1,\n        start_time: null,\n        end_time: null,\n        service_price: null,\n        car_price: null,\n        recommend: 0,\n        star: 5.0,\n        video: null,\n        true_lng: null,\n        true_lat: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push(\"/massage/coach\");\n    }\n  }\n};\n</script>\n"]}]}