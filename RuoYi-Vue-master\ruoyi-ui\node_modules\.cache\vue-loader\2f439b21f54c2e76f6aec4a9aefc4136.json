{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753761435959}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXJCYWxhbmNlTGlzdCB9IGZyb20gIkAvYXBpL21hc3NhZ2UvdXNlciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJlY2hhcmdlUmVjb3JkVGFiIiwKICBwcm9wczogewogICAgdXNlcklkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgdG90YWw6IDAsCiAgICAgIHJlY29yZExpc3Q6IFtdLAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsCiAgICAgICAgdHlwZTogbnVsbCwKICAgICAgICBhZGQ6IG51bGwsCiAgICAgICAgbWluQW1vdW50OiBudWxsLAogICAgICAgIG1heEFtb3VudDogbnVsbAogICAgICB9LAogICAgICB0b3RhbFJlY2hhcmdlOiAwLAogICAgICByZWNoYXJnZUNvdW50OiAwLAogICAgICBhdmdSZWNoYXJnZTogMCwKICAgICAgbGFzdFJlY2hhcmdlRGF0ZTogJy0nCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRTdGF0aXN0aWNzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywKICAgICAgICBpZDogdGhpcy51c2VySWQKICAgICAgfTsKICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgewogICAgICAgIHBhcmFtcy5zdGFydFRpbWUgPSB0aGlzLmRhdGVSYW5nZVswXTsKICAgICAgICBwYXJhbXMuZW5kVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOwogICAgICB9CgogICAgICBnZXRVc2VyQmFsYW5jZUxpc3QocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zb2xlLmxvZygn5L2Z6aKd6K6w5b2VQVBJ5ZON5bqUOicsIHJlc3BvbnNlKTsKICAgICAgICB0aGlzLnJlY29yZExpc3QgPSByZXNwb25zZS5yb3dzIHx8IFtdOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbCB8fCAwOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfkvZnpop3orrDlvZVBUEnplJnor686JywgZXJyb3IpOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRTdGF0aXN0aWNzKCkgewogICAgICBpZiAodGhpcy5yZWNvcmRMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDlj6rnu5/orqHlhYXlgLzorrDlvZXvvIhhZGQ9MeeahOiusOW9le+8iQogICAgICAgIGNvbnN0IHJlY2hhcmdlUmVjb3JkcyA9IHRoaXMucmVjb3JkTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmFkZCA9PT0gMSk7CiAgICAgICAgdGhpcy50b3RhbFJlY2hhcmdlID0gcmVjaGFyZ2VSZWNvcmRzLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyAocGFyc2VGbG9hdChpdGVtLnByaWNlKSB8fCAwKSwgMCk7CiAgICAgICAgdGhpcy5yZWNoYXJnZUNvdW50ID0gcmVjaGFyZ2VSZWNvcmRzLmxlbmd0aDsKICAgICAgICB0aGlzLmF2Z1JlY2hhcmdlID0gdGhpcy5yZWNoYXJnZUNvdW50ID4gMCA/ICh0aGlzLnRvdGFsUmVjaGFyZ2UgLyB0aGlzLnJlY2hhcmdlQ291bnQpIDogMDsKCiAgICAgICAgLy8g6I635Y+W5pyA6L+R5YWF5YC85pe26Ze0CiAgICAgICAgaWYgKHJlY2hhcmdlUmVjb3Jkcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBjb25zdCBsYXRlc3RSZWNoYXJnZSA9IHJlY2hhcmdlUmVjb3Jkcy5yZWR1Y2UoKGxhdGVzdCwgY3VycmVudCkgPT4gewogICAgICAgICAgICByZXR1cm4gKGN1cnJlbnQuY3JlYXRlX3RpbWUgPiBsYXRlc3QuY3JlYXRlX3RpbWUpID8gY3VycmVudCA6IGxhdGVzdDsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy5sYXN0UmVjaGFyZ2VEYXRlID0gdGhpcy5wYXJzZVRpbWUobGF0ZXN0UmVjaGFyZ2UuY3JlYXRlX3RpbWUgKiAxMDAwLCAne3l9LXttfS17ZH0nKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5sYXN0UmVjaGFyZ2VEYXRlID0gJy0nOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRvdGFsUmVjaGFyZ2UgPSAwOwogICAgICAgIHRoaXMucmVjaGFyZ2VDb3VudCA9IDA7CiAgICAgICAgdGhpcy5hdmdSZWNoYXJnZSA9IDA7CiAgICAgICAgdGhpcy5sYXN0UmVjaGFyZ2VEYXRlID0gJy0nOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOaPkOS+m+e7meeItue7hOS7tuiwg+eUqOeahOWIt+aWsOaWueazlQogICAgcmVmcmVzaCgpIHsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5L6b57uZ54i257uE5Lu26LCD55So55qE5Yi35paw5pa55rOVCiAgICByZWZyZXNoKCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["RechargeRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RechargeRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"recharge-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"记录类型\" prop=\"type\">\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择记录类型\" clearable>\n          <el-option label=\"用户充值\" value=\"1\" />\n          <el-option label=\"下单消费\" value=\"2\" />\n          <el-option label=\"订单退款\" value=\"3\" />\n          <el-option label=\"升级消费\" value=\"4\" />\n          <el-option label=\"管理员调整\" value=\"5\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"变动类型\" prop=\"add\">\n        <el-select v-model=\"queryParams.add\" placeholder=\"请选择变动类型\" clearable>\n          <el-option label=\"收入(+)\" value=\"1\" />\n          <el-option label=\"支出(-)\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"金额范围\">\n        <el-input-number v-model=\"queryParams.minAmount\" placeholder=\"最小金额\" :min=\"0\" :precision=\"2\" style=\"width: 120px;\"></el-input-number>\n        <span style=\"margin: 0 8px;\">-</span>\n        <el-input-number v-model=\"queryParams.maxAmount\" placeholder=\"最大金额\" :min=\"0\" :precision=\"2\" style=\"width: 120px;\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"时间范围\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalRecharge }}</div>\n            <div class=\"stat-label\">总充值金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ rechargeCount }}</div>\n            <div class=\"stat-label\">充值次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgRecharge }}</div>\n            <div class=\"stat-label\">平均充值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastRechargeDate }}</div>\n            <div class=\"stat-label\">最近充值</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 充值记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"control_name\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>\n          <span class=\"ml-md\">\n            <span :class=\"[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]\">\n              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}\n            </span>\n          </span>\n          ，现余额<span class=\"ml-sm c-success\">¥{{ scope.row.after_balance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"text\">\n        <template slot-scope=\"scope\">\n          <div class=\"ellipsis-2\" v-html=\"scope.row.text\"></div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserBalanceList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"RechargeRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        type: null,\n        add: null,\n        minAmount: null,\n        maxAmount: null\n      },\n      totalRecharge: 0,\n      rechargeCount: 0,\n      avgRecharge: 0,\n      lastRechargeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserBalanceList(params).then(response => {\n        console.log('余额记录API响应:', response);\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch((error) => {\n        console.error('余额记录API错误:', error);\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 只统计充值记录（add=1的记录）\n        const rechargeRecords = this.recordList.filter(item => item.add === 1);\n        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\n        this.rechargeCount = rechargeRecords.length;\n        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;\n\n        // 获取最近充值时间\n        if (rechargeRecords.length > 0) {\n          const latestRecharge = rechargeRecords.reduce((latest, current) => {\n            return (current.create_time > latest.create_time) ? current : latest;\n          });\n          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');\n        } else {\n          this.lastRechargeDate = '-';\n        }\n      } else {\n        this.totalRecharge = 0;\n        this.rechargeCount = 0;\n        this.avgRecharge = 0;\n        this.lastRechargeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.recharge-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}