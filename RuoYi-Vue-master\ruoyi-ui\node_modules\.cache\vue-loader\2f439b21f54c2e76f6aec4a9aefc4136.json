{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753763362810}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXJCYWxhbmNlTGlzdCB9IGZyb20gIkAvYXBpL21hc3NhZ2UvdXNlciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJlY2hhcmdlUmVjb3JkVGFiIiwKICBwcm9wczogewogICAgdXNlcklkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgdG90YWw6IDAsCiAgICAgIHJlY29yZExpc3Q6IFtdLAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsCiAgICAgICAgdHlwZTogbnVsbCwKICAgICAgICBhZGQ6IG51bGwKICAgICAgfSwKICAgICAgdG90YWxSZWNoYXJnZTogMCwKICAgICAgcmVjaGFyZ2VDb3VudDogMCwKICAgICAgYXZnUmVjaGFyZ2U6IDAsCiAgICAgIGxhc3RSZWNoYXJnZURhdGU6ICctJwogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW1zID0gewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsCiAgICAgICAgaWQ6IHRoaXMudXNlcklkCiAgICAgIH07CiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICBwYXJhbXMuc3RhcnRUaW1lID0gdGhpcy5kYXRlUmFuZ2VbMF07CiAgICAgICAgcGFyYW1zLmVuZFRpbWUgPSB0aGlzLmRhdGVSYW5nZVsxXTsKICAgICAgfQoKICAgICAgZ2V0VXNlckJhbGFuY2VMaXN0KHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc29sZS5sb2coJ+S9memineiusOW9lUFQSeWTjeW6lDonLCByZXNwb25zZSk7CiAgICAgICAgdGhpcy5yZWNvcmRMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKTsKICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5L2Z6aKd6K6w5b2VQVBJ6ZSZ6K+vOicsIGVycm9yKTsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0U3RhdGlzdGljcygpIHsKICAgICAgaWYgKHRoaXMucmVjb3JkTGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgLy8g5Y+q57uf6K6h5YWF5YC86K6w5b2V77yIYWRkPTHnmoTorrDlvZXvvIkKICAgICAgICBjb25zdCByZWNoYXJnZVJlY29yZHMgPSB0aGlzLnJlY29yZExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5hZGQgPT09IDEpOwogICAgICAgIHRoaXMudG90YWxSZWNoYXJnZSA9IHJlY2hhcmdlUmVjb3Jkcy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgKHBhcnNlRmxvYXQoaXRlbS5wcmljZSkgfHwgMCksIDApOwogICAgICAgIHRoaXMucmVjaGFyZ2VDb3VudCA9IHJlY2hhcmdlUmVjb3Jkcy5sZW5ndGg7CiAgICAgICAgdGhpcy5hdmdSZWNoYXJnZSA9IHRoaXMucmVjaGFyZ2VDb3VudCA+IDAgPyAodGhpcy50b3RhbFJlY2hhcmdlIC8gdGhpcy5yZWNoYXJnZUNvdW50KSA6IDA7CgogICAgICAgIC8vIOiOt+WPluacgOi/keWFheWAvOaXtumXtAogICAgICAgIGlmIChyZWNoYXJnZVJlY29yZHMubGVuZ3RoID4gMCkgewogICAgICAgICAgY29uc3QgbGF0ZXN0UmVjaGFyZ2UgPSByZWNoYXJnZVJlY29yZHMucmVkdWNlKChsYXRlc3QsIGN1cnJlbnQpID0+IHsKICAgICAgICAgICAgcmV0dXJuIChjdXJyZW50LmNyZWF0ZV90aW1lID4gbGF0ZXN0LmNyZWF0ZV90aW1lKSA/IGN1cnJlbnQgOiBsYXRlc3Q7CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMubGFzdFJlY2hhcmdlRGF0ZSA9IHRoaXMucGFyc2VUaW1lKGxhdGVzdFJlY2hhcmdlLmNyZWF0ZV90aW1lICogMTAwMCwgJ3t5fS17bX0te2R9Jyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMubGFzdFJlY2hhcmdlRGF0ZSA9ICctJzsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50b3RhbFJlY2hhcmdlID0gMDsKICAgICAgICB0aGlzLnJlY2hhcmdlQ291bnQgPSAwOwogICAgICAgIHRoaXMuYXZnUmVjaGFyZ2UgPSAwOwogICAgICAgIHRoaXMubGFzdFJlY2hhcmdlRGF0ZSA9ICctJzsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDmj5Dkvpvnu5nniLbnu4Tku7bosIPnlKjnmoTliLfmlrDmlrnms5UKICAgIHJlZnJlc2goKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaPkOS+m+e7meeItue7hOS7tuiwg+eUqOeahOWIt+aWsOaWueazlQogICAgcmVmcmVzaCgpIHsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["RechargeRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RechargeRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"recharge-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"记录类型\" prop=\"type\">\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择记录类型\" clearable>\n          <el-option label=\"用户充值\" value=\"1\" />\n          <el-option label=\"下单消费\" value=\"2\" />\n          <el-option label=\"订单退款\" value=\"3\" />\n          <el-option label=\"升级消费\" value=\"4\" />\n          <el-option label=\"管理员调整\" value=\"5\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"变动类型\" prop=\"add\">\n        <el-select v-model=\"queryParams.add\" placeholder=\"请选择变动类型\" clearable>\n          <el-option label=\"收入(+)\" value=\"1\" />\n          <el-option label=\"支出(-)\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"时间范围\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalRecharge }}</div>\n            <div class=\"stat-label\">总充值金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ rechargeCount }}</div>\n            <div class=\"stat-label\">充值次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgRecharge }}</div>\n            <div class=\"stat-label\">平均充值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastRechargeDate }}</div>\n            <div class=\"stat-label\">最近充值</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 充值记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"control_name\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>\n          <span class=\"ml-md\">\n            <span :class=\"[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]\">\n              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}\n            </span>\n          </span>\n          ，现余额<span class=\"ml-sm c-success\">¥{{ scope.row.after_balance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"text\">\n        <template slot-scope=\"scope\">\n          <div class=\"ellipsis-2\" v-html=\"scope.row.text\"></div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserBalanceList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"RechargeRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        type: null,\n        add: null\n      },\n      totalRecharge: 0,\n      rechargeCount: 0,\n      avgRecharge: 0,\n      lastRechargeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserBalanceList(params).then(response => {\n        console.log('余额记录API响应:', response);\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch((error) => {\n        console.error('余额记录API错误:', error);\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 只统计充值记录（add=1的记录）\n        const rechargeRecords = this.recordList.filter(item => item.add === 1);\n        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\n        this.rechargeCount = rechargeRecords.length;\n        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;\n\n        // 获取最近充值时间\n        if (rechargeRecords.length > 0) {\n          const latestRecharge = rechargeRecords.reduce((latest, current) => {\n            return (current.create_time > latest.create_time) ? current : latest;\n          });\n          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');\n        } else {\n          this.lastRechargeDate = '-';\n        }\n      } else {\n        this.totalRecharge = 0;\n        this.rechargeCount = 0;\n        this.avgRecharge = 0;\n        this.lastRechargeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.recharge-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}