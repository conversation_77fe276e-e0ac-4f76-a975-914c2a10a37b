{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753759739654}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RechargeRecordTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RechargeRecordTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"recharge-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"充值方式\" prop=\"payMethod\">\n        <el-select v-model=\"queryParams.payMethod\" placeholder=\"请选择充值方式\" clearable>\n          <el-option label=\"微信支付\" value=\"1\" />\n          <el-option label=\"支付宝\" value=\"2\" />\n          <el-option label=\"银行卡\" value=\"3\" />\n          <el-option label=\"现金\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"充值时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalRecharge }}</div>\n            <div class=\"stat-label\">总充值金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ rechargeCount }}</div>\n            <div class=\"stat-label\">充值次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgRecharge }}</div>\n            <div class=\"stat-label\">平均充值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastRechargeDate }}</div>\n            <div class=\"stat-label\">最近充值</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 充值记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"control_name\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>\n          <span class=\"ml-md\">\n            <span :class=\"[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]\">\n              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}\n            </span>\n          </span>\n          ，现余额<span class=\"ml-sm c-success\">¥{{ scope.row.after_balance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"text\">\n        <template slot-scope=\"scope\">\n          <div class=\"ellipsis-2\" v-html=\"scope.row.text\"></div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserBalanceList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"RechargeRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        payMethod: null\n      },\n      totalRecharge: 0,\n      rechargeCount: 0,\n      avgRecharge: 0,\n      lastRechargeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserBalanceList(params).then(response => {\n        console.log('余额记录API响应:', response);\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch((error) => {\n        console.error('余额记录API错误:', error);\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 只统计充值记录（add=1的记录）\n        const rechargeRecords = this.recordList.filter(item => item.add === 1);\n        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\n        this.rechargeCount = rechargeRecords.length;\n        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;\n\n        // 获取最近充值时间\n        if (rechargeRecords.length > 0) {\n          const latestRecharge = rechargeRecords.reduce((latest, current) => {\n            return (current.create_time > latest.create_time) ? current : latest;\n          });\n          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');\n        } else {\n          this.lastRechargeDate = '-';\n        }\n      } else {\n        this.totalRecharge = 0;\n        this.rechargeCount = 0;\n        this.avgRecharge = 0;\n        this.lastRechargeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.recharge-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}