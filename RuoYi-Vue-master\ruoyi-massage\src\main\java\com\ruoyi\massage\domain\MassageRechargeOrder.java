package com.ruoyi.massage.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 充值订单对象 ims_massage_service_balance_order_list
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageRechargeOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderCode;

    /** 交易流水号 */
    @Excel(name = "交易流水号")
    private String transactionId;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payPrice;

    /** 销售价格 */
    @Excel(name = "销售价格")
    private BigDecimal salePrice;

    /** 实际到账金额 */
    @Excel(name = "实际到账金额")
    private BigDecimal truePrice;

    /** 支付时间 */
    @Excel(name = "支付时间")
    private Long payTime;

    /** 订单状态(1待支付 2已支付) */
    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=已支付")
    private Integer status;

    /** 充值卡标题 */
    @Excel(name = "充值卡标题")
    private String title;

    /** 充值卡ID */
    @Excel(name = "充值卡ID")
    private Long cardId;

    /** 当前余额 */
    @Excel(name = "当前余额")
    private BigDecimal nowBalance;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }

    public void setTransactionId(String transactionId) 
    {
        this.transactionId = transactionId;
    }

    public String getTransactionId() 
    {
        return transactionId;
    }

    public void setPayPrice(BigDecimal payPrice) 
    {
        this.payPrice = payPrice;
    }

    public BigDecimal getPayPrice() 
    {
        return payPrice;
    }

    public void setSalePrice(BigDecimal salePrice) 
    {
        this.salePrice = salePrice;
    }

    public BigDecimal getSalePrice() 
    {
        return salePrice;
    }

    public void setTruePrice(BigDecimal truePrice) 
    {
        this.truePrice = truePrice;
    }

    public BigDecimal getTruePrice() 
    {
        return truePrice;
    }

    public void setPayTime(Long payTime) 
    {
        this.payTime = payTime;
    }

    public Long getPayTime() 
    {
        return payTime;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setCardId(Long cardId) 
    {
        this.cardId = cardId;
    }

    public Long getCardId() 
    {
        return cardId;
    }

    public void setNowBalance(BigDecimal nowBalance) 
    {
        this.nowBalance = nowBalance;
    }

    public BigDecimal getNowBalance() 
    {
        return nowBalance;
    }

    public void setCreateTimestamp(Long createTimestamp) 
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp() 
    {
        return createTimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("userId", getUserId())
            .append("orderCode", getOrderCode())
            .append("transactionId", getTransactionId())
            .append("payPrice", getPayPrice())
            .append("salePrice", getSalePrice())
            .append("truePrice", getTruePrice())
            .append("payTime", getPayTime())
            .append("status", getStatus())
            .append("title", getTitle())
            .append("cardId", getCardId())
            .append("nowBalance", getNowBalance())
            .append("createTimestamp", getCreateTimestamp())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
