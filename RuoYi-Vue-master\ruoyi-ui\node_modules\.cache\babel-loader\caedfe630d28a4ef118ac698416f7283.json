{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753761435959}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "name", "props", "userId", "type", "String", "Number", "required", "data", "loading", "showSearch", "total", "recordList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "add", "minAmount", "maxAmount", "totalRecharge", "rechargeCount", "avg<PERSON><PERSON>arge", "lastRechargeDate", "created", "getList", "getStatistics", "methods", "_defineProperty2", "default", "_this", "params", "_objectSpread2", "id", "length", "startTime", "endTime", "getUserBalanceList", "then", "response", "console", "log", "rows", "catch", "error", "rechargeRecords", "filter", "item", "reduce", "sum", "parseFloat", "price", "latestRecharge", "latest", "current", "create_time", "parseTime", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "refresh"], "sources": ["src/views/massage/user/components/RechargeRecordTab.vue"], "sourcesContent": ["<template>\n  <div class=\"recharge-record-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"记录类型\" prop=\"type\">\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择记录类型\" clearable>\n          <el-option label=\"用户充值\" value=\"1\" />\n          <el-option label=\"下单消费\" value=\"2\" />\n          <el-option label=\"订单退款\" value=\"3\" />\n          <el-option label=\"升级消费\" value=\"4\" />\n          <el-option label=\"管理员调整\" value=\"5\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"变动类型\" prop=\"add\">\n        <el-select v-model=\"queryParams.add\" placeholder=\"请选择变动类型\" clearable>\n          <el-option label=\"收入(+)\" value=\"1\" />\n          <el-option label=\"支出(-)\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"金额范围\">\n        <el-input-number v-model=\"queryParams.minAmount\" placeholder=\"最小金额\" :min=\"0\" :precision=\"2\" style=\"width: 120px;\"></el-input-number>\n        <span style=\"margin: 0 8px;\">-</span>\n        <el-input-number v-model=\"queryParams.maxAmount\" placeholder=\"最大金额\" :min=\"0\" :precision=\"2\" style=\"width: 120px;\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"时间范围\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ totalRecharge }}</div>\n            <div class=\"stat-label\">总充值金额</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ rechargeCount }}</div>\n            <div class=\"stat-label\">充值次数</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">¥{{ avgRecharge }}</div>\n            <div class=\"stat-label\">平均充值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastRechargeDate }}</div>\n            <div class=\"stat-label\">最近充值</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 充值记录表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"control_name\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}{{ scope.row.goods_title }}</div>\n          <span class=\"ml-md\">\n            <span :class=\"[{ 'c-link': scope.row.add }, { 'c-warning': !scope.row.add }]\">\n              {{ `${scope.row.add ? '+' : '-'} ¥${scope.row.price}` }}\n            </span>\n          </span>\n          ，现余额<span class=\"ml-sm c-success\">¥{{ scope.row.after_balance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"text\">\n        <template slot-scope=\"scope\">\n          <div class=\"ellipsis-2\" v-html=\"scope.row.text\"></div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserBalanceList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"RechargeRecordTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        type: null,\n        add: null,\n        minAmount: null,\n        maxAmount: null\n      },\n      totalRecharge: 0,\n      rechargeCount: 0,\n      avgRecharge: 0,\n      lastRechargeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserBalanceList(params).then(response => {\n        console.log('余额记录API响应:', response);\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch((error) => {\n        console.error('余额记录API错误:', error);\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 只统计充值记录（add=1的记录）\n        const rechargeRecords = this.recordList.filter(item => item.add === 1);\n        this.totalRecharge = rechargeRecords.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\n        this.rechargeCount = rechargeRecords.length;\n        this.avgRecharge = this.rechargeCount > 0 ? (this.totalRecharge / this.rechargeCount) : 0;\n\n        // 获取最近充值时间\n        if (rechargeRecords.length > 0) {\n          const latestRecharge = rechargeRecords.reduce((latest, current) => {\n            return (current.create_time > latest.create_time) ? current : latest;\n          });\n          this.lastRechargeDate = this.parseTime(latestRecharge.create_time * 1000, '{y}-{m}-{d}');\n        } else {\n          this.lastRechargeDate = '-';\n        }\n      } else {\n        this.totalRecharge = 0;\n        this.rechargeCount = 0;\n        this.avgRecharge = 0;\n        this.lastRechargeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.recharge-record-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAqHA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,UAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAb,MAAA,OAAAA,MAAA;QACAC,IAAA;QACAa,GAAA;QACAC,SAAA;QACAC,SAAA;MACA;MACAC,aAAA;MACAC,aAAA;MACAC,WAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA,MAAAC,gBAAA,CAAAC,OAAA;IACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MACA,KAAArB,OAAA;MACA,IAAAsB,MAAA,OAAAC,cAAA,CAAAH,OAAA,MAAAG,cAAA,CAAAH,OAAA,MACA,KAAAf,WAAA;QACAmB,EAAA,OAAA9B;MAAA,EACA;MACA,SAAAU,SAAA,SAAAA,SAAA,CAAAqB,MAAA;QACAH,MAAA,CAAAI,SAAA,QAAAtB,SAAA;QACAkB,MAAA,CAAAK,OAAA,QAAAvB,SAAA;MACA;MAEA,IAAAwB,wBAAA,EAAAN,MAAA,EAAAO,IAAA,WAAAC,QAAA;QACAC,OAAA,CAAAC,GAAA,eAAAF,QAAA;QACAT,KAAA,CAAAlB,UAAA,GAAA2B,QAAA,CAAAG,IAAA;QACAZ,KAAA,CAAAnB,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAmB,KAAA,CAAArB,OAAA;QACAqB,KAAA,CAAAJ,aAAA;MACA,GAAAiB,KAAA,WAAAC,KAAA;QACAJ,OAAA,CAAAI,KAAA,eAAAA,KAAA;QACAd,KAAA,CAAArB,OAAA;MACA;IACA;IACAiB,aAAA,WAAAA,cAAA;MACA,SAAAd,UAAA,CAAAsB,MAAA;QACA;QACA,IAAAW,eAAA,QAAAjC,UAAA,CAAAkC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA9B,GAAA;QAAA;QACA,KAAAG,aAAA,GAAAyB,eAAA,CAAAG,MAAA,WAAAC,GAAA,EAAAF,IAAA;UAAA,OAAAE,GAAA,IAAAC,UAAA,CAAAH,IAAA,CAAAI,KAAA;QAAA;QACA,KAAA9B,aAAA,GAAAwB,eAAA,CAAAX,MAAA;QACA,KAAAZ,WAAA,QAAAD,aAAA,YAAAD,aAAA,QAAAC,aAAA;;QAEA;QACA,IAAAwB,eAAA,CAAAX,MAAA;UACA,IAAAkB,cAAA,GAAAP,eAAA,CAAAG,MAAA,WAAAK,MAAA,EAAAC,OAAA;YACA,OAAAA,OAAA,CAAAC,WAAA,GAAAF,MAAA,CAAAE,WAAA,GAAAD,OAAA,GAAAD,MAAA;UACA;UACA,KAAA9B,gBAAA,QAAAiC,SAAA,CAAAJ,cAAA,CAAAG,WAAA;QACA;UACA,KAAAhC,gBAAA;QACA;MACA;QACA,KAAAH,aAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACAkC,WAAA,WAAAA,YAAA;MACA,KAAA3C,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACAiC,UAAA,WAAAA,WAAA;MACA,KAAA7C,SAAA;MACA,KAAA8C,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,OAAA,WAAAA,QAAA;MACA,KAAAnC,OAAA;IACA;EAAA,uBAAAmC,QAAA,EAEA;IACA,KAAAnC,OAAA;EACA;AAEA", "ignoreList": []}]}