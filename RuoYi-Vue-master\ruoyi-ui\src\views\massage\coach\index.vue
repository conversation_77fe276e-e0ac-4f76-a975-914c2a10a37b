<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="达人姓名" prop="coach_name">
        <el-input
          v-model="queryParams.coach_name"
          placeholder="请输入达人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="达人状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择达人状态" clearable>
          <el-option label="待审核" value="1" />
          <el-option label="正常" value="2" />
          <el-option label="禁用" value="3" />
          <el-option label="审核失败" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="认证状态" prop="auth_status">
        <el-select v-model="queryParams.auth_status" placeholder="请选择认证状态" clearable>
          <el-option label="未认证" value="0" />
          <el-option label="待审核" value="1" />
          <el-option label="已认证" value="2" />
          <el-option label="认证失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作状态" prop="is_work">
        <el-select v-model="queryParams.is_work" placeholder="请选择工作状态" clearable>
          <el-option label="工作中" value="1" />
          <el-option label="休息中" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="推荐状态" prop="recommend">
        <el-select v-model="queryParams.recommend" placeholder="请选择推荐状态" clearable>
          <el-option label="推荐" value="1" />
          <el-option label="普通" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['massage:coach:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleBatchEdit"
          v-hasPermi="['massage:coach:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['massage:coach:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="coachList" @selection-change="handleSelectionChange">
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="头像" align="center" prop="work_img" width="80">
        <template slot-scope="scope">
          <image-preview :src="scope.row.work_img" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="coach_name" width="120" />
      <el-table-column label="手机号" align="center" prop="mobile" width="120" />
      <el-table-column label="申请时间" align="center" prop="create_time" width="160">
        <template slot-scope="scope">
          <div>
            <p>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d}') }}</p>
            <p>{{ parseTime(scope.row.create_time * 1000, '{h}:{i}:{s}') }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否绑定" align="center" prop="user_id" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.user_id" type="success">已绑定</el-tag>
          <el-tag v-else type="info">未绑定</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="认证状态" align="center" prop="auth_status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auth_status == 0" type="info">未认证</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 2" type="success">已认证</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 3" type="danger">认证失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.status == 2" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status == 3" type="danger">禁用</el-tag>
          <el-tag v-else-if="scope.row.status == 4" type="info">审核失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="设为推荐" align="center" prop="recommend" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.recommend"
            :active-value="1"
            :inactive-value="0"
            @change="handleRecommendChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="编辑" align="center" width="80">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            plain
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['massage:coach:edit']"
          >编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="更多菜单" align="center" width="120" fixed="right">
        <template slot-scope="scope">
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button size="mini" type="danger" plain>
              更多菜单<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="auth" v-if="scope.row.status == 1 || scope.row.auth_status == 1" v-hasPermi="['massage:coach:audit']">授权达人</el-dropdown-item>
              <el-dropdown-item command="disable" v-if="scope.row.status == 2" v-hasPermi="['massage:coach:edit']">禁用达人</el-dropdown-item>
              <el-dropdown-item command="enable" v-if="scope.row.status == 3" v-hasPermi="['massage:coach:edit']">启用达人</el-dropdown-item>
              <el-dropdown-item command="delete" v-hasPermi="['massage:coach:remove']">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>

    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />





    <!-- 达人审核对话框 -->
    <el-dialog title="达人审核" :visible.sync="auditOpen" width="400px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="4">审核失败</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="sh_text">
          <el-input v-model="auditForm.sh_text" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAuditForm">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCoach, getCoach, delCoach, updateCoach, auditCoach } from "@/api/massage/coach";

export default {
  name: "Coach",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 达人表格数据
      coachList: [],

      auditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        coach_name: null,
        mobile: null,
        status: null,
        auth_status: null
      },
      // 表单参数
      form: {},
      auditForm: {},
      // 表单校验
      rules: {
        coach_name: [
          { required: true, message: "达人姓名不能为空", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      },
      auditRules: {
        status: [
          { required: true, message: "审核状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询达人列表 */
    getList() {
      this.loading = true;
      listCoach(this.queryParams).then(response => {
        this.coachList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/massage/coach/edit");
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      this.$router.push("/massage/coach/edit?id=" + row.id);
    },
    /** 批量编辑操作 */
    handleBatchEdit() {
      if (this.ids.length === 1) {
        this.$router.push("/massage/coach/edit?id=" + this.ids[0]);
      } else {
        this.$modal.msgError("请选择一条数据进行编辑");
      }
    },
    /** 推荐状态切换 */
    handleRecommendChange(row) {
      const text = row.recommend === 1 ? "推荐" : "取消推荐";
      this.$modal.confirm('确认要"' + text + '""' + row.coach_name + '"达人吗？').then(() => {
        return updateCoach({
          id: row.id,
          recommend: row.recommend
        });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.recommend = row.recommend === 1 ? 0 : 1;
      });
    },
    /** 更多菜单操作 */
    handleCommand(command, row) {
      switch (command) {
        case "auth":
          this.handleAuthCoach(row);
          break;
        case "disable":
          this.handleStatusChange(row, 3, "禁用");
          break;
        case "enable":
          this.handleStatusChange(row, 2, "启用");
          break;
        case "delete":
          this.handleDelete(row);
          break;
      }
    },
    /** 授权达人 */
    handleAuthCoach(row) {
      let statusText = "";
      let newStatus = 2;
      let newAuthStatus = 2;

      if (row.status == 1) {
        statusText = "通过达人审核";
      } else if (row.auth_status == 1) {
        statusText = "通过认证审核";
      }

      this.$modal.confirm('确认要"' + statusText + '""' + row.coach_name + '"达人吗？').then(() => {
        return updateCoach({
          id: row.id,
          status: newStatus,
          auth_status: newAuthStatus,
          sh_time: Math.floor(Date.now() / 1000),
          sh_text: "管理员授权通过"
        });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(statusText + "成功");
      });
    },

    /** 状态变更 */
    handleStatusChange(row, status, text) {
      this.$modal.confirm('确认要"' + text + '""' + row.coach_name + '"达人吗？').then(() => {
        return updateCoach({
          id: row.id,
          status: status
        });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除达人编号为"' + ids + '"的数据项？').then(function() {
        return delCoach(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },


    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除达人编号为"' + ids + '"的数据项？').then(function() {
        return delCoach(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 达人审核 */
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: null,
        sh_text: null
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAuditForm() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditCoach(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {};
    }
  }
};
</script>
