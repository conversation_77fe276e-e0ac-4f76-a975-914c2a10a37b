<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="达人姓名" prop="coach_name">
        <el-input
          v-model="queryParams.coach_name"
          placeholder="请输入达人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="达人状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择达人状态" clearable>
          <el-option label="待审核" value="1" />
          <el-option label="正常" value="2" />
          <el-option label="禁用" value="3" />
          <el-option label="审核失败" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="认证状态" prop="auth_status">
        <el-select v-model="queryParams.auth_status" placeholder="请选择认证状态" clearable>
          <el-option label="未认证" value="0" />
          <el-option label="待审核" value="1" />
          <el-option label="已认证" value="2" />
          <el-option label="认证失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作状态" prop="is_work">
        <el-select v-model="queryParams.is_work" placeholder="请选择工作状态" clearable>
          <el-option label="工作中" value="1" />
          <el-option label="休息中" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="推荐状态" prop="recommend">
        <el-select v-model="queryParams.recommend" placeholder="请选择推荐状态" clearable>
          <el-option label="推荐" value="1" />
          <el-option label="普通" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['massage:coach:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['massage:coach:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['massage:coach:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="coachList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="达人ID" align="center" prop="id" width="80" />
      <el-table-column label="头像" align="center" prop="work_img" width="80">
        <template slot-scope="scope">
          <image-preview :src="scope.row.work_img" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="达人姓名" align="center" prop="coach_name" width="120" />
      <el-table-column label="手机号" align="center" prop="mobile" width="120" />
      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sex == 1">男</el-tag>
          <el-tag v-else-if="scope.row.sex == 2" type="danger">女</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="从业时间" align="center" prop="work_time" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.work_time }}年</span>
        </template>
      </el-table-column>
      <el-table-column label="评分" align="center" prop="star" width="80">
        <template slot-scope="scope">
          <el-rate v-model="scope.row.star" disabled show-score text-color="#ff9900" score-template="{value}"></el-rate>
        </template>
      </el-table-column>
      <el-table-column label="工作状态" align="center" prop="is_work" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.is_work == 1" type="success">工作中</el-tag>
          <el-tag v-else type="info">休息中</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐" align="center" prop="recommend" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.recommend == 1" type="warning">推荐</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="达人状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.status == 2" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status == 3" type="danger">禁用</el-tag>
          <el-tag v-else-if="scope.row.status == 4" type="info">审核失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="认证状态" align="center" prop="auth_status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auth_status == 0" type="info">未认证</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 2" type="success">已认证</el-tag>
          <el-tag v-else-if="scope.row.auth_status == 3" type="danger">认证失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="create_time" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['massage:coach:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['massage:coach:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['massage:coach:audit']"
            v-if="scope.row.status == 1"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['massage:coach:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改达人对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="达人姓名" prop="coach_name">
              <el-input v-model="form.coach_name" placeholder="请输入达人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="从业时间" prop="work_time">
              <el-input-number v-model="form.work_time" :min="0" :max="50" placeholder="请输入从业年限" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="id_card">
              <el-input v-model="form.id_card" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="服务地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入服务地址" />
        </el-form-item>
        <el-form-item label="个人简介" prop="text">
          <el-input v-model="form.text" type="textarea" placeholder="请输入个人简介" />
        </el-form-item>
        <el-form-item label="工作照片" prop="work_img">
          <image-upload v-model="form.work_img"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否工作" prop="is_work">
              <el-select v-model="form.is_work" placeholder="请选择工作状态">
                <el-option label="工作中" :value="1" />
                <el-option label="休息中" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否推荐" prop="recommend">
              <el-select v-model="form.recommend" placeholder="请选择是否推荐">
                <el-option label="否" :value="0" />
                <el-option label="是" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务价格" prop="service_price">
              <el-input-number v-model="form.service_price" :min="0" :precision="2" placeholder="请输入服务价格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车费" prop="car_price">
              <el-input-number v-model="form.car_price" :min="0" :precision="2" placeholder="请输入车费" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 达人详情对话框 -->
    <el-dialog title="达人详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="达人ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="达人姓名">{{ detailForm.coach_name }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailForm.mobile }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          <el-tag v-if="detailForm.sex == 1">男</el-tag>
          <el-tag v-else-if="detailForm.sex == 2" type="danger">女</el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="从业时间">{{ detailForm.work_time }}年</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ detailForm.id_card }}</el-descriptions-item>
        <el-descriptions-item label="服务地址">{{ detailForm.address }}</el-descriptions-item>
        <el-descriptions-item label="评分">
          <el-rate v-model="detailForm.star" disabled show-score text-color="#ff9900" score-template="{value}"></el-rate>
        </el-descriptions-item>
        <el-descriptions-item label="工作状态">
          <el-tag v-if="detailForm.is_work == 1" type="success">工作中</el-tag>
          <el-tag v-else type="info">休息中</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="推荐状态">
          <el-tag v-if="detailForm.recommend == 1" type="warning">推荐</el-tag>
          <el-tag v-else type="info">普通</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="服务价格">{{ detailForm.service_price }}元</el-descriptions-item>
        <el-descriptions-item label="车费">{{ detailForm.car_price }}元</el-descriptions-item>
        <el-descriptions-item label="订单数量">{{ detailForm.total_order_num || 0 }}</el-descriptions-item>
        <el-descriptions-item label="达人状态">
          <el-tag v-if="detailForm.status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="detailForm.status == 2" type="success">正常</el-tag>
          <el-tag v-else-if="detailForm.status == 3" type="danger">禁用</el-tag>
          <el-tag v-else-if="detailForm.status == 4" type="info">审核失败</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="认证状态">
          <el-tag v-if="detailForm.auth_status == 0" type="info">未认证</el-tag>
          <el-tag v-else-if="detailForm.auth_status == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="detailForm.auth_status == 2" type="success">已认证</el-tag>
          <el-tag v-else-if="detailForm.auth_status == 3" type="danger">认证失败</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">
          {{ parseTime(detailForm.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="审核时间" v-if="detailForm.sh_time">
          {{ parseTime(detailForm.sh_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="个人简介" :span="2">{{ detailForm.text }}</el-descriptions-item>
        <el-descriptions-item label="工作照片" :span="2">
          <image-preview :src="detailForm.work_img" :width="100" :height="100"/>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 达人审核对话框 -->
    <el-dialog title="达人审核" :visible.sync="auditOpen" width="400px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="4">审核失败</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="sh_text">
          <el-input v-model="auditForm.sh_text" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAuditForm">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCoach, getCoach, delCoach, addCoach, updateCoach, auditCoach } from "@/api/massage/coach";

export default {
  name: "Coach",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 达人表格数据
      coachList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      detailOpen: false,
      auditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        coach_name: null,
        mobile: null,
        status: null,
        auth_status: null
      },
      // 表单参数
      form: {},
      detailForm: {},
      auditForm: {},
      // 表单校验
      rules: {
        coach_name: [
          { required: true, message: "达人姓名不能为空", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      },
      auditRules: {
        status: [
          { required: true, message: "审核状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询达人列表 */
    getList() {
      this.loading = true;
      listCoach(this.queryParams).then(response => {
        this.coachList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        coach_name: null,
        mobile: null,
        sex: null,
        work_time: null,
        id_card: null,
        address: null,
        text: null,
        work_img: null,
        is_work: 1,
        recommend: 0,
        service_price: null,
        car_price: null,
        star: 5.0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加达人";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCoach(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改达人";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCoach(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCoach(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除达人编号为"' + ids + '"的数据项？').then(function() {
        return delCoach(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看详情 */
    handleView(row) {
      getCoach(row.id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 达人审核 */
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: null,
        sh_text: null
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAuditForm() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditCoach(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {};
    }
  }
};
</script>
