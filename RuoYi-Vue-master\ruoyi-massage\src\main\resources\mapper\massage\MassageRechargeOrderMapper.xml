<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageRechargeOrderMapper">
    
    <resultMap type="MassageRechargeOrder" id="MassageRechargeOrderResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="userId"    column="user_id"    />
        <result property="orderCode"    column="order_code"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="salePrice"    column="sale_price"    />
        <result property="truePrice"    column="true_price"    />
        <result property="payTime"    column="pay_time"    />
        <result property="status"    column="status"    />
        <result property="title"    column="title"    />
        <result property="cardId"    column="card_id"    />
        <result property="nowBalance"    column="now_balance"    />
        <result property="createTimestamp"    column="create_time"    />
    </resultMap>

    <sql id="selectMassageRechargeOrderVo">
        select id, uniacid, user_id, order_code, transaction_id, pay_price, sale_price, true_price, 
               pay_time, status, title, card_id, now_balance, create_time 
        from ims_massage_service_balance_order_list
    </sql>

    <select id="selectMassageRechargeOrderList" parameterType="MassageRechargeOrder" resultMap="MassageRechargeOrderResult">
        <include refid="selectMassageRechargeOrderVo"/>
        <where>  
            <if test="uniacid != null "> and uniacid = #{uniacid}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="orderCode != null  and orderCode != ''"> and order_code = #{orderCode}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="cardId != null "> and card_id = #{cardId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageRechargeOrderById" parameterType="Long" resultMap="MassageRechargeOrderResult">
        <include refid="selectMassageRechargeOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectMassageRechargeOrderByCode" parameterType="String" resultMap="MassageRechargeOrderResult">
        <include refid="selectMassageRechargeOrderVo"/>
        where order_code = #{orderCode}
    </select>
        
    <insert id="insertMassageRechargeOrder" parameterType="MassageRechargeOrder" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_service_balance_order_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">uniacid,</if>
            <if test="userId != null">user_id,</if>
            <if test="orderCode != null">order_code,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="truePrice != null">true_price,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="status != null">status,</if>
            <if test="title != null">title,</if>
            <if test="cardId != null">card_id,</if>
            <if test="nowBalance != null">now_balance,</if>
            <if test="createTimestamp != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">#{uniacid},</if>
            <if test="userId != null">#{userId},</if>
            <if test="orderCode != null">#{orderCode},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="salePrice != null">#{salePrice},</if>
            <if test="truePrice != null">#{truePrice},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="status != null">#{status},</if>
            <if test="title != null">#{title},</if>
            <if test="cardId != null">#{cardId},</if>
            <if test="nowBalance != null">#{nowBalance},</if>
            <if test="createTimestamp != null">#{createTimestamp},</if>
         </trim>
    </insert>

    <update id="updateMassageRechargeOrder" parameterType="MassageRechargeOrder">
        update ims_massage_service_balance_order_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderCode != null">order_code = #{orderCode},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="salePrice != null">sale_price = #{salePrice},</if>
            <if test="truePrice != null">true_price = #{truePrice},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="title != null">title = #{title},</if>
            <if test="cardId != null">card_id = #{cardId},</if>
            <if test="nowBalance != null">now_balance = #{nowBalance},</if>
            <if test="createTimestamp != null">create_time = #{createTimestamp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageRechargeOrderById" parameterType="Long">
        delete from ims_massage_service_balance_order_list where id = #{id}
    </delete>

    <delete id="deleteMassageRechargeOrderByIds" parameterType="String">
        delete from ims_massage_service_balance_order_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
