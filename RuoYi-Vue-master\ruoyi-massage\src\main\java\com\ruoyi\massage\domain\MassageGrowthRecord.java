package com.ruoyi.massage.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户成长值记录对象 ims_massage_service_growth_record
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageGrowthRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 成长值变动数量 */
    @Excel(name = "成长值变动数量")
    private Integer growth;

    /** 是否增加(1增加 0减少) */
    @Excel(name = "是否增加", readConverterExp = "1=增加,0=减少")
    private Integer isAdd;

    /** 变动前成长值 */
    @Excel(name = "变动前成长值")
    private Integer beforeGrowth;

    /** 变动后成长值 */
    @Excel(name = "变动后成长值")
    private Integer afterGrowth;

    /** 变动类型(1消费获得 2管理员调整 3系统扣除) */
    @Excel(name = "变动类型", readConverterExp = "1=消费获得,2=管理员调整,3=系统扣除")
    private Integer type;

    /** 关联订单ID */
    @Excel(name = "关联订单ID")
    private Long orderId;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUniacid(Integer uniacid)
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid()
    {
        return uniacid;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setGrowth(Integer growth) 
    {
        this.growth = growth;
    }

    public Integer getGrowth() 
    {
        return growth;
    }
    public void setIsAdd(Integer isAdd) 
    {
        this.isAdd = isAdd;
    }

    public Integer getIsAdd() 
    {
        return isAdd;
    }
    public void setBeforeGrowth(Integer beforeGrowth) 
    {
        this.beforeGrowth = beforeGrowth;
    }

    public Integer getBeforeGrowth() 
    {
        return beforeGrowth;
    }
    public void setAfterGrowth(Integer afterGrowth) 
    {
        this.afterGrowth = afterGrowth;
    }

    public Integer getAfterGrowth() 
    {
        return afterGrowth;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId()
    {
        return orderId;
    }
    public void setCreateTimestamp(Long createTimestamp)
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp()
    {
        return createTimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("userId", getUserId())
            .append("growth", getGrowth())
            .append("isAdd", getIsAdd())
            .append("beforeGrowth", getBeforeGrowth())
            .append("afterGrowth", getAfterGrowth())
            .append("type", getType())
            .append("orderId", getOrderId())
            .append("createTimestamp", getCreateTimestamp())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
