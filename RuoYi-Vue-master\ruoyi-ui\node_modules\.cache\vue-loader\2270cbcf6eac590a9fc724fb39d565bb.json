{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue?vue&type=style&index=0&id=ddaf7906&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\edit.vue", "mtime": 1753804856744}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753582855261}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753582864848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753582856704}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jb2FjaC1pbmZvLWNhcmQgewogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KCi5jb2FjaC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmNvYWNoLWF2YXRhciB7CiAgbWFyZ2luLXJpZ2h0OiAyMHB4Owp9CgouY29hY2gtYXZhdGFyIGltZyB7CiAgd2lkdGg6IDgwcHg7CiAgaGVpZ2h0OiA4MHB4OwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBvYmplY3QtZml0OiBjb3ZlcjsKfQoKLmRlZmF1bHQtYXZhdGFyIHsKICB3aWR0aDogODBweDsKICBoZWlnaHQ6IDgwcHg7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgouY29hY2gtYmFzaWMtaW5mbyB7CiAgZmxleDogMTsKfQoKLmNvYWNoLW5hbWUtcm93IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLmNvYWNoLW5hbWUgewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDIwcHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICMzMzM7Cn0KCi5jb2FjaC1kZXRhaWxzIHAgewogIG1hcmdpbjogNXB4IDA7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjNjY2Owp9CgouY29hY2gtZGV0YWlscyAubGFiZWwgewogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB3aWR0aDogNjBweDsKICBjb2xvcjogIzk5OTsKfQoKLmNvYWNoLWRldGFpbHMgLnZhbHVlIHsKICBjb2xvcjogIzMzMzsKICBmb250LXdlaWdodDogNTAwOwp9CgouY29hY2gtc3RhdHMgewogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOwogIHBhZGRpbmctdG9wOiAyMHB4Owp9Cgouc3RhdC1pdGVtIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogMTBweDsKICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwogIGJvcmRlci1yYWRpdXM6IDZweDsKfQoKLnN0YXQtbGFiZWwgewogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzk5OTsKICBtYXJnaW4tYm90dG9tOiA1cHg7Cn0KCi5zdGF0LXZhbHVlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICMzMzM7Cn0K"}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmrBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/massage/coach", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card>\n      <div slot=\"header\" class=\"clearfix\">\n        <span>{{ title }}</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回</el-button>\n      </div>\n      \n      <!-- 达人基础信息展示 -->\n      <el-card class=\"coach-info-card\" v-if=\"form.id\" style=\"margin-bottom: 20px;\">\n        <div class=\"coach-header\">\n          <div class=\"coach-avatar\">\n            <img :src=\"form.avatarUrl || form.work_img\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover;\" v-if=\"form.avatarUrl || form.work_img\"/>\n            <div v-else class=\"default-avatar\">\n              <i class=\"el-icon-user\" style=\"font-size: 40px; color: #ccc;\"></i>\n            </div>\n          </div>\n          <div class=\"coach-basic-info\">\n            <div class=\"coach-name-row\">\n              <h3 class=\"coach-name\">{{ form.coach_name || '未设置' }}</h3>\n              <el-tag\n                :type=\"getAuthStatusType(form.auth_status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getAuthStatusText(form.auth_status) }}\n              </el-tag>\n              <el-tag\n                :type=\"getStatusType(form.status)\"\n                size=\"small\"\n                style=\"margin-left: 10px;\"\n              >\n                {{ getStatusText(form.status) }}\n              </el-tag>\n            </div>\n            <div class=\"coach-details\">\n              <p><span class=\"label\">ID：</span><span class=\"value\">{{ form.id }}</span></p>\n              <p><span class=\"label\">性别：</span><span class=\"value\">{{ getSexText(form.sex) }}</span></p>\n              <p><span class=\"label\">手机号：</span><span class=\"value\">{{ form.mobile || '未设置' }}</span></p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"coach-stats\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">申请时间</div>\n                <div class=\"stat-value\">{{ parseTime(form.create_time * 1000, '{y}-{m}-{d}') }}</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">账号余额</div>\n                <div class=\"stat-value\">{{ form.balance_cash || 0 }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">服务时长</div>\n                <div class=\"stat-value\">{{ calculateServiceTime() }}小时</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">在线时长</div>\n                <div class=\"stat-value\">{{ calculateOnlineTime() }}小时</div>\n              </div>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">业绩</div>\n                <div class=\"stat-value\">{{ calculatePerformance() }}元</div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否认证</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.auth_status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.auth_status == 2 ? '已认证' : '未认证' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">是否授权</div>\n                <div class=\"stat-value\">\n                  <el-tag :type=\"form.status == 2 ? 'success' : 'info'\" size=\"mini\">\n                    {{ form.status == 2 ? '已授权' : '未授权' }}\n                  </el-tag>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"stat-item\">\n                <div class=\"stat-label\">总订单数</div>\n                <div class=\"stat-value\">{{ form.total_order_num || 0 }}单</div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-card>\n\n      <el-tabs v-model=\"activeName\" type=\"card\">\n        <!-- 基础信息 -->\n        <el-tab-pane label=\"基础信息\" name=\"basic\">\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"真实姓名\" prop=\"true_user_name\">\n              <el-input v-model=\"form.true_user_name\" maxlength=\"15\" show-word-limit placeholder=\"请输入真实姓名\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证号\" prop=\"id_card\">\n              <el-input v-model=\"form.id_card\" placeholder=\"请输入身份证号\" />\n            </el-form-item>\n\n            <el-form-item label=\"身份证照片\" prop=\"license\">\n              <image-upload v-model=\"form.license\" :limit=\"2\"/>\n              <div class=\"el-upload__tip\">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"生活照\" prop=\"self_img\">\n              <image-upload v-model=\"form.self_img\" :limit=\"9\"/>\n              <div class=\"el-upload__tip\">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>\n            </el-form-item>\n\n            <el-form-item label=\"达人简介\" prop=\"text\">\n              <el-input v-model=\"form.text\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入达人简介\" />\n            </el-form-item>\n\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟收藏量\" prop=\"virtual_collect\">\n                  <el-input-number v-model=\"form.virtual_collect\" :min=\"0\" placeholder=\"请输入虚拟收藏量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"虚拟评论量\" prop=\"virtual_comment\">\n                  <el-input-number v-model=\"form.virtual_comment\" :min=\"0\" placeholder=\"请输入虚拟评论量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"访问量\" prop=\"pv\">\n                  <el-input-number v-model=\"form.pv\" :min=\"0\" placeholder=\"请输入访问量\" style=\"width: 100%\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n            <div style=\"text-align: center; margin-top: 20px;\">\n              <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n              <el-button @click=\"goBack\">取 消</el-button>\n            </div>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 接单时间 -->\n        <el-tab-pane label=\"接单时间\" name=\"workTime\" v-if=\"form.id\">\n          <el-form ref=\"timeForm\" :model=\"timeForm\" :rules=\"timeRules\" label-width=\"120px\">\n            <el-form-item label=\"是否接单\" prop=\"is_work\">\n              <el-radio-group v-model=\"timeForm.is_work\">\n                <el-radio :label=\"1\">接单</el-radio>\n                <el-radio :label=\"0\">休息</el-radio>\n              </el-radio-group>\n            </el-form-item>\n\n            <el-form-item label=\"接单时间\" prop=\"start_time\" v-if=\"timeForm.is_work\">\n              <div style=\"display: flex; align-items: center;\">\n                <el-time-picker\n                  v-model=\"timeForm.start_time\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n                <span style=\"margin: 0 10px;\">至</span>\n                <el-time-picker\n                  v-model=\"timeForm.end_time\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  style=\"width: 150px\"\n                ></el-time-picker>\n              </div>\n            </el-form-item>\n\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"saveWorkTime\">保存接单时间</el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <!-- 已关联服务 -->\n        <el-tab-pane label=\"已关联服务\" name=\"services\" v-if=\"form.id\">\n          <div style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" @click=\"showServiceDialog = true\">关联服务</el-button>\n          </div>\n\n          <el-table v-loading=\"serviceLoading\" :data=\"serviceList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n            <el-table-column prop=\"title\" label=\"服务名称\" />\n            <el-table-column prop=\"price\" label=\"服务价格\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"time_long\" label=\"服务时长\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.time_long }}分钟</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"danger\" @click=\"removeService(scope.row.id)\">移除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"serviceTotal > 0\"\n            :total=\"serviceTotal\"\n            :page.sync=\"serviceQueryParams.pageNum\"\n            :limit.sync=\"serviceQueryParams.pageSize\"\n            @pagination=\"getServiceList\"\n          />\n        </el-tab-pane>\n\n        <!-- 服务记录 -->\n        <el-tab-pane label=\"服务记录\" name=\"orders\" v-if=\"form.id\">\n          <el-table v-loading=\"orderLoading\" :data=\"orderList\" style=\"width: 100%\">\n            <el-table-column prop=\"id\" label=\"订单ID\" width=\"80\" />\n            <el-table-column prop=\"order_code\" label=\"订单号\" width=\"180\" />\n            <el-table-column prop=\"goods_info\" label=\"服务项目\" />\n            <el-table-column prop=\"pay_price\" label=\"订单金额\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.pay_price }}元</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"order_status\" label=\"订单状态\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getOrderStatusType(scope.row.order_status)\">\n                  {{ getOrderStatusText(scope.row.order_status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"create_time\" label=\"下单时间\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            v-show=\"orderTotal > 0\"\n            :total=\"orderTotal\"\n            :page.sync=\"orderQueryParams.pageNum\"\n            :limit.sync=\"orderQueryParams.pageSize\"\n            @pagination=\"getOrderList\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n\n    <!-- 关联服务对话框 -->\n    <el-dialog title=\"关联服务\" :visible.sync=\"showServiceDialog\" width=\"800px\" append-to-body>\n      <el-form :inline=\"true\" :model=\"serviceSearchForm\" class=\"demo-form-inline\">\n        <el-form-item label=\"服务名称\">\n          <el-input v-model=\"serviceSearchForm.title\" placeholder=\"请输入服务名称\" clearable />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"searchAvailableServices\">查询</el-button>\n          <el-button @click=\"resetServiceSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-table\n        ref=\"serviceTable\"\n        v-loading=\"availableServiceLoading\"\n        :data=\"availableServiceList\"\n        @selection-change=\"handleServiceSelectionChange\"\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column prop=\"id\" label=\"服务ID\" width=\"80\" />\n        <el-table-column prop=\"title\" label=\"服务名称\" />\n        <el-table-column prop=\"price\" label=\"价格\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.price }}元</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"time_long\" label=\"时长\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.time_long }}分钟</span>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"availableServiceTotal > 0\"\n        :total=\"availableServiceTotal\"\n        :page.sync=\"availableServiceQueryParams.pageNum\"\n        :limit.sync=\"availableServiceQueryParams.pageSize\"\n        @pagination=\"getAvailableServiceList\"\n      />\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showServiceDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addServices\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getCoach, addCoach, updateCoach } from \"@/api/massage/coach\";\nimport {\n  listCoachService,\n  listAvailableService,\n  addCoachService,\n  delCoachService,\n  getCoachWorkTime,\n  saveCoachWorkTime,\n  listCoachOrder\n} from \"@/api/massage/coachService\";\n\nexport default {\n  name: \"CoachEdit\",\n  data() {\n    return {\n      // 标题\n      title: \"\",\n      // 激活的标签页\n      activeName: \"basic\",\n      // 表单参数\n      form: {},\n      // 接单时间表单\n      timeForm: {\n        is_work: 1,\n        start_time: null,\n        end_time: null\n      },\n      // 表单校验\n      rules: {\n        true_user_name: [\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\n          { min: 2, max: 15, message: \"姓名长度在 2 到 15 个字符\", trigger: \"blur\" }\n        ],\n        id_card: [\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\n          { pattern: /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: \"请输入正确的身份证号\", trigger: \"blur\" }\n        ],\n        virtual_collect: [\n          { type: \"number\", min: 0, message: \"虚拟收藏量不能小于0\", trigger: \"blur\" }\n        ],\n        virtual_comment: [\n          { type: \"number\", min: 0, message: \"虚拟评论量不能小于0\", trigger: \"blur\" }\n        ],\n        pv: [\n          { type: \"number\", min: 0, message: \"访问量不能小于0\", trigger: \"blur\" }\n        ]\n      },\n      // 接单时间校验\n      timeRules: {\n        is_work: [\n          { required: true, message: \"请选择是否接单\", trigger: \"change\" }\n        ]\n      },\n      // 已关联服务\n      serviceList: [],\n      serviceLoading: false,\n      serviceTotal: 0,\n      serviceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      },\n      // 可关联服务\n      showServiceDialog: false,\n      availableServiceList: [],\n      availableServiceLoading: false,\n      availableServiceTotal: 0,\n      availableServiceQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null\n      },\n      serviceSearchForm: {\n        title: null\n      },\n      selectedServices: [],\n      // 服务记录\n      orderList: [],\n      orderLoading: false,\n      orderTotal: 0,\n      orderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coachId: null\n      }\n    };\n  },\n  created() {\n    const id = this.$route.query && this.$route.query.id;\n    this.form.id = id || null;\n    if (id) {\n      this.title = \"修改达人\";\n      this.getInfo(id);\n      this.serviceQueryParams.coachId = id;\n      this.orderQueryParams.coachId = id;\n      this.getWorkTime(id);\n      this.getServiceList();\n      this.getOrderList();\n    } else {\n      this.title = \"新增达人\";\n      this.reset();\n    }\n  },\n  methods: {\n    /** 查询达人详细 */\n    getInfo(id) {\n      getCoach(id).then(response => {\n        this.form = response.data;\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        coach_name: null,\n        mobile: null,\n        sex: 1,\n        work_img: null,\n        avatarUrl: null, // 微信头像\n        create_time: Math.floor(Date.now() / 1000),\n        balance_cash: 0,\n        total_order_num: 0,\n        service_price: 0,\n        auth_status: 0,\n        status: 1,\n        true_user_name: null,\n        id_card: null,\n        license: null,\n        self_img: null,\n        text: null,\n        virtual_collect: 0,\n        virtual_comment: 0,\n        pv: 0\n      };\n      this.resetForm(\"form\");\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.goBack();\n            });\n          } else {\n            addCoach(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.goBack();\n            });\n          }\n        }\n      });\n    },\n    /** 返回 */\n    goBack() {\n      this.$router.push({ path: \"/massage/coach\" });\n    },\n\n    /** 获取接单时间 */\n    getWorkTime(id) {\n      getCoachWorkTime(id).then(response => {\n        this.timeForm = response.data || {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      }).catch(() => {\n        // 如果获取失败，使用默认值\n        this.timeForm = {\n          is_work: 1,\n          start_time: \"09:00\",\n          end_time: \"18:00\"\n        };\n      });\n    },\n\n    /** 保存接单时间 */\n    saveWorkTime() {\n      this.$refs[\"timeForm\"].validate(valid => {\n        if (valid) {\n          const data = {\n            coachId: this.form.id,\n            ...this.timeForm\n          };\n          saveCoachWorkTime(data).then(() => {\n            this.$modal.msgSuccess(\"接单时间保存成功\");\n          });\n        }\n      });\n    },\n\n    /** 获取已关联服务列表 */\n    getServiceList() {\n      this.serviceLoading = true;\n      listCoachService(this.serviceQueryParams).then(response => {\n        this.serviceList = response.rows;\n        this.serviceTotal = response.total;\n        this.serviceLoading = false;\n      }).catch(() => {\n        this.serviceLoading = false;\n      });\n    },\n\n    /** 获取可关联服务列表 */\n    getAvailableServiceList() {\n      this.availableServiceLoading = true;\n      listAvailableService(this.availableServiceQueryParams).then(response => {\n        this.availableServiceList = response.rows;\n        this.availableServiceTotal = response.total;\n        this.availableServiceLoading = false;\n      }).catch(() => {\n        this.availableServiceLoading = false;\n      });\n    },\n\n    /** 搜索可关联服务 */\n    searchAvailableServices() {\n      this.availableServiceQueryParams.pageNum = 1;\n      this.availableServiceQueryParams.title = this.serviceSearchForm.title;\n      this.getAvailableServiceList();\n    },\n\n    /** 重置服务搜索 */\n    resetServiceSearch() {\n      this.serviceSearchForm.title = null;\n      this.availableServiceQueryParams.title = null;\n      this.getAvailableServiceList();\n    },\n\n    /** 服务选择变化 */\n    handleServiceSelectionChange(selection) {\n      this.selectedServices = selection;\n    },\n\n    /** 添加服务 */\n    addServices() {\n      if (this.selectedServices.length === 0) {\n        this.$modal.msgError(\"请选择要关联的服务\");\n        return;\n      }\n      const data = {\n        coachId: this.form.id,\n        serviceIds: this.selectedServices.map(item => item.id)\n      };\n      addCoachService(data).then(() => {\n        this.$modal.msgSuccess(\"服务关联成功\");\n        this.showServiceDialog = false;\n        this.getServiceList();\n      });\n    },\n\n    /** 移除服务 */\n    removeService(serviceId) {\n      this.$modal.confirm('确认要移除该服务吗？').then(() => {\n        delCoachService(this.form.id, serviceId).then(() => {\n          this.$modal.msgSuccess(\"服务移除成功\");\n          this.getServiceList();\n        });\n      });\n    },\n\n    /** 获取服务记录列表 */\n    getOrderList() {\n      this.orderLoading = true;\n      listCoachOrder(this.orderQueryParams).then(response => {\n        this.orderList = response.rows;\n        this.orderTotal = response.total;\n        this.orderLoading = false;\n      }).catch(() => {\n        this.orderLoading = false;\n      });\n    },\n\n    /** 获取订单状态类型 */\n    getOrderStatusType(status) {\n      const statusMap = {\n        1: 'warning',\n        2: 'success',\n        3: 'danger',\n        4: 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n\n    /** 获取订单状态文本 */\n    getOrderStatusText(status) {\n      const statusMap = {\n        1: '待服务',\n        2: '已完成',\n        3: '已取消',\n        4: '已退款'\n      };\n      return statusMap[status] || '未知';\n    },\n\n    /** 获取认证状态类型 */\n    getAuthStatusType(status) {\n      const typeMap = {\n        0: 'info',     // 未认证\n        1: 'warning',  // 认证中\n        2: 'success',  // 已认证\n        3: 'danger'    // 认证失败\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取认证状态文本 */\n    getAuthStatusText(status) {\n      const textMap = {\n        0: '未认证',\n        1: '认证中',\n        2: '已认证',\n        3: '认证失败'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取状态类型 */\n    getStatusType(status) {\n      const typeMap = {\n        1: 'warning',  // 待审核\n        2: 'success',  // 已通过\n        3: 'info',     // 已禁用\n        4: 'danger'    // 已驳回\n      };\n      return typeMap[status] || 'info';\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const textMap = {\n        1: '待审核',\n        2: '已通过',\n        3: '已禁用',\n        4: '已驳回'\n      };\n      return textMap[status] || '未知';\n    },\n\n    /** 获取性别文本 */\n    getSexText(sex) {\n      const textMap = {\n        1: '男',\n        2: '女'\n      };\n      return textMap[sex] || '未设置';\n    },\n\n    /** 计算服务时长 */\n    calculateServiceTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * 1.5;\n    },\n\n    /** 计算在线时长 */\n    calculateOnlineTime() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      const days = Math.floor((Date.now() / 1000 - (this.form.create_time || 0)) / 86400);\n      return Math.max(days * 8, 0);\n    },\n\n    /** 计算业绩 */\n    calculatePerformance() {\n      // 这里可以根据实际业务逻辑计算\n      // 暂时返回模拟数据\n      return (this.form.total_order_num || 0) * (this.form.service_price || 100);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.coach-info-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.coach-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20px;\n}\n\n.coach-avatar {\n  margin-right: 20px;\n}\n\n.coach-avatar img {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n\n.default-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.coach-basic-info {\n  flex: 1;\n}\n\n.coach-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.coach-name {\n  margin: 0;\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.coach-details p {\n  margin: 5px 0;\n  font-size: 14px;\n  color: #666;\n}\n\n.coach-details .label {\n  display: inline-block;\n  width: 60px;\n  color: #999;\n}\n\n.coach-details .value {\n  color: #333;\n  font-weight: 500;\n}\n\n.coach-stats {\n  border-top: 1px solid #f0f0f0;\n  padding-top: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n  background: #fafafa;\n  border-radius: 6px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n</style>\n"]}]}