<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择用户状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入城市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
          <el-option label="未知" value="0" />
          <el-option label="男" value="1" />
          <el-option label="女" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['massage:user:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['massage:user:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['massage:user:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['massage:user:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="用户头像" align="center" prop="avatarUrl" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.avatarUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickName" width="120" />
      <el-table-column label="手机号" align="center" prop="phone" width="120" />
      <el-table-column label="会员等级" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '普通会员' : '已拉黑' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用户余额" align="center" prop="balance" width="100">
        <template slot-scope="scope">
          <span style="color: #f56c6c;">¥{{ scope.row.balance || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="现金余额" align="center" prop="cash" width="100">
        <template slot-scope="scope">
          <span style="color: #67c23a;">¥{{ scope.row.cash || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成长值" align="center" prop="growth" width="100" />
      <el-table-column label="消费总金额" align="center" width="120">
        <template slot-scope="scope">
          <span style="color: #409eff;">¥{{ scope.row.totalConsumption || 0 }}</span>
          <el-tooltip content="包含支付下单但尚未消费的金额" placement="top">
            <i class="el-icon-question" style="margin-left: 5px; color: #909399;"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="所在城市" align="center" prop="city" width="120" />
      <el-table-column label="注册时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime * 1000) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['massage:user:query']"
          >查看</el-button>
          <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)" trigger="click">
            <el-button size="mini" type="text">
              修改/充值<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="balance">修改余额</el-dropdown-item>
              <el-dropdown-item command="growth">修改成长</el-dropdown-item>
              <el-dropdown-item command="blacklist" divided>拉黑用户</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改按摩用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="国家" prop="country">
          <el-input v-model="form.country" placeholder="请输入国家" />
        </el-form-item>
        <el-form-item label="语言" prop="language">
          <el-input v-model="form.language" placeholder="请输入语言" />
        </el-form-item>
        <el-form-item label="地区" prop="area">
          <el-input v-model="form.area" placeholder="请输入地区" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="应用ID" prop="uniacid">
          <el-input v-model="form.uniacid" placeholder="请输入应用ID" />
        </el-form-item>
        <el-form-item label="来源类型" prop="sourceType">
          <el-input v-model="form.sourceType" placeholder="请输入来源类型" />
        </el-form-item>
        <el-form-item label="支付宝账号" prop="alipayNumber">
          <el-input v-model="form.alipayNumber" placeholder="请输入支付宝账号" />
        </el-form-item>
        <el-form-item label="支付宝姓名" prop="alipayName">
          <el-input v-model="form.alipayName" placeholder="请输入支付宝姓名" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 余额调整对话框 -->
    <el-dialog title="余额调整" :visible.sync="balanceOpen" width="400px" append-to-body>
      <el-form ref="balanceForm" :model="balanceForm" :rules="balanceRules" label-width="80px">
        <el-form-item label="当前余额">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ currentUser.balance || 0 }}</span>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input v-model="balanceForm.amount" placeholder="请输入调整金额（正数为增加，负数为减少）" />
        </el-form-item>
        <el-form-item label="调整备注" prop="remark">
          <el-input v-model="balanceForm.remark" type="textarea" placeholder="请输入调整备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBalanceForm">确 定</el-button>
        <el-button @click="cancelBalance">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 成长值调整对话框 -->
    <el-dialog title="成长值调整" :visible.sync="growthOpen" width="400px" append-to-body>
      <el-form ref="growthForm" :model="growthForm" :rules="growthRules" label-width="80px">
        <el-form-item label="当前成长值">
          <span style="color: #67c23a; font-weight: bold;">{{ currentUser.growth || 0 }}</span>
        </el-form-item>
        <el-form-item label="调整成长值" prop="growth">
          <el-input v-model="growthForm.growth" placeholder="请输入调整成长值（正数为增加，负数为减少）" />
        </el-form-item>
        <el-form-item label="调整备注" prop="remark">
          <el-input v-model="growthForm.remark" type="textarea" placeholder="请输入调整备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGrowthForm">确 定</el-button>
        <el-button @click="cancelGrowth">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, adjustBalance, adjustGrowth, changeStatus } from "@/api/massage/user";

export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 按摩用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 余额调整弹出层
      balanceOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        phone: null,
        status: null,
        city: null,
        gender: null
      },
      // 表单参数
      form: {},
      // 当前操作的用户
      currentUser: {},
      // 余额调整表单
      balanceForm: {},
      balanceOpen: false,
      // 成长值调整表单
      growthForm: {},
      growthOpen: false,
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      },
      // 余额调整校验
      balanceRules: {
        amount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "调整备注不能为空", trigger: "blur" }
        ]
      },
      // 成长值调整校验
      growthRules: {
        growth: [
          { required: true, message: "调整成长值不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "调整备注不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询按摩用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uniacid: null,
        openid: null,
        nickName: null,
        avatarUrl: null,
        createTime: null,
        status: 1,
        capId: null,
        city: null,
        country: null,
        gender: null,
        language: null,
        province: null,
        balance: 0,
        phone: null,
        sessionKey: null,
        pid: null,
        cash: 0,
        unionid: null,
        appOpenid: null,
        webOpenid: null,
        wechatOpenid: null,
        lastLoginType: null,
        newCash: null,
        lock: null,
        isFx: null,
        iosOpenid: null,
        pushId: null,
        alipayNumber: null,
        alipayName: null,
        ip: null,
        growth: 0,
        memberCalculateTime: null,
        userStatus: null,
        isQr: null,
        sourceType: null,
        area: null,
        adminId: null,
        isPopImg: null,
        memberDiscountTime: null,
        memberDiscountId: null,
        partnerMoney: null,
        totalPartnerMoney: null,
        delUserId: null,
        alipayIdCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加按摩用户";
    },
    /** 查看详情操作 */
    handleView(row) {
      this.$router.push({
        path: '/massage/user/detail/' + row.id
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改按摩用户";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除按摩用户编号为"' + ids + '"的数据项？').then(function() {
        return delUser(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('massage/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 用户状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.nickName + '"用户吗？').then(function() {
        return changeStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 1 ? 2 : 1;
      });
    },
    /** 下拉菜单命令处理 */
    handleDropdownCommand(command, row) {
      this.currentUser = row;
      switch (command) {
        case 'balance':
          this.handleAdjustBalance(row);
          break;
        case 'growth':
          this.handleAdjustGrowth(row);
          break;
        case 'blacklist':
          this.handleBlacklistUser(row);
          break;
      }
    },
    /** 余额调整 */
    handleAdjustBalance(row) {
      this.currentUser = row;
      this.balanceForm = {
        userId: row.id,
        amount: null,
        remark: null
      };
      this.balanceOpen = true;
    },
    /** 成长值调整 */
    handleAdjustGrowth(row) {
      this.currentUser = row;
      this.growthForm = {
        userId: row.id,
        growth: null,
        remark: null
      };
      this.growthOpen = true;
    },
    /** 拉黑用户 */
    handleBlacklistUser(row) {
      const text = row.status === 1 ? "拉黑" : "解除拉黑";
      const newStatus = row.status === 1 ? 2 : 1;
      this.$modal.confirm('确认要"' + text + '""' + row.nickName + '"用户吗？').then(function() {
        return changeStatus(row.id, newStatus);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
        this.getList();
      }).catch(function() {
        // 操作取消
      });
    },
    /** 提交余额调整 */
    submitBalanceForm() {
      this.$refs["balanceForm"].validate(valid => {
        if (valid) {
          adjustBalance(this.balanceForm).then(response => {
            this.$modal.msgSuccess("余额调整成功");
            this.balanceOpen = false;
            this.getList();
            // 发送余额调整成功事件
            this.$eventBus.$emit('userBalanceAdjusted', this.balanceForm.userId);
          });
        }
      });
    },
    /** 取消余额调整 */
    cancelBalance() {
      this.balanceOpen = false;
      this.balanceForm = {};
      this.currentUser = {};
    },
    /** 提交成长值调整 */
    submitGrowthForm() {
      this.$refs["growthForm"].validate(valid => {
        if (valid) {
          adjustGrowth(this.growthForm).then(response => {
            this.$modal.msgSuccess("成长值调整成功");
            this.growthOpen = false;
            this.getList();
            // 发送成长值调整成功事件
            this.$eventBus.$emit('userGrowthAdjusted', this.growthForm.userId);
          });
        }
      });
    },
    /** 取消成长值调整 */
    cancelGrowth() {
      this.growthOpen = false;
      this.growthForm = {};
      this.currentUser = {};
    }
  }
};
</script>

<style scoped>
.user-detail {
  padding: 20px;
}

.user-avatar {
  text-align: center;
  padding: 20px;
}

.user-avatar img {
  border: 3px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar h3 {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
}

.user-info {
  padding: 20px 0;
}

.el-descriptions {
  margin-top: 20px;
}

.el-descriptions-item__label {
  font-weight: 600;
  color: #606266;
}
</style>
