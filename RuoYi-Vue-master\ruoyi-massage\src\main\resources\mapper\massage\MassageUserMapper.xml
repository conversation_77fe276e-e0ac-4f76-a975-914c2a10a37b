<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageUserMapper">
    
    <resultMap type="MassageUser" id="MassageUserResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="openid"    column="openid"    />
        <result property="nickName"    column="nickName"    />
        <result property="avatarUrl"    column="avatarUrl"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="capId"    column="cap_id"    />
        <result property="city"    column="city"    />
        <result property="country"    column="country"    />
        <result property="gender"    column="gender"    />
        <result property="language"    column="language"    />
        <result property="province"    column="province"    />
        <result property="balance"    column="balance"    />
        <result property="phone"    column="phone"    />
        <result property="sessionKey"    column="session_key"    />
        <result property="pid"    column="pid"    />
        <result property="cash"    column="cash"    />
        <result property="unionid"    column="unionid"    />
        <result property="appOpenid"    column="app_openid"    />
        <result property="webOpenid"    column="web_openid"    />
        <result property="wechatOpenid"    column="wechat_openid"    />
        <result property="lastLoginType"    column="last_login_type"    />
        <result property="newCash"    column="new_cash"    />
        <result property="lock"    column="lock"    />
        <result property="isFx"    column="is_fx"    />
        <result property="iosOpenid"    column="ios_openid"    />
        <result property="pushId"    column="push_id"    />
        <result property="alipayNumber"    column="alipay_number"    />
        <result property="alipayName"    column="alipay_name"    />
        <result property="ip"    column="ip"    />
        <result property="growth"    column="growth"    />
        <result property="memberCalculateTime"    column="member_calculate_time"    />
        <result property="userStatus"    column="user_status"    />
        <result property="isQr"    column="is_qr"    />
        <result property="sourceType"    column="source_type"    />
        <result property="area"    column="area"    />
        <result property="adminId"    column="admin_id"    />
        <result property="isPopImg"    column="is_pop_img"    />
        <result property="memberDiscountTime"    column="member_discount_time"    />
        <result property="memberDiscountId"    column="member_discount_id"    />
        <result property="partnerMoney"    column="partner_money"    />
        <result property="totalPartnerMoney"    column="total_partner_money"    />
        <result property="delUserId"    column="del_user_id"    />
        <result property="alipayIdCode"    column="alipay_id_code"    />
    </resultMap>

    <sql id="selectMassageUserVo">
        select id, uniacid, openid, nickName, avatarUrl, create_time, status, cap_id, city, country, gender, language, province, balance, phone, session_key, pid, cash, unionid, app_openid, web_openid, wechat_openid, last_login_type, new_cash, `lock`, is_fx, ios_openid, push_id, alipay_number, alipay_name, ip, growth, member_calculate_time, user_status, is_qr, source_type, area, admin_id, is_pop_img, member_discount_time, member_discount_id, partner_money, total_partner_money, del_user_id, alipay_id_code from ims_massage_service_user_list
    </sql>

    <select id="selectMassageUserList" parameterType="MassageUser" resultMap="MassageUserResult">
        <include refid="selectMassageUserVo"/>
        <where>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="nickName != null  and nickName != ''"> and nickName like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageUserById" parameterType="Long" resultMap="MassageUserResult">
        <include refid="selectMassageUserVo"/>
        where id = #{id}
    </select>

    <select id="selectMassageUserByOpenid" parameterType="String" resultMap="MassageUserResult">
        <include refid="selectMassageUserVo"/>
        where openid = #{openid}
    </select>

    <select id="selectMassageUserByPhone" parameterType="String" resultMap="MassageUserResult">
        <include refid="selectMassageUserVo"/>
        where phone = #{phone}
    </select>
        
    <insert id="insertMassageUser" parameterType="MassageUser" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_service_user_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null">openid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="nickName != null">nickName,</if>
            <if test="avatarUrl != null">avatarUrl,</if>
            <if test="phone != null">phone,</if>
            <if test="gender != null">gender,</if>
            <if test="city != null">city,</if>
            <if test="country != null">country,</if>
            <if test="province != null">province,</if>
            <if test="balance != null">balance,</if>
            <if test="cash != null">cash,</if>
            <if test="growth != null">growth,</if>
            <if test="status != null">status,</if>
            <if test="lastLoginType != null">last_login_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="uniacid != null">uniacid,</if>
            <if test="language != null">language,</if>
            <if test="sessionKey != null">session_key,</if>
            <if test="pid != null">pid,</if>
            <if test="appOpenid != null">app_openid,</if>
            <if test="webOpenid != null">web_openid,</if>
            <if test="wechatOpenid != null">wechat_openid,</if>
            <if test="newCash != null">new_cash,</if>
            <if test="lock != null">`lock`,</if>
            <if test="isFx != null">is_fx,</if>
            <if test="iosOpenid != null">ios_openid,</if>
            <if test="pushId != null">push_id,</if>
            <if test="alipayNumber != null">alipay_number,</if>
            <if test="alipayName != null">alipay_name,</if>
            <if test="ip != null">ip,</if>
            <if test="memberCalculateTime != null">member_calculate_time,</if>
            <if test="userStatus != null">user_status,</if>
            <if test="isQr != null">is_qr,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="area != null">area,</if>
            <if test="adminId != null">admin_id,</if>
            <if test="isPopImg != null">is_pop_img,</if>
            <if test="memberDiscountTime != null">member_discount_time,</if>
            <if test="memberDiscountId != null">member_discount_id,</if>
            <if test="partnerMoney != null">partner_money,</if>
            <if test="totalPartnerMoney != null">total_partner_money,</if>
            <if test="delUserId != null">del_user_id,</if>
            <if test="alipayIdCode != null">alipay_id_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null">#{openid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="phone != null">#{phone},</if>
            <if test="gender != null">#{gender},</if>
            <if test="city != null">#{city},</if>
            <if test="country != null">#{country},</if>
            <if test="province != null">#{province},</if>
            <if test="balance != null">#{balance},</if>
            <if test="cash != null">#{cash},</if>
            <if test="growth != null">#{growth},</if>
            <if test="status != null">#{status},</if>
            <if test="lastLoginType != null">#{lastLoginType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="uniacid != null">#{uniacid},</if>
            <if test="language != null">#{language},</if>
            <if test="sessionKey != null">#{sessionKey},</if>
            <if test="pid != null">#{pid},</if>
            <if test="appOpenid != null">#{appOpenid},</if>
            <if test="webOpenid != null">#{webOpenid},</if>
            <if test="wechatOpenid != null">#{wechatOpenid},</if>
            <if test="newCash != null">#{newCash},</if>
            <if test="lock != null">#{lock},</if>
            <if test="isFx != null">#{isFx},</if>
            <if test="iosOpenid != null">#{iosOpenid},</if>
            <if test="pushId != null">#{pushId},</if>
            <if test="alipayNumber != null">#{alipayNumber},</if>
            <if test="alipayName != null">#{alipayName},</if>
            <if test="ip != null">#{ip},</if>
            <if test="memberCalculateTime != null">#{memberCalculateTime},</if>
            <if test="userStatus != null">#{userStatus},</if>
            <if test="isQr != null">#{isQr},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="area != null">#{area},</if>
            <if test="adminId != null">#{adminId},</if>
            <if test="isPopImg != null">#{isPopImg},</if>
            <if test="memberDiscountTime != null">#{memberDiscountTime},</if>
            <if test="memberDiscountId != null">#{memberDiscountId},</if>
            <if test="partnerMoney != null">#{partnerMoney},</if>
            <if test="totalPartnerMoney != null">#{totalPartnerMoney},</if>
            <if test="delUserId != null">#{delUserId},</if>
            <if test="alipayIdCode != null">#{alipayIdCode},</if>
         </trim>
    </insert>

    <update id="updateMassageUser" parameterType="MassageUser">
        update ims_massage_service_user_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="nickName != null">nickName = #{nickName},</if>
            <if test="avatarUrl != null">avatarUrl = #{avatarUrl},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="city != null">city = #{city},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="cash != null">cash = #{cash},</if>
            <if test="growth != null">growth = #{growth},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastLoginType != null">last_login_type = #{lastLoginType},</if>
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="language != null">language = #{language},</if>
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="appOpenid != null">app_openid = #{appOpenid},</if>
            <if test="webOpenid != null">web_openid = #{webOpenid},</if>
            <if test="wechatOpenid != null">wechat_openid = #{wechatOpenid},</if>
            <if test="newCash != null">new_cash = #{newCash},</if>
            <if test="lock != null">`lock` = #{lock},</if>
            <if test="isFx != null">is_fx = #{isFx},</if>
            <if test="iosOpenid != null">ios_openid = #{iosOpenid},</if>
            <if test="pushId != null">push_id = #{pushId},</if>
            <if test="alipayNumber != null">alipay_number = #{alipayNumber},</if>
            <if test="alipayName != null">alipay_name = #{alipayName},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="memberCalculateTime != null">member_calculate_time = #{memberCalculateTime},</if>
            <if test="userStatus != null">user_status = #{userStatus},</if>
            <if test="isQr != null">is_qr = #{isQr},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="area != null">area = #{area},</if>
            <if test="adminId != null">admin_id = #{adminId},</if>
            <if test="isPopImg != null">is_pop_img = #{isPopImg},</if>
            <if test="memberDiscountTime != null">member_discount_time = #{memberDiscountTime},</if>
            <if test="memberDiscountId != null">member_discount_id = #{memberDiscountId},</if>
            <if test="partnerMoney != null">partner_money = #{partnerMoney},</if>
            <if test="totalPartnerMoney != null">total_partner_money = #{totalPartnerMoney},</if>
            <if test="delUserId != null">del_user_id = #{delUserId},</if>
            <if test="alipayIdCode != null">alipay_id_code = #{alipayIdCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageUserById" parameterType="Long">
        delete from ims_massage_service_user_list where id = #{id}
    </delete>

    <delete id="deleteMassageUserByIds" parameterType="String">
        delete from ims_massage_service_user_list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计查询 -->
    <select id="getUserStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalUsers,
            COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers,
            COUNT(CASE WHEN DATE(FROM_UNIXTIME(create_time)) = CURDATE() THEN 1 END) as todayNewUsers,
            COALESCE(SUM(balance), 0) as totalBalance,
            COALESCE(SUM(total_consumption), 0) as totalConsumption
        FROM ims_massage_service_user_list
    </select>

    <select id="getTodayNewUsers" resultType="Long">
        SELECT COUNT(*) FROM ims_massage_service_user_list
        WHERE DATE(FROM_UNIXTIME(create_time)) = CURDATE()
    </select>

    <select id="getActiveUsers" resultType="Long">
        SELECT COUNT(*) FROM ims_massage_service_user_list
        WHERE status = 1
        <if test="days != null and days > 0">
            AND last_login_time &gt;= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL #{days} DAY))
        </if>
    </select>

    <select id="getUserGrowthTrend" resultType="map">
        SELECT 
            DATE(FROM_UNIXTIME(create_time)) as date,
            COUNT(*) as userCount
        FROM ims_massage_service_user_list
        <where>
            <if test="startDate != null and startDate != ''">
                AND DATE(FROM_UNIXTIME(create_time)) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(FROM_UNIXTIME(create_time)) &lt;= #{endDate}
            </if>
        </where>
        GROUP BY DATE(FROM_UNIXTIME(create_time))
        ORDER BY date
    </select>

    <select id="getUserCityDistribution" resultType="map">
        SELECT 
            city_name as name,
            COUNT(*) as value
        FROM ims_massage_service_user_list
        WHERE city_name IS NOT NULL AND city_name != ''
        GROUP BY city_name
        ORDER BY value DESC
        LIMIT 10
    </select>

    <select id="getUserGenderDistribution" resultType="map">
        SELECT 
            CASE gender 
                WHEN 1 THEN '男'
                WHEN 2 THEN '女'
                ELSE '未知'
            END as name,
            COUNT(*) as value
        FROM ims_massage_service_user_list
        GROUP BY gender
    </select>

    <select id="getUserAgeDistribution" resultType="map">
        SELECT 
            CASE
                WHEN YEAR(CURDATE()) - YEAR(birthday) &lt; 20 THEN '20岁以下'
                WHEN YEAR(CURDATE()) - YEAR(birthday) BETWEEN 20 AND 30 THEN '20-30岁'
                WHEN YEAR(CURDATE()) - YEAR(birthday) BETWEEN 31 AND 40 THEN '31-40岁'
                WHEN YEAR(CURDATE()) - YEAR(birthday) BETWEEN 41 AND 50 THEN '41-50岁'
                ELSE '50岁以上'
            END as name,
            COUNT(*) as value
        FROM ims_massage_service_user_list
        WHERE birthday IS NOT NULL
        GROUP BY name
    </select>

    <select id="getUserConsumptionStats" resultType="map">
        SELECT 
            COALESCE(SUM(total_consumption), 0) as totalConsumption,
            order_count as totalOrders,
            COALESCE(AVG(total_consumption), 0) as avgConsumption
        FROM ims_massage_service_user_list
        WHERE id = #{userId}
    </select>

    <update id="batchUpdateUserStatus">
        UPDATE ims_massage_service_user_list
        SET status = #{status}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取用户消费记录 -->
    <select id="selectUserOrderList" parameterType="Long" resultType="java.util.Map">
        select
            o.id,
            o.order_code,
            o.pay_price,
            CASE
                WHEN o.pay_type = 1 THEN '微信支付'
                WHEN o.pay_type = 2 THEN '支付宝支付'
                WHEN o.pay_type = 3 THEN '余额支付'
                WHEN o.pay_type = 4 THEN '线下支付'
                ELSE '其他支付'
            END as pay_type_text,
            o.pay_type,
            o.pay_time,
            o.create_time,
            o.pay_model,
            o.balance,
            o.is_add,
            1 as order_type,
            '按摩服务' as order_type_text,
            '' as coach_name,
            '' as goods_name
        from ims_massage_service_order_list o
        where o.user_id = #{userId}
        and o.pay_time > 0

        UNION ALL

        select
            r.id,
            r.order_code,
            r.pay_price,
            CASE
                WHEN r.pay_type = 1 THEN '微信支付'
                WHEN r.pay_type = 2 THEN '支付宝支付'
                WHEN r.pay_type = 3 THEN '余额支付'
                WHEN r.pay_type = 4 THEN '线下支付'
                ELSE '其他支付'
            END as pay_type_text,
            r.pay_type,
            r.pay_time,
            r.create_time,
            r.pay_model,
            0 as balance,
            1 as is_add,
            2 as order_type,
            '余额充值' as order_type_text,
            '' as coach_name,
            '' as goods_name
        from ims_massage_service_recharge_order r
        where r.user_id = #{userId}
        and r.pay_time > 0

        order by create_time desc
    </select>

    <!-- 获取用户成长值记录 -->
    <select id="selectUserGrowthList" parameterType="Long" resultType="java.util.Map">
        select
            r.id,
            '系统' as create_user,
            CASE
                WHEN r.type = 1 THEN '服务订单消费'
                WHEN r.type = 2 THEN '管理员调整'
                WHEN r.type = 3 THEN '系统降级扣除'
                WHEN r.type = 4 THEN '储值订单消费'
                ELSE '未知操作'
            END as type_text,
            CASE
                WHEN r.type = 1 AND r.order_id > 0 THEN CONCAT('订单号：', IFNULL(o.order_code, ''))
                WHEN r.type = 4 AND r.order_id > 0 THEN CONCAT('充值订单：', IFNULL(ro.order_code, ''))
                WHEN r.type = 2 THEN '管理员手动调整'
                WHEN r.type = 3 THEN '系统自动降级'
                ELSE '系统操作'
            END as remark,
            r.is_add,
            r.growth,
            r.after_growth,
            r.create_time
        from ims_massage_member_growth r
        left join ims_massage_service_order_list o on r.order_id = o.id and r.type = 1
        left join ims_massage_service_recharge_order ro on r.order_id = ro.id and r.type = 4
        where r.user_id = #{userId}
        order by r.create_time desc
    </select>

    <!-- 获取用户余额记录 -->
    <select id="selectUserBalanceList" parameterType="Long" resultType="java.util.Map">
        select
            r.id,
            '系统' as control_name,
            CASE
                WHEN r.type = 1 THEN '用户充值'
                WHEN r.type = 2 THEN '下单消费'
                WHEN r.type = 3 THEN '订单退款'
                WHEN r.type = 4 THEN '升级消费'
                WHEN r.type = 5 THEN '管理员调整'
                ELSE '其他操作'
            END as type_text,
            '' as goods_title,
            r.`add`,
            r.price,
            r.after_balance,
            CASE
                WHEN r.type = 1 THEN '用户充值到账'
                WHEN r.type = 2 THEN '订单消费扣减'
                WHEN r.type = 3 THEN '订单退款到账'
                WHEN r.type = 4 THEN '升级服务扣减'
                WHEN r.type = 5 THEN '管理员手动调整'
                ELSE '系统操作'
            END as text,
            r.create_time
        from ims_massage_service_balance_water r
        where r.user_id = #{userId}
        order by r.create_time desc
    </select>

    <!-- 获取用户充值记录 -->
    <select id="selectUserRechargeList" parameterType="Long" resultType="java.util.Map">
        select
            r.id,
            '系统' as control_name,
            CONCAT('充值: ', r.title) as type_text,
            1 as `add`,
            r.true_price as price,
            r.now_balance as after_balance,
            '' as text,
            r.create_time
        from ims_massage_service_balance_order_list r
        where r.user_id = #{userId}
        and r.status = 2
        order by r.create_time desc
    </select>

    <!-- 获取用户优惠券列表 -->
    <select id="selectUserCouponList" parameterType="Long" resultType="java.util.Map">
        select
            id,
            title,
            type,
            discount,
            full,
            create_time,
            end_time,
            status,
            use_time
        from ims_massage_service_coupon_record
        where user_id = #{userId}
        and is_show = 1
        order by create_time desc
    </select>

    <!-- 获取用户折扣卡列表 -->
    <select id="selectUserDiscountList" parameterType="Long" resultType="java.util.Map">
        select
            a.id,
            a.title,
            a.discount,
            a.cash,
            b.pay_price,
            a.create_time,
            a.over_time,
            a.status,
            CASE WHEN a.over_time <![CDATA[<]]> unix_timestamp() THEN 1 ELSE 0 END as is_over
        from ims_massage_balance_discount_user_card a
        left join ims_massage_balance_discount_order_list b on a.card_order_id = b.id
        where a.user_id = #{userId}
        and a.status = 1
        order by a.discount, a.cash, a.id desc
    </select>

    <!-- 获取用户屏蔽列表 -->
    <select id="selectUserBlockedList" parameterType="Long" resultType="java.util.Map">
        select
            a.id,
            a.user_id as blocked_user_id,
            b.nickName as nick_name,
            b.avatarUrl as avatar_url,
            a.create_time
        from ims_massage_shield_list a
        left join ims_massage_service_user_list b on a.user_id = b.id
        where a.coach_id = #{userId}
        and a.type = 3
        order by a.create_time desc
    </select>

    <!-- 添加屏蔽用户 -->
    <insert id="insertBlockedUser">
        insert into ims_massage_shield_list (coach_id, user_id, type, create_time)
        values (#{userId}, #{blockedUserId}, 3, unix_timestamp())
    </insert>

    <!-- 解除屏蔽用户 -->
    <delete id="deleteBlockedUser">
        delete from ims_massage_shield_list where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
