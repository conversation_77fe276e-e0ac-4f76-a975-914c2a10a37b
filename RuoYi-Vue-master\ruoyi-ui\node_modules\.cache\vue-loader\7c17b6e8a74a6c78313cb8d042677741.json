{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue?vue&type=style&index=0&id=364750cb&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue", "mtime": 1753764978961}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753582855261}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753582864848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753582856704}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJveC1jYXJkIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KLnRleHQuaXRlbSB7CiAgcGFkZGluZzogMTBweDsKfQoubnVtYmVyIHsKICBmb250LXNpemU6IDI0cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICM0MDlFRkY7CiAgbWFyZ2luLXRvcDogNXB4Owp9Cg=="}, {"version": 3, "sources": ["CommentListTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CommentListTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"评价星级\" prop=\"star\">\n        <el-select v-model=\"queryParams.star\" placeholder=\"请选择评价星级\" clearable>\n          <el-option label=\"5星\" value=\"5\" />\n          <el-option label=\"4星\" value=\"4\" />\n          <el-option label=\"3星\" value=\"3\" />\n          <el-option label=\"2星\" value=\"2\" />\n          <el-option label=\"1星\" value=\"1\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择评价状态\" clearable>\n          <el-option label=\"正常\" value=\"1\" />\n          <el-option label=\"隐藏\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"mb8\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>总评价数</span>\n            <div class=\"number\">{{ statistics.totalComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>平均星级</span>\n            <div class=\"number\">{{ statistics.avgStar }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评数(4-5星)</span>\n            <div class=\"number\">{{ statistics.goodComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评率</span>\n            <div class=\"number\">{{ statistics.goodRate }}%</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 评价列表 -->\n    <el-table v-loading=\"loading\" :data=\"commentList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"orderCode\" width=\"120\" />\n      <el-table-column label=\"评价星级\" align=\"center\" prop=\"star\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            disabled\n            show-score\n            text-color=\"#ff9900\"\n            score-template=\"{value}星\">\n          </el-rate>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"text\" :show-overflow-tooltip=\"true\" min-width=\"200\" />\n      <el-table-column label=\"评价标签\" align=\"center\" prop=\"lableList\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"tag in scope.row.lableList\"\n            :key=\"tag\"\n            size=\"mini\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"技师\" align=\"center\" prop=\"coachName\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.massage_comment_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:comment:query']\"\n          >详情</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['massage:comment:edit']\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 评价详情对话框 -->\n    <el-dialog title=\"评价详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\n      <el-form ref=\"detailForm\" :model=\"detailForm\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"订单编号\">\n              <span>{{ detailForm.orderCode }}</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"技师\">\n              <span>{{ detailForm.coachName }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价星级\">\n              <el-rate\n                v-model=\"detailForm.star\"\n                disabled\n                show-score\n                text-color=\"#ff9900\"\n                score-template=\"{value}星\">\n              </el-rate>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价时间\">\n              <span>{{ parseTime(detailForm.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"评价内容\">\n          <span>{{ detailForm.text }}</span>\n        </el-form-item>\n        <el-form-item label=\"评价标签\" v-if=\"detailForm.lableList && detailForm.lableList.length > 0\">\n          <el-tag\n            v-for=\"tag in detailForm.lableList\"\n            :key=\"tag\"\n            size=\"small\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"服务评价\" v-if=\"detailForm.serviceList && detailForm.serviceList.length > 0\">\n          <div v-for=\"service in detailForm.serviceList\" :key=\"service.serviceId\" style=\"margin-bottom: 10px;\">\n            <span>{{ service.serviceName }}：</span>\n            <el-rate\n              v-model=\"service.star\"\n              disabled\n              show-score\n              text-color=\"#ff9900\"\n              score-template=\"{value}星\">\n            </el-rate>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 编辑评价对话框 -->\n    <el-dialog title=\"编辑评价\" :visible.sync=\"editOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"editForm\" :model=\"editForm\" :rules=\"editRules\" label-width=\"80px\">\n        <el-form-item label=\"评价状态\" prop=\"status\">\n          <el-radio-group v-model=\"editForm.status\">\n            <el-radio :label=\"1\">正常</el-radio>\n            <el-radio :label=\"0\">隐藏</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"是否置顶\" prop=\"top\">\n          <el-radio-group v-model=\"editForm.top\">\n            <el-radio :label=\"1\">是</el-radio>\n            <el-radio :label=\"0\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitEdit\">确 定</el-button>\n        <el-button @click=\"editOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUserCommentList, updateComment } from \"@/api/massage/comment\";\n\nexport default {\n  name: \"CommentListTab\",\n  dicts: ['massage_comment_status'],\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 评价表格数据\n      commentList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      detailOpen: false,\n      editOpen: false,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        star: null,\n        status: null\n      },\n      // 表单参数\n      detailForm: {},\n      editForm: {},\n      // 表单校验\n      editRules: {\n        status: [\n          { required: true, message: \"评价状态不能为空\", trigger: \"change\" }\n        ]\n      },\n      // 统计信息\n      statistics: {\n        totalComments: 0,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询评价列表 */\n    getList() {\n      this.loading = true;\n      const params = this.addDateRange(this.queryParams, this.dateRange);\n      getUserCommentList(params).then(response => {\n        this.commentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n        this.getStatistics();\n      });\n    },\n    /** 计算统计信息 */\n    getStatistics() {\n      this.statistics = {\n        totalComments: this.commentList.length,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      };\n      \n      if (this.commentList.length > 0) {\n        const totalStar = this.commentList.reduce((sum, item) => sum + (item.star || 0), 0);\n        this.statistics.avgStar = (totalStar / this.commentList.length).toFixed(1);\n        this.statistics.goodComments = this.commentList.filter(item => item.star >= 4).length;\n        this.statistics.goodRate = ((this.statistics.goodComments / this.commentList.length) * 100).toFixed(1);\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 查看评价详情 */\n    handleView(row) {\n      this.detailForm = { ...row };\n      this.detailOpen = true;\n    },\n    /** 编辑评价 */\n    handleUpdate(row) {\n      this.editForm = {\n        id: row.id,\n        status: row.status,\n        top: row.top\n      };\n      this.editOpen = true;\n    },\n    /** 提交编辑 */\n    submitEdit() {\n      this.$refs[\"editForm\"].validate(valid => {\n        if (valid) {\n          updateComment(this.editForm).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.editOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  text-align: center;\n}\n.text.item {\n  padding: 10px;\n}\n.number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-top: 5px;\n}\n</style>\n"]}]}