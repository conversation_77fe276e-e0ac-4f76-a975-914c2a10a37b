package com.ruoyi.massage.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.massage.domain.MassageCoachWorkTime;
import com.ruoyi.massage.service.IMassageCoachWorkTimeService;

/**
 * 达人接单时间Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/massage/coachWorkTime")
public class MassageCoachWorkTimeController extends BaseController
{
    @Autowired
    private IMassageCoachWorkTimeService massageCoachWorkTimeService;

    /**
     * 获取达人接单时间
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:query')")
    @GetMapping("/{coachId}")
    public AjaxResult getInfo(@PathVariable("coachId") Long coachId)
    {
        return success(massageCoachWorkTimeService.selectMassageCoachWorkTimeByCoachId(coachId));
    }

    /**
     * 保存达人接单时间
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:edit')")
    @Log(title = "达人接单时间", businessType = BusinessType.UPDATE)
    @PostMapping
    public AjaxResult save(@RequestBody MassageCoachWorkTime massageCoachWorkTime)
    {
        return toAjax(massageCoachWorkTimeService.saveMassageCoachWorkTime(massageCoachWorkTime));
    }
}
