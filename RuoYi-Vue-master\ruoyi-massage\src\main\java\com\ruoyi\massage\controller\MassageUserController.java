package com.ruoyi.massage.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.massage.domain.MassageUser;
import com.ruoyi.massage.service.IMassageUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 按摩用户Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RestController
@RequestMapping("/massage/user")
public class MassageUserController extends BaseController
{
    @Autowired
    private IMassageUserService massageUserService;

    /**
     * 查询按摩用户列表
     */
    @PreAuthorize("@ss.hasPermi('massage:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(MassageUser massageUser)
    {
        startPage();
        List<MassageUser> list = massageUserService.selectMassageUserList(massageUser);
        return getDataTable(list);
    }

    /**
     * 导出按摩用户列表
     */
    @PreAuthorize("@ss.hasPermi('massage:user:export')")
    @Log(title = "按摩用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MassageUser massageUser)
    {
        List<MassageUser> list = massageUserService.selectMassageUserList(massageUser);
        ExcelUtil<MassageUser> util = new ExcelUtil<MassageUser>(MassageUser.class);
        util.exportExcel(response, list, "按摩用户数据");
    }

    /**
     * 获取按摩用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(massageUserService.selectMassageUserById(id));
    }

    /**
     * 新增按摩用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:add')")
    @Log(title = "按摩用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MassageUser massageUser)
    {
        return toAjax(massageUserService.insertMassageUser(massageUser));
    }

    /**
     * 修改按摩用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "按摩用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MassageUser massageUser)
    {
        return toAjax(massageUserService.updateMassageUser(massageUser));
    }

    /**
     * 删除按摩用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:remove')")
    @Log(title = "按摩用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(massageUserService.deleteMassageUserByIds(ids));
    }

    /**
     * 禁用/启用用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "用户状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MassageUser massageUser)
    {
        return toAjax(massageUserService.updateMassageUser(massageUser));
    }

    /**
     * 用户余额调整
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "用户余额调整", businessType = BusinessType.UPDATE)
    @PostMapping("/adjustBalance")
    public AjaxResult adjustBalance(@RequestBody Map<String, Object> params)
    {
        Long userId = Long.valueOf(params.get("userId").toString());
        BigDecimal amount = new BigDecimal(params.get("amount").toString());
        String remark = params.get("remark").toString();

        return toAjax(massageUserService.adjustBalance(userId, amount, remark));
    }

    /**
     * 用户成长值调整
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "用户成长值调整", businessType = BusinessType.UPDATE)
    @PostMapping("/adjustGrowth")
    public AjaxResult adjustGrowth(@RequestBody Map<String, Object> params)
    {
        Long userId = Long.valueOf(params.get("userId").toString());
        Integer growth = Integer.valueOf(params.get("growth").toString());
        String remark = params.get("remark").toString();

        return toAjax(massageUserService.adjustGrowth(userId, growth, remark));
    }



    /**
     * 获取用户统计数据
     */
    @PreAuthorize("@ss.hasPermi('massage:user:list')")
    @GetMapping("/statistics")
    public AjaxResult getUserStatistics()
    {
        return success(massageUserService.getUserStatistics());
    }

    /**
     * 获取用户消费记录
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/orderList")
    public TableDataInfo getUserOrderList(MassageUser massageUser,
                                        @RequestParam(value = "orderType", required = false) String orderType,
                                        @RequestParam(value = "payType", required = false) String payType,
                                        @RequestParam(value = "beginTime", required = false) String beginTime,
                                        @RequestParam(value = "endTime", required = false) String endTime)
    {
        startPage();
        Map<String, Object> params = new HashMap<>();
        params.put("userId", massageUser.getId());
        params.put("orderType", orderType);
        params.put("payType", payType);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<Map<String, Object>> list = massageUserService.getUserOrderList(params);
        return getDataTable(list);
    }

    /**
     * 获取用户充值记录
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/rechargeList")
    public TableDataInfo getUserRechargeList(MassageUser massageUser)
    {
        startPage();
        List<Map<String, Object>> list = massageUserService.getUserRechargeList(massageUser.getId());
        return getDataTable(list);
    }

    /**
     * 获取用户成长值记录
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/growthList")
    public TableDataInfo getUserGrowthList(MassageUser massageUser,
                                         @RequestParam(value = "type", required = false) String type,
                                         @RequestParam(value = "isAdd", required = false) String isAdd,
                                         @RequestParam(value = "beginTime", required = false) String beginTime,
                                         @RequestParam(value = "endTime", required = false) String endTime)
    {
        startPage();
        Map<String, Object> params = new HashMap<>();
        params.put("userId", massageUser.getId());
        params.put("type", type);
        params.put("isAdd", isAdd);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<Map<String, Object>> list = massageUserService.getUserGrowthList(params);
        return getDataTable(list);
    }

    /**
     * 获取用户评价列表
     */
    @GetMapping("/commentList")
    public TableDataInfo getUserCommentList(MassageUser massageUser,
                                          @RequestParam(value = "star", required = false) String star,
                                          @RequestParam(value = "status", required = false) String status,
                                          @RequestParam(value = "beginTime", required = false) String beginTime,
                                          @RequestParam(value = "endTime", required = false) String endTime)
    {
        startPage();
        Map<String, Object> params = new HashMap<>();
        params.put("userId", massageUser.getId());
        params.put("star", star);
        params.put("status", status);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<Map<String, Object>> list = massageUserService.getUserCommentList(params);
        return getDataTable(list);
    }

    /**
     * 获取用户余额记录
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/balanceList")
    public TableDataInfo getUserBalanceList(MassageUser massageUser,
                                          @RequestParam(value = "type", required = false) String type,
                                          @RequestParam(value = "add", required = false) String add,
                                          @RequestParam(value = "beginTime", required = false) String beginTime,
                                          @RequestParam(value = "endTime", required = false) String endTime)
    {
        startPage();
        Map<String, Object> params = new HashMap<>();
        params.put("userId", massageUser.getId());
        params.put("type", type);
        params.put("add", add);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<Map<String, Object>> list = massageUserService.getUserBalanceList(params);
        return getDataTable(list);
    }

    /**
     * 获取用户优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/couponList")
    public TableDataInfo getUserCouponList(MassageUser massageUser)
    {
        startPage();
        List<Map<String, Object>> list = massageUserService.getUserCouponList(massageUser.getId());
        return getDataTable(list);
    }

    /**
     * 获取用户折扣卡列表
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/discountList")
    public TableDataInfo getUserDiscountList(MassageUser massageUser)
    {
        startPage();
        List<Map<String, Object>> list = massageUserService.getUserDiscountList(massageUser.getId());
        return getDataTable(list);
    }

    /**
     * 获取用户屏蔽列表
     */
    @PreAuthorize("@ss.hasPermi('massage:user:query')")
    @GetMapping("/blockedList")
    public TableDataInfo getUserBlockedList(MassageUser massageUser)
    {
        startPage();
        List<Map<String, Object>> list = massageUserService.getUserBlockedList(massageUser.getId());
        return getDataTable(list);
    }

    /**
     * 添加屏蔽用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "添加屏蔽用户", businessType = BusinessType.INSERT)
    @PostMapping("/addBlocked")
    public AjaxResult addBlockedUser(@RequestBody Map<String, Object> params)
    {
        Long userId = Long.valueOf(params.get("userId").toString());
        Long blockedUserId = Long.valueOf(params.get("blockedUserId").toString());

        return toAjax(massageUserService.addBlockedUser(userId, blockedUserId));
    }

    /**
     * 解除屏蔽用户
     */
    @PreAuthorize("@ss.hasPermi('massage:user:edit')")
    @Log(title = "解除屏蔽用户", businessType = BusinessType.DELETE)
    @PostMapping("/removeBlocked")
    public AjaxResult removeBlockedUser(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) params.get("ids");

        return toAjax(massageUserService.removeBlockedUser(ids));
    }
}
