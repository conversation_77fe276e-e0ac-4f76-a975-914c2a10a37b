<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="评价星级" prop="star">
        <el-select v-model="queryParams.star" placeholder="请选择评价星级" clearable>
          <el-option label="5星" value="5" />
          <el-option label="4星" value="4" />
          <el-option label="3星" value="3" />
          <el-option label="2星" value="2" />
          <el-option label="1星" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="评价状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择评价状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="隐藏" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="评价时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-d-arrow-left" size="mini" @click="showSearch=!showSearch">
          {{showSearch ? '隐藏' : '显示'}}搜索
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="text item">
            <span>总评价数</span>
            <div class="number">{{ statistics.totalComments }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="text item">
            <span>平均星级</span>
            <div class="number">{{ statistics.avgStar }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="text item">
            <span>好评数(4-5星)</span>
            <div class="number">{{ statistics.goodComments }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="text item">
            <span>好评率</span>
            <div class="number">{{ statistics.goodRate }}%</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 评价列表 -->
    <el-table v-loading="loading" :data="commentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" align="center" prop="orderCode" width="120" />
      <el-table-column label="评价星级" align="center" prop="star" width="100">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.star"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}星">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="text" :show-overflow-tooltip="true" min-width="200" />
      <el-table-column label="评价标签" align="center" prop="lableList" width="150">
        <template slot-scope="scope">
          <el-tag
            v-for="tag in scope.row.lableList"
            :key="tag"
            size="mini"
            style="margin: 2px;">
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="技师" align="center" prop="coachName" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.massage_comment_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="评价时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['massage:comment:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['massage:comment:edit']"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 评价详情对话框 -->
    <el-dialog title="评价详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-form ref="detailForm" :model="detailForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单编号">
              <span>{{ detailForm.orderCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技师">
              <span>{{ detailForm.coachName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="评价星级">
              <el-rate
                v-model="detailForm.star"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}星">
              </el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价时间">
              <span>{{ parseTime(detailForm.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="评价内容">
          <span>{{ detailForm.text }}</span>
        </el-form-item>
        <el-form-item label="评价标签" v-if="detailForm.lableList && detailForm.lableList.length > 0">
          <el-tag
            v-for="tag in detailForm.lableList"
            :key="tag"
            size="small"
            style="margin: 2px;">
            {{ tag }}
          </el-tag>
        </el-form-item>
        <el-form-item label="服务评价" v-if="detailForm.serviceList && detailForm.serviceList.length > 0">
          <div v-for="service in detailForm.serviceList" :key="service.serviceId" style="margin-bottom: 10px;">
            <span>{{ service.serviceName }}：</span>
            <el-rate
              v-model="service.star"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}星">
            </el-rate>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 编辑评价对话框 -->
    <el-dialog title="编辑评价" :visible.sync="editOpen" width="500px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="80px">
        <el-form-item label="评价状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否置顶" prop="top">
          <el-radio-group v-model="editForm.top">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEdit">确 定</el-button>
        <el-button @click="editOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserCommentList, updateComment } from "@/api/massage/comment";

export default {
  name: "CommentListTab",
  dicts: ['massage_comment_status'],
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价表格数据
      commentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      detailOpen: false,
      editOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: this.userId,
        star: null,
        status: null
      },
      // 表单参数
      detailForm: {},
      editForm: {},
      // 表单校验
      editRules: {
        status: [
          { required: true, message: "评价状态不能为空", trigger: "change" }
        ]
      },
      // 统计信息
      statistics: {
        totalComments: 0,
        avgStar: 0,
        goodComments: 0,
        goodRate: 0
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询评价列表 */
    getList() {
      this.loading = true;
      const params = this.addDateRange(this.queryParams, this.dateRange);
      getUserCommentList(params).then(response => {
        this.commentList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.getStatistics();
      });
    },
    /** 计算统计信息 */
    getStatistics() {
      this.statistics = {
        totalComments: this.commentList.length,
        avgStar: 0,
        goodComments: 0,
        goodRate: 0
      };
      
      if (this.commentList.length > 0) {
        const totalStar = this.commentList.reduce((sum, item) => sum + (item.star || 0), 0);
        this.statistics.avgStar = (totalStar / this.commentList.length).toFixed(1);
        this.statistics.goodComments = this.commentList.filter(item => item.star >= 4).length;
        this.statistics.goodRate = ((this.statistics.goodComments / this.commentList.length) * 100).toFixed(1);
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看评价详情 */
    handleView(row) {
      this.detailForm = { ...row };
      this.detailOpen = true;
    },
    /** 编辑评价 */
    handleUpdate(row) {
      this.editForm = {
        id: row.id,
        status: row.status,
        top: row.top
      };
      this.editOpen = true;
    },
    /** 提交编辑 */
    submitEdit() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          updateComment(this.editForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.editOpen = false;
            this.getList();
          });
        }
      });
    },
    // 提供给父组件调用的刷新方法
    refresh() {
      this.getList();
    }
  }
};
</script>

<style scoped>
.box-card {
  text-align: center;
}
.text.item {
  padding: 10px;
}
.number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-top: 5px;
}
</style>
