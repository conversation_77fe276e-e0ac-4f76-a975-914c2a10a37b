<template>
  <div class="app-container">
    <!-- 用户基本信息 -->
    <el-card class="user-info-card">
      <div slot="header" class="clearfix">
        <span>用户详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="user-avatar-section">
            <img :src="userInfo.avatarUrl || '/static/default-avatar.png'" alt="用户头像" class="user-avatar">
            <h3>{{ userInfo.nickName || '未设置昵称' }}</h3>
            <p class="user-id">用户ID: {{ userInfo.id }}</p>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="user-stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-value">¥{{ userInfo.balance || 0 }}</div>
                  <div class="stat-label">用户余额</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-value">¥{{ userInfo.cash || 0 }}</div>
                  <div class="stat-label">现金余额</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-value">¥{{ userInfo.totalConsumption || 0 }}</div>
                  <div class="stat-label">消费总金额</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-value">{{ userInfo.growth || 0 }}</div>
                  <div class="stat-label">成长值</div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <el-descriptions :column="3" border style="margin-top: 20px;">
            <el-descriptions-item label="手机号">{{ userInfo.phone || '未绑定' }}</el-descriptions-item>
            <el-descriptions-item label="性别">
              <span v-if="userInfo.gender === 1">男</span>
              <span v-else-if="userInfo.gender === 2">女</span>
              <span v-else>未知</span>
            </el-descriptions-item>
            <el-descriptions-item label="城市">{{ userInfo.city || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="省份">{{ userInfo.province || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="国家">{{ userInfo.country || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="用户状态">
              <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
                {{ userInfo.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ parseTime(userInfo.createTime * 1000) }}</el-descriptions-item>
            <el-descriptions-item label="应用ID">{{ userInfo.uniacid || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="来源类型">{{ userInfo.sourceType || '未知' }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <!-- 标签页内容 -->
    <el-card style="margin-top: 20px;">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="用户信息" name="userInfo">
          <user-info-tab ref="userInfoTab" :user-info="userInfo" @refresh="getUserInfo" />
        </el-tab-pane>
        <el-tab-pane label="消费记录" name="consumption">
          <consumption-record-tab ref="consumptionRecordTab" :user-id="userId" />
        </el-tab-pane>
        <el-tab-pane label="余额明细" name="recharge">
          <recharge-record-tab ref="rechargeRecordTab" :user-id="userId" />
        </el-tab-pane>
        <el-tab-pane label="成长值明细" name="growth">
          <growth-detail-tab ref="growthDetailTab" :user-id="userId" />
        </el-tab-pane>
        <el-tab-pane label="持有优惠券" name="coupons">
          <user-coupons-tab :user-id="userId" />
        </el-tab-pane>
        <el-tab-pane label="持有折扣卡" name="discounts">
          <user-discounts-tab :user-id="userId" />
        </el-tab-pane>
        <el-tab-pane label="屏蔽达人" name="blocked">
          <blocked-users-tab :user-id="userId" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { getUser } from "@/api/massage/user";
import UserInfoTab from './components/UserInfoTab.vue';
import ConsumptionRecordTab from './components/ConsumptionRecordTab.vue';
import RechargeRecordTab from './components/RechargeRecordTab.vue';
import GrowthDetailTab from './components/GrowthDetailTab.vue';
import UserCouponsTab from './components/UserCouponsTab.vue';
import UserDiscountsTab from './components/UserDiscountsTab.vue';
import BlockedUsersTab from './components/BlockedUsersTab.vue';

export default {
  name: "UserDetail",
  components: {
    UserInfoTab,
    ConsumptionRecordTab,
    RechargeRecordTab,
    GrowthDetailTab,
    UserCouponsTab,
    UserDiscountsTab,
    BlockedUsersTab
  },
  data() {
    return {
      userId: null,
      userInfo: {},
      activeTab: 'userInfo',
      loading: false
    };
  },
  created() {
    this.userId = this.$route.params.id;
    this.getUserInfo();
  },
  mounted() {
    // 监听余额调整事件
    this.$eventBus.$on('userBalanceAdjusted', (userId) => {
      if (userId == this.userId) {
        this.refreshBalanceRecords();
        // 重新获取用户信息以更新余额显示
        this.getUserInfo();
      }
    });

    // 监听成长值调整事件
    this.$eventBus.$on('userGrowthAdjusted', (userId) => {
      if (userId == this.userId) {
        this.refreshGrowthRecords();
        // 重新获取用户信息以更新成长值显示
        this.getUserInfo();
      }
    });

    // 监听订单支付成功事件
    this.$eventBus.$on('orderPaymentSuccess', (userId) => {
      if (userId == this.userId) {
        this.refreshAllRecords();
        // 重新获取用户信息以更新余额和成长值显示
        this.getUserInfo();
      }
    });
  },
  beforeDestroy() {
    // 移除事件监听
    this.$eventBus.$off('userBalanceAdjusted');
    this.$eventBus.$off('userGrowthAdjusted');
    this.$eventBus.$off('orderPaymentSuccess');
  },
  methods: {
    /** 获取用户信息 */
    getUserInfo() {
      this.loading = true;
      getUser(this.userId).then(response => {
        this.userInfo = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 标签页切换 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
    },
    /** 返回列表 */
    goBack() {
      this.$router.go(-1);
    },
    /** 刷新余额明细 */
    refreshBalanceRecords() {
      if (this.$refs.rechargeRecordTab) {
        this.$refs.rechargeRecordTab.refresh();
      }
    },
    /** 刷新成长值明细 */
    refreshGrowthRecords() {
      if (this.$refs.growthDetailTab) {
        this.$refs.growthDetailTab.refresh();
      }
    },
    /** 刷新消费记录 */
    refreshConsumptionRecords() {
      if (this.$refs.consumptionRecordTab) {
        this.$refs.consumptionRecordTab.refresh();
      }
    },
    /** 刷新所有明细记录 */
    refreshAllRecords() {
      this.refreshBalanceRecords();
      this.refreshGrowthRecords();
      this.refreshConsumptionRecords();
    }
  }
};
</script>

<style scoped>
.user-info-card {
  margin-bottom: 20px;
}

.user-avatar-section {
  text-align: center;
  padding: 20px;
}

.user-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar-section h3 {
  margin: 15px 0 5px 0;
  color: #333;
  font-weight: 500;
}

.user-id {
  color: #666;
  font-size: 14px;
}

.user-stats {
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.el-descriptions {
  margin-top: 20px;
}
</style>
