-- 创建评价相关表

-- 订单评价表
CREATE TABLE `ims_massage_service_order_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `uniacid` int(11) DEFAULT NULL COMMENT '应用ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `coach_id` bigint(20) DEFAULT NULL COMMENT '技师ID',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '管理员ID',
  `star` tinyint(1) NOT NULL DEFAULT '5' COMMENT '评价星级(1-5)',
  `text` text COMMENT '评价内容',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1正常 0隐藏 -1删除)',
  `top` tinyint(1) DEFAULT '0' COMMENT '是否置顶(1是 0否)',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间戳2',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_coach_id` (`coach_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单评价表';

-- 评价标签表
CREATE TABLE `ims_massage_service_lable` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `uniacid` int(11) DEFAULT NULL COMMENT '应用ID',
  `title` varchar(100) NOT NULL COMMENT '标签名称',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用 0禁用 -1删除)',
  `top` int(11) DEFAULT '0' COMMENT '排序权重',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_uniacid` (`uniacid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价标签表';

-- 评价标签关联表
CREATE TABLE `ims_massage_service_comment_lable` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `uniacid` int(11) DEFAULT NULL COMMENT '应用ID',
  `comment_id` bigint(20) NOT NULL COMMENT '评价ID',
  `lable_id` bigint(20) NOT NULL COMMENT '标签ID',
  `lable_title` varchar(100) DEFAULT NULL COMMENT '标签名称',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_lable_id` (`lable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价标签关联表';

-- 服务项目评价表
CREATE TABLE `ims_massage_service_order_comment_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '服务评价ID',
  `uniacid` int(11) DEFAULT NULL COMMENT '应用ID',
  `comment_id` bigint(20) NOT NULL COMMENT '评价ID',
  `service_id` bigint(20) NOT NULL COMMENT '服务项目ID',
  `star` tinyint(1) NOT NULL DEFAULT '5' COMMENT '服务评价星级(1-5)',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务项目评价表';

-- 插入一些默认的评价标签
INSERT INTO `ims_massage_service_lable` (`uniacid`, `title`, `status`, `top`, `create_time`) VALUES
(1, '服务态度好', 1, 100, UNIX_TIMESTAMP()),
(1, '技术专业', 1, 90, UNIX_TIMESTAMP()),
(1, '环境舒适', 1, 80, UNIX_TIMESTAMP()),
(1, '价格合理', 1, 70, UNIX_TIMESTAMP()),
(1, '按时到达', 1, 60, UNIX_TIMESTAMP()),
(1, '效果明显', 1, 50, UNIX_TIMESTAMP()),
(1, '值得推荐', 1, 40, UNIX_TIMESTAMP()),
(1, '下次还来', 1, 30, UNIX_TIMESTAMP());
