{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\index.vue", "mtime": 1753796067024}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_coach", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "coachList", "detailOpen", "auditOpen", "queryParams", "pageNum", "pageSize", "coach_name", "mobile", "status", "auth_status", "form", "detailForm", "auditForm", "rules", "required", "message", "trigger", "pattern", "auditRules", "created", "getList", "methods", "_this", "listCoach", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleAdd", "$router", "push", "handleEdit", "row", "handleRecommendChange", "_this2", "text", "recommend", "$modal", "confirm", "updateCoach", "msgSuccess", "catch", "handleCommand", "command", "handleAuthCoach", "handleRecommendAction", "handleStatusChange", "handleDetail", "_this3", "statusText", "newStatus", "newAuthStatus", "sh_time", "Math", "floor", "Date", "now", "sh_text", "_this4", "_this5", "_this6", "getCoach", "detailTitle", "handleUpdate", "_this7", "reset", "open", "title", "handleDelete", "_this8", "delCoach", "handleView", "_this9", "handleAudit", "submitAuditForm", "_this0", "$refs", "validate", "valid", "auditCoach", "cancelAudit"], "sources": ["src/views/massage/coach/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"达人姓名\" prop=\"coach_name\">\n        <el-input\n          v-model=\"queryParams.coach_name\"\n          placeholder=\"请输入达人姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号\" prop=\"mobile\">\n        <el-input\n          v-model=\"queryParams.mobile\"\n          placeholder=\"请输入手机号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"达人状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择达人状态\" clearable>\n          <el-option label=\"待审核\" value=\"1\" />\n          <el-option label=\"正常\" value=\"2\" />\n          <el-option label=\"禁用\" value=\"3\" />\n          <el-option label=\"审核失败\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"认证状态\" prop=\"auth_status\">\n        <el-select v-model=\"queryParams.auth_status\" placeholder=\"请选择认证状态\" clearable>\n          <el-option label=\"未认证\" value=\"0\" />\n          <el-option label=\"待审核\" value=\"1\" />\n          <el-option label=\"已认证\" value=\"2\" />\n          <el-option label=\"认证失败\" value=\"3\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"工作状态\" prop=\"is_work\">\n        <el-select v-model=\"queryParams.is_work\" placeholder=\"请选择工作状态\" clearable>\n          <el-option label=\"工作中\" value=\"1\" />\n          <el-option label=\"休息中\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"推荐状态\" prop=\"recommend\">\n        <el-select v-model=\"queryParams.recommend\" placeholder=\"请选择推荐状态\" clearable>\n          <el-option label=\"推荐\" value=\"1\" />\n          <el-option label=\"普通\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['massage:coach:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['massage:coach:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['massage:coach:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"coachList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"头像\" align=\"center\" prop=\"work_img\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.work_img\" :width=\"50\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"coach_name\" width=\"120\" />\n      <el-table-column label=\"手机号\" align=\"center\" prop=\"mobile\" width=\"120\" />\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <div>\n            <p>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d}') }}</p>\n            <p>{{ parseTime(scope.row.create_time * 1000, '{h}:{i}:{s}') }}</p>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"user_id\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.user_id || '-' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"是否绑定\" align=\"center\" prop=\"user_id\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.user_id\" type=\"success\">已绑定</el-tag>\n          <el-tag v-else type=\"info\">未绑定</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"认证状态\" align=\"center\" prop=\"auth_status\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.auth_status == 0\" type=\"info\">未认证</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 2\" type=\"success\">已认证</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 3\" type=\"danger\">认证失败</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 2\" type=\"success\">正常</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 3\" type=\"danger\">禁用</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 4\" type=\"info\">审核失败</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"设为推荐\" align=\"center\" prop=\"recommend\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.recommend\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n            @change=\"handleRecommendChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"编辑\" align=\"center\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"primary\"\n            plain\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n            v-hasPermi=\"['massage:coach:edit']\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更多菜单\" align=\"center\" width=\"120\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-dropdown @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['massage:coach:audit']\">\n            <el-button size=\"mini\" type=\"danger\" plain>\n              更多菜单<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"auth\" v-if=\"scope.row.status == 1 || scope.row.auth_status == 1\">授权达人</el-dropdown-item>\n              <el-dropdown-item command=\"recommend\" v-if=\"scope.row.recommend == 0\">设为推荐</el-dropdown-item>\n              <el-dropdown-item command=\"unrecommend\" v-if=\"scope.row.recommend == 1\">取消推荐</el-dropdown-item>\n              <el-dropdown-item command=\"disable\" v-if=\"scope.row.status == 2\">禁用达人</el-dropdown-item>\n              <el-dropdown-item command=\"enable\" v-if=\"scope.row.status == 3\">启用达人</el-dropdown-item>\n              <el-dropdown-item command=\"detail\">查看详情</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:coach:query']\"\n          >详情</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['massage:coach:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleAudit(scope.row)\"\n            v-hasPermi=\"['massage:coach:audit']\"\n            v-if=\"scope.row.status == 1\"\n          >审核</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['massage:coach:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n\n\n    <!-- 达人详情对话框 -->\n    <el-dialog title=\"达人详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"达人ID\">{{ detailForm.id }}</el-descriptions-item>\n        <el-descriptions-item label=\"达人姓名\">{{ detailForm.coach_name }}</el-descriptions-item>\n        <el-descriptions-item label=\"手机号\">{{ detailForm.mobile }}</el-descriptions-item>\n        <el-descriptions-item label=\"性别\">\n          <el-tag v-if=\"detailForm.sex == 1\">男</el-tag>\n          <el-tag v-else-if=\"detailForm.sex == 2\" type=\"danger\">女</el-tag>\n        </el-descriptions-item>\n\n        <el-descriptions-item label=\"从业时间\">{{ detailForm.work_time }}年</el-descriptions-item>\n        <el-descriptions-item label=\"身份证号\">{{ detailForm.id_card }}</el-descriptions-item>\n        <el-descriptions-item label=\"服务地址\">{{ detailForm.address }}</el-descriptions-item>\n        <el-descriptions-item label=\"评分\">\n          <el-rate v-model=\"detailForm.star\" disabled show-score text-color=\"#ff9900\" score-template=\"{value}\"></el-rate>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"工作状态\">\n          <el-tag v-if=\"detailForm.is_work == 1\" type=\"success\">工作中</el-tag>\n          <el-tag v-else type=\"info\">休息中</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"推荐状态\">\n          <el-tag v-if=\"detailForm.recommend == 1\" type=\"warning\">推荐</el-tag>\n          <el-tag v-else type=\"info\">普通</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"服务价格\">{{ detailForm.service_price }}元</el-descriptions-item>\n        <el-descriptions-item label=\"车费\">{{ detailForm.car_price }}元</el-descriptions-item>\n        <el-descriptions-item label=\"订单数量\">{{ detailForm.total_order_num || 0 }}</el-descriptions-item>\n        <el-descriptions-item label=\"达人状态\">\n          <el-tag v-if=\"detailForm.status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"detailForm.status == 2\" type=\"success\">正常</el-tag>\n          <el-tag v-else-if=\"detailForm.status == 3\" type=\"danger\">禁用</el-tag>\n          <el-tag v-else-if=\"detailForm.status == 4\" type=\"info\">审核失败</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"认证状态\">\n          <el-tag v-if=\"detailForm.auth_status == 0\" type=\"info\">未认证</el-tag>\n          <el-tag v-else-if=\"detailForm.auth_status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"detailForm.auth_status == 2\" type=\"success\">已认证</el-tag>\n          <el-tag v-else-if=\"detailForm.auth_status == 3\" type=\"danger\">认证失败</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"注册时间\">\n          {{ parseTime(detailForm.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"审核时间\" v-if=\"detailForm.sh_time\">\n          {{ parseTime(detailForm.sh_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"个人简介\" :span=\"2\">{{ detailForm.text }}</el-descriptions-item>\n        <el-descriptions-item label=\"工作照片\" :span=\"2\">\n          <image-preview :src=\"detailForm.work_img\" :width=\"100\" :height=\"100\"/>\n        </el-descriptions-item>\n      </el-descriptions>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 达人审核对话框 -->\n    <el-dialog title=\"达人审核\" :visible.sync=\"auditOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\n        <el-form-item label=\"审核状态\" prop=\"status\">\n          <el-radio-group v-model=\"auditForm.status\">\n            <el-radio :label=\"2\">审核通过</el-radio>\n            <el-radio :label=\"4\">审核失败</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核意见\" prop=\"sh_text\">\n          <el-input v-model=\"auditForm.sh_text\" type=\"textarea\" placeholder=\"请输入审核意见\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitAuditForm\">确 定</el-button>\n        <el-button @click=\"cancelAudit\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listCoach, getCoach, delCoach, addCoach, updateCoach, auditCoach } from \"@/api/massage/coach\";\n\nexport default {\n  name: \"Coach\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 达人表格数据\n      coachList: [],\n\n      detailOpen: false,\n      auditOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coach_name: null,\n        mobile: null,\n        status: null,\n        auth_status: null\n      },\n      // 表单参数\n      form: {},\n      detailForm: {},\n      auditForm: {},\n      // 表单校验\n      rules: {\n        coach_name: [\n          { required: true, message: \"达人姓名不能为空\", trigger: \"blur\" }\n        ],\n        mobile: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      },\n      auditRules: {\n        status: [\n          { required: true, message: \"审核状态不能为空\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询达人列表 */\n    getList() {\n      this.loading = true;\n      listCoach(this.queryParams).then(response => {\n        this.coachList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.$router.push(\"/massage/coach/edit\");\n    },\n    /** 编辑按钮操作 */\n    handleEdit(row) {\n      this.$router.push(\"/massage/coach/edit?id=\" + row.id);\n    },\n    /** 推荐状态切换 */\n    handleRecommendChange(row) {\n      const text = row.recommend === 1 ? \"推荐\" : \"取消推荐\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          recommend: row.recommend\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(() => {\n        row.recommend = row.recommend === 1 ? 0 : 1;\n      });\n    },\n    /** 更多菜单操作 */\n    handleCommand(command, row) {\n      switch (command) {\n        case \"auth\":\n          this.handleAuthCoach(row);\n          break;\n        case \"recommend\":\n          this.handleRecommendAction(row, 1);\n          break;\n        case \"unrecommend\":\n          this.handleRecommendAction(row, 0);\n          break;\n        case \"disable\":\n          this.handleStatusChange(row, 3, \"禁用\");\n          break;\n        case \"enable\":\n          this.handleStatusChange(row, 2, \"启用\");\n          break;\n        case \"detail\":\n          this.handleDetail(row);\n          break;\n      }\n    },\n    /** 授权达人 */\n    handleAuthCoach(row) {\n      let statusText = \"\";\n      let newStatus = 2;\n      let newAuthStatus = 2;\n\n      if (row.status == 1) {\n        statusText = \"通过达人审核\";\n      } else if (row.auth_status == 1) {\n        statusText = \"通过认证审核\";\n      }\n\n      this.$modal.confirm('确认要\"' + statusText + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          status: newStatus,\n          auth_status: newAuthStatus,\n          sh_time: Math.floor(Date.now() / 1000),\n          sh_text: \"管理员授权通过\"\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(statusText + \"成功\");\n      });\n    },\n    /** 推荐操作 */\n    handleRecommendAction(row, recommend) {\n      const text = recommend === 1 ? \"推荐\" : \"取消推荐\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          recommend: recommend\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      });\n    },\n    /** 状态变更 */\n    handleStatusChange(row, status, text) {\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          status: status\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      });\n    },\n    /** 查看详情 */\n    handleDetail(row) {\n      const id = row.id || this.ids;\n      getCoach(id).then(response => {\n        this.detailForm = response.data;\n        this.detailOpen = true;\n        this.detailTitle = \"达人详情\";\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getCoach(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改达人\";\n      });\n    },\n\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除达人编号为\"' + ids + '\"的数据项？').then(function() {\n        return delCoach(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 查看详情 */\n    handleView(row) {\n      getCoach(row.id).then(response => {\n        this.detailForm = response.data;\n        this.detailOpen = true;\n      });\n    },\n    /** 达人审核 */\n    handleAudit(row) {\n      this.auditForm = {\n        id: row.id,\n        status: null,\n        sh_text: null\n      };\n      this.auditOpen = true;\n    },\n    /** 提交审核 */\n    submitAuditForm() {\n      this.$refs[\"auditForm\"].validate(valid => {\n        if (valid) {\n          auditCoach(this.auditForm).then(response => {\n            this.$modal.msgSuccess(\"审核成功\");\n            this.auditOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    /** 取消审核 */\n    cancelAudit() {\n      this.auditOpen = false;\n      this.auditForm = {};\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;AAuSA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MAEAC,UAAA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,WAAA;MACA;MACA;MACAC,IAAA;MACAC,UAAA;MACAC,SAAA;MACA;MACAC,KAAA;QACAP,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,MAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,UAAA;QACAV,MAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA5B,OAAA;MACA,IAAA6B,gBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtB,SAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvB,KAAA,GAAA0B,QAAA,CAAA1B,KAAA;QACAuB,KAAA,CAAA5B,OAAA;MACA;IACA;IAEA,aACAiC,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApC,GAAA,GAAAoC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAAtC,MAAA,GAAAmC,SAAA,CAAAI,MAAA;MACA,KAAAtC,QAAA,IAAAkC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAH,OAAA,CAAAC,IAAA,6BAAAE,GAAA,CAAAN,EAAA;IACA;IACA,aACAO,qBAAA,WAAAA,sBAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,IAAA,GAAAH,GAAA,CAAAI,SAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,UAAAH,IAAA,UAAAH,GAAA,CAAAlC,UAAA,YAAAkB,IAAA;QACA,WAAAuB,kBAAA;UACAb,EAAA,EAAAM,GAAA,CAAAN,EAAA;UACAU,SAAA,EAAAJ,GAAA,CAAAI;QACA;MACA,GAAApB,IAAA;QACAkB,MAAA,CAAAtB,OAAA;QACAsB,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAT,GAAA,CAAAI,SAAA,GAAAJ,GAAA,CAAAI,SAAA;MACA;IACA;IACA,aACAM,aAAA,WAAAA,cAAAC,OAAA,EAAAX,GAAA;MACA,QAAAW,OAAA;QACA;UACA,KAAAC,eAAA,CAAAZ,GAAA;UACA;QACA;UACA,KAAAa,qBAAA,CAAAb,GAAA;UACA;QACA;UACA,KAAAa,qBAAA,CAAAb,GAAA;UACA;QACA;UACA,KAAAc,kBAAA,CAAAd,GAAA;UACA;QACA;UACA,KAAAc,kBAAA,CAAAd,GAAA;UACA;QACA;UACA,KAAAe,YAAA,CAAAf,GAAA;UACA;MACA;IACA;IACA,WACAY,eAAA,WAAAA,gBAAAZ,GAAA;MAAA,IAAAgB,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,SAAA;MACA,IAAAC,aAAA;MAEA,IAAAnB,GAAA,CAAAhC,MAAA;QACAiD,UAAA;MACA,WAAAjB,GAAA,CAAA/B,WAAA;QACAgD,UAAA;MACA;MAEA,KAAAZ,MAAA,CAAAC,OAAA,UAAAW,UAAA,UAAAjB,GAAA,CAAAlC,UAAA,YAAAkB,IAAA;QACA,WAAAuB,kBAAA;UACAb,EAAA,EAAAM,GAAA,CAAAN,EAAA;UACA1B,MAAA,EAAAkD,SAAA;UACAjD,WAAA,EAAAkD,aAAA;UACAC,OAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;UACAC,OAAA;QACA;MACA,GAAAzC,IAAA;QACAgC,MAAA,CAAApC,OAAA;QACAoC,MAAA,CAAAX,MAAA,CAAAG,UAAA,CAAAS,UAAA;MACA;IACA;IACA,WACAJ,qBAAA,WAAAA,sBAAAb,GAAA,EAAAI,SAAA;MAAA,IAAAsB,MAAA;MACA,IAAAvB,IAAA,GAAAC,SAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,UAAAH,IAAA,UAAAH,GAAA,CAAAlC,UAAA,YAAAkB,IAAA;QACA,WAAAuB,kBAAA;UACAb,EAAA,EAAAM,GAAA,CAAAN,EAAA;UACAU,SAAA,EAAAA;QACA;MACA,GAAApB,IAAA;QACA0C,MAAA,CAAA9C,OAAA;QACA8C,MAAA,CAAArB,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA;IACA;IACA,WACAW,kBAAA,WAAAA,mBAAAd,GAAA,EAAAhC,MAAA,EAAAmC,IAAA;MAAA,IAAAwB,MAAA;MACA,KAAAtB,MAAA,CAAAC,OAAA,UAAAH,IAAA,UAAAH,GAAA,CAAAlC,UAAA,YAAAkB,IAAA;QACA,WAAAuB,kBAAA;UACAb,EAAA,EAAAM,GAAA,CAAAN,EAAA;UACA1B,MAAA,EAAAA;QACA;MACA,GAAAgB,IAAA;QACA2C,MAAA,CAAA/C,OAAA;QACA+C,MAAA,CAAAtB,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA;IACA;IACA,WACAY,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAAlC,EAAA,GAAAM,GAAA,CAAAN,EAAA,SAAAvC,GAAA;MACA,IAAA0E,eAAA,EAAAnC,EAAA,EAAAV,IAAA,WAAAC,QAAA;QACA2C,MAAA,CAAAzD,UAAA,GAAAc,QAAA,CAAAhC,IAAA;QACA2E,MAAA,CAAAnE,UAAA;QACAmE,MAAA,CAAAE,WAAA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA/B,GAAA;MAAA,IAAAgC,MAAA;MACA,KAAAC,KAAA;MACA,IAAAvC,EAAA,GAAAM,GAAA,CAAAN,EAAA,SAAAvC,GAAA;MACA,IAAA0E,eAAA,EAAAnC,EAAA,EAAAV,IAAA,WAAAC,QAAA;QACA+C,MAAA,CAAA9D,IAAA,GAAAe,QAAA,CAAAhC,IAAA;QACA+E,MAAA,CAAAE,IAAA;QACAF,MAAA,CAAAG,KAAA;MACA;IACA;IAEA,aACAC,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,IAAAlF,GAAA,GAAA6C,GAAA,CAAAN,EAAA,SAAAvC,GAAA;MACA,KAAAkD,MAAA,CAAAC,OAAA,kBAAAnD,GAAA,aAAA6B,IAAA;QACA,WAAAsD,eAAA,EAAAnF,GAAA;MACA,GAAA6B,IAAA;QACAqD,MAAA,CAAAzD,OAAA;QACAyD,MAAA,CAAAhC,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACA8B,UAAA,WAAAA,WAAAvC,GAAA;MAAA,IAAAwC,MAAA;MACA,IAAAX,eAAA,EAAA7B,GAAA,CAAAN,EAAA,EAAAV,IAAA,WAAAC,QAAA;QACAuD,MAAA,CAAArE,UAAA,GAAAc,QAAA,CAAAhC,IAAA;QACAuF,MAAA,CAAA/E,UAAA;MACA;IACA;IACA,WACAgF,WAAA,WAAAA,YAAAzC,GAAA;MACA,KAAA5B,SAAA;QACAsB,EAAA,EAAAM,GAAA,CAAAN,EAAA;QACA1B,MAAA;QACAyD,OAAA;MACA;MACA,KAAA/D,SAAA;IACA;IACA,WACAgF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,cAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,iBAAA,EAAAJ,MAAA,CAAAvE,SAAA,EAAAY,IAAA,WAAAC,QAAA;YACA0D,MAAA,CAAAtC,MAAA,CAAAG,UAAA;YACAmC,MAAA,CAAAjF,SAAA;YACAiF,MAAA,CAAA/D,OAAA;UACA;QACA;MACA;IACA;IACA,WACAoE,WAAA,WAAAA,YAAA;MACA,KAAAtF,SAAA;MACA,KAAAU,SAAA;IACA;EACA;AACA", "ignoreList": []}]}