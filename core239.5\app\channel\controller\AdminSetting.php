<?php
namespace app\channel\controller;
use app\AdminRest;
use app\massage\model\Config;
use app\massage\model\ConfigSetting;
use think\App;



class AdminSetting extends AdminRest
{


    protected $model;

    protected $admin_model;


    public function __construct(App $app) {

        parent::__construct($app);

        $this->model = new Config();
    }


    /**
     * <AUTHOR>
     * @DataTime: 2021-03-12 15:04
     * @功能说明:配置详情
     */
    public function configInfo(){

        $arr = [

            'channel_admin_balance',
            'channel_balance',
            'channel_bind_forever',
            'channel_bind_type',
            'channel_check_status',
            'channel_coach_balance',
            'channel_menu_name',
            'channel_poster',
            'channel_status',
            'user_channel_over_time',
        ];

        $data = getConfigSettingArr($this->_uniacid,$arr);

        return $this->success($data);
    }


    /**
     * <AUTHOR>
     * @DataTime: 2021-03-12 16:14
     * @功能说明:编辑配置
     */
    public function configUpdate(){

        $input = $this->_input;

        $update = [

            'channel_admin_balance' => $input['channel_admin_balance'],
            'channel_balance' => $input['channel_balance'],
            'channel_bind_forever' => $input['channel_bind_forever'],
            'channel_bind_type' => $input['channel_bind_type'],
            'channel_check_status' => $input['channel_check_status'],
            'channel_coach_balance' => $input['channel_coach_balance'],
            'channel_menu_name' => $input['channel_menu_name'],
            'channel_poster' => $input['channel_poster'],
            'channel_status' => $input['channel_status'],
            'user_channel_over_time' => $input['user_channel_over_time'],
        ];

        $config_model = new ConfigSetting();

        $res = $config_model->dataUpdate($update,$this->_uniacid);

        return $this->success($res);
    }








}
