<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ title }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="基础信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="达人姓名" prop="coach_name">
                  <el-input v-model="form.coach_name" maxlength="15" show-word-limit placeholder="请输入达人姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="mobile">
                  <el-input v-model="form.mobile" maxlength="11" show-word-limit placeholder="请输入手机号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="性别" prop="sex">
                  <el-radio-group v-model="form.sex">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="2">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="从业时间" prop="work_time">
                  <el-input-number v-model="form.work_time" :min="0" :max="50" placeholder="请输入从业年限">
                    <template slot="append">年</template>
                  </el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="身高" prop="height">
                  <el-input v-model="form.height" placeholder="请输入身高">
                    <template slot="append">cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="体重" prop="weight">
                  <el-input v-model="form.weight" placeholder="请输入体重">
                    <template slot="append">kg</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="生日" prop="birthday">
                  <el-date-picker
                    v-model="form.birthday"
                    type="date"
                    placeholder="请选择生日"
                    value-format="timestamp"
                    style="width: 100%"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="星座" prop="constellation">
                  <el-input v-model="form.constellation" placeholder="请输入星座" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="身份证号" prop="id_card">
                  <el-input v-model="form.id_card" placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="城市" prop="city">
                  <el-input v-model="form.city" placeholder="请输入城市" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="服务地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入服务地址" />
            </el-form-item>
            <el-form-item label="个人简介" prop="text">
              <el-input v-model="form.text" type="textarea" :rows="4" placeholder="请输入个人简介" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="工作信息" name="work">
            <el-row>
              <el-col :span="12">
                <el-form-item label="工作状态" prop="is_work">
                  <el-radio-group v-model="form.is_work">
                    <el-radio :label="1">工作中</el-radio>
                    <el-radio :label="0">休息中</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作类型" prop="work_type">
                  <el-select v-model="form.work_type" placeholder="请选择工作类型">
                    <el-option label="可服务" :value="1" />
                    <el-option label="服务中" :value="2" />
                    <el-option label="可预约" :value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="是否推荐" prop="recommend">
                  <el-radio-group v-model="form.recommend">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评分" prop="star">
                  <el-rate v-model="form.star" :max="5" show-score text-color="#ff9900" score-template="{value}"></el-rate>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="服务价格" prop="service_price">
                  <el-input-number v-model="form.service_price" :min="0" :precision="2" placeholder="请输入服务价格">
                    <template slot="append">元</template>
                  </el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车费" prop="car_price">
                  <el-input-number v-model="form.car_price" :min="0" :precision="2" placeholder="请输入车费">
                    <template slot="append">元</template>
                  </el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="开始时间" prop="start_time">
                  <el-time-picker
                    v-model="form.start_time"
                    placeholder="请选择开始工作时间"
                    format="HH:mm"
                    value-format="HH:mm"
                    style="width: 100%"
                  ></el-time-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="end_time">
                  <el-time-picker
                    v-model="form.end_time"
                    placeholder="请选择结束工作时间"
                    format="HH:mm"
                    value-format="HH:mm"
                    style="width: 100%"
                  ></el-time-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="免车费距离" prop="free_fare_distance">
                  <el-input-number v-model="form.free_fare_distance" :min="0" :precision="2" placeholder="请输入免车费距离">
                    <template slot="append">km</template>
                  </el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车费承担方" prop="free_fare_bear">
                  <el-select v-model="form.free_fare_bear" placeholder="请选择车费承担方">
                    <el-option label="不开启" :value="0" />
                    <el-option label="平台" :value="1" />
                    <el-option label="技师" :value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="虚拟订单数" prop="order_num">
                  <el-input-number v-model="form.order_num" :min="0" placeholder="请输入虚拟订单数量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="虚拟排序" prop="index_top">
                  <el-input-number v-model="form.index_top" :min="0" placeholder="请输入排序值" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="图片信息" name="images">
            <el-form-item label="工作照片" prop="work_img">
              <image-upload v-model="form.work_img" :limit="1"/>
              <div class="el-upload__tip">建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>
            </el-form-item>
            <el-form-item label="个人照片" prop="self_img">
              <image-upload v-model="form.self_img" :limit="9"/>
              <div class="el-upload__tip">最多上传9张，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>
            </el-form-item>
            <el-form-item label="执照证书" prop="license">
              <image-upload v-model="form.license" :limit="3"/>
              <div class="el-upload__tip">最多上传3张，支持jpg、png格式，大小不超过2M</div>
            </el-form-item>
            <el-form-item label="个人视频" prop="video">
              <file-upload v-model="form.video" :limit="1" :file-type="['mp4', 'avi', 'mov']"/>
              <div class="el-upload__tip">支持mp4、avi、mov格式，大小不超过50M</div>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="位置信息" name="location">
            <el-form-item label="所在地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入详细地址" />
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="显示经度" prop="lng">
                  <el-input v-model="form.lng" placeholder="请输入显示经度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="显示纬度" prop="lat">
                  <el-input v-model="form.lat" placeholder="请输入显示纬度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="真实经度" prop="true_lng">
                  <el-input v-model="form.true_lng" placeholder="请输入真实经度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="真实纬度" prop="true_lat">
                  <el-input v-model="form.true_lat" placeholder="请输入真实纬度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="城市ID" prop="city_id">
                  <el-input-number v-model="form.city_id" :min="0" placeholder="请输入城市ID" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实时定位" prop="coach_position">
                  <el-radio-group v-model="form.coach_position">
                    <el-radio :label="0">关闭</el-radio>
                    <el-radio :label="1">开启</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="地址更新时间" prop="address_update_time">
              <el-date-picker
                v-model="form.address_update_time"
                type="datetime"
                placeholder="请选择地址更新时间"
                value-format="timestamp"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="其他设置" name="other">
            <el-row>
              <el-col :span="12">
                <el-form-item label="行业类型" prop="industry_type">
                  <el-input-number v-model="form.industry_type" :min="0" placeholder="请输入行业类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="技师类型" prop="type_id">
                  <el-input-number v-model="form.type_id" :min="0" placeholder="请输入技师类型" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="个性标签" prop="personality_icon">
                  <el-input-number v-model="form.personality_icon" :min="0" placeholder="请输入个性标签ID" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="岗位标签" prop="station_icon">
                  <el-input-number v-model="form.station_icon" :min="0" placeholder="请输入岗位标签ID" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="技师图标" prop="coach_icon">
                  <el-input-number v-model="form.coach_icon" :min="0" placeholder="请输入技师图标ID" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="推荐挂件" prop="recommend_icon">
                  <el-input-number v-model="form.recommend_icon" :min="0" placeholder="请输入推荐挂件ID" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="显示销量" prop="show_salenum">
                  <el-radio-group v-model="form.show_salenum">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理员添加" prop="admin_add">
                  <el-radio-group v-model="form.admin_add">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="虚拟收藏量" prop="virtual_collect">
                  <el-input-number v-model="form.virtual_collect" :min="0" placeholder="请输入虚拟收藏量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="虚拟评论量" prop="virtual_comment">
                  <el-input-number v-model="form.virtual_comment" :min="0" placeholder="请输入虚拟评论量" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="访问量" prop="pv">
                  <el-input-number v-model="form.pv" :min="0" placeholder="请输入访问量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="版本号" prop="version">
                  <el-input-number v-model="form.version" :min="0" placeholder="请输入版本号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="真实姓名" prop="true_user_name">
              <el-input v-model="form.true_user_name" placeholder="请输入真实姓名" />
            </el-form-item>
            <el-form-item label="身份证编码" prop="id_code">
              <el-input v-model="form.id_code" placeholder="请输入身份证编码" />
            </el-form-item>
            <el-form-item label="扩展数据" prop="data">
              <el-input v-model="form.data" type="textarea" :rows="3" placeholder="请输入扩展数据(JSON格式)" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
        
        <div style="text-align: center; margin-top: 20px;">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="goBack">取 消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCoach, addCoach, updateCoach } from "@/api/massage/coach";

export default {
  name: "CoachEdit",
  data() {
    return {
      // 标题
      title: "",
      // 激活的标签页
      activeName: "basic",
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        coach_name: [
          { required: true, message: "达人姓名不能为空", trigger: "blur" },
          { min: 2, max: 15, message: "姓名长度在 2 到 15 个字符", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        sex: [
          { required: true, message: "请选择性别", trigger: "change" }
        ],
        work_time: [
          { required: true, message: "请输入从业时间", trigger: "blur" },
          { type: "number", min: 0, max: 50, message: "从业时间应在0-50年之间", trigger: "blur" }
        ],
        height: [
          { pattern: /^\d{2,3}$/, message: "请输入正确的身高(cm)", trigger: "blur" }
        ],
        weight: [
          { pattern: /^\d{2,3}(\.\d)?$/, message: "请输入正确的体重(kg)", trigger: "blur" }
        ],
        id_card: [
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: "请输入正确的身份证号", trigger: "blur" }
        ],
        service_price: [
          { type: "number", min: 0, message: "服务价格不能小于0", trigger: "blur" }
        ],
        car_price: [
          { type: "number", min: 0, message: "车费不能小于0", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    const id = this.$route.query && this.$route.query.id;
    this.form.id = id || null;
    if (id) {
      this.title = "修改达人";
      this.getInfo(id);
    } else {
      this.title = "新增达人";
      this.reset();
    }
  },
  methods: {
    /** 查询达人详细 */
    getInfo(id) {
      getCoach(id).then(response => {
        this.form = response.data;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uniacid: 1,
        coach_name: null,
        user_id: null,
        mobile: null,
        id_card: null,
        sex: 1,
        work_time: null,
        city: null,
        lng: null,
        lat: null,
        address: null,
        text: null,
        license: null,
        work_img: null,
        status: 1,
        auth_status: 0,
        sh_time: null,
        sh_text: null,
        create_time: Math.floor(Date.now() / 1000),
        self_img: null,
        is_work: 1,
        start_time: null,
        end_time: null,
        service_price: null,
        car_price: null,
        id_code: null,
        star: 5.0,
        admin_add: 1,
        city_id: null,
        video: null,
        is_update: 0,
        integral: 0,
        order_num: 0,
        recommend: 0,
        balance_cash: 0,
        index_top: 0,
        coach_position: 0,
        near_time: null,
        check_cash: 0,
        agent_type: 1,
        partner_id: null,
        partner_time: null,
        total_order_num: 0,
        birthday: null,
        credit_value: 100,
        credit_top: 0,
        recommend_icon: null,
        free_fare_distance: 0,
        show_salenum: 1,
        coach_icon: null,
        true_lng: null,
        true_lat: null,
        type_id: null,
        version: 1,
        true_user_name: null,
        industry_type: null,
        height: null,
        weight: null,
        constellation: null,
        free_fare_bear: 0,
        personality_icon: null,
        station_icon: null,
        address_update_time: null,
        pv: 0,
        virtual_collect: 0,
        virtual_comment: 0,
        work_type: 1,
        data: null
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCoach(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.goBack();
            });
          } else {
            addCoach(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.goBack();
            });
          }
        }
      });
    },
    /** 返回 */
    goBack() {
      this.$router.push({ path: "/massage/coach" });
    }
  }
};
</script>
