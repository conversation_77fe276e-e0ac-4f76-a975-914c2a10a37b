<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ title }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基础信息</span>
          </div>

          <el-form-item label="真实姓名" prop="true_user_name">
            <el-input v-model="form.true_user_name" maxlength="15" show-word-limit placeholder="请输入真实姓名" />
          </el-form-item>

          <el-form-item label="身份证号" prop="id_card">
            <el-input v-model="form.id_card" placeholder="请输入身份证号" />
          </el-form-item>

          <el-form-item label="身份证照片" prop="license">
            <image-upload v-model="form.license" :limit="2"/>
            <div class="el-upload__tip">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>
          </el-form-item>

          <el-form-item label="生活照" prop="self_img">
            <image-upload v-model="form.self_img" :limit="9"/>
            <div class="el-upload__tip">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>
          </el-form-item>

          <el-row>
            <el-col :span="8">
              <el-form-item label="虚拟收藏量" prop="virtual_collect">
                <el-input-number v-model="form.virtual_collect" :min="0" placeholder="请输入虚拟收藏量" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="虚拟评论量" prop="virtual_comment">
                <el-input-number v-model="form.virtual_comment" :min="0" placeholder="请输入虚拟评论量" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="访问量" prop="pv">
                <el-input-number v-model="form.pv" :min="0" placeholder="请输入访问量" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        
        <div style="text-align: center; margin-top: 20px;">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="goBack">取 消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCoach, addCoach, updateCoach } from "@/api/massage/coach";

export default {
  name: "CoachEdit",
  data() {
    return {
      // 标题
      title: "",
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        true_user_name: [
          { required: true, message: "真实姓名不能为空", trigger: "blur" },
          { min: 2, max: 15, message: "姓名长度在 2 到 15 个字符", trigger: "blur" }
        ],
        id_card: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: "请输入正确的身份证号", trigger: "blur" }
        ],
        virtual_collect: [
          { type: "number", min: 0, message: "虚拟收藏量不能小于0", trigger: "blur" }
        ],
        virtual_comment: [
          { type: "number", min: 0, message: "虚拟评论量不能小于0", trigger: "blur" }
        ],
        pv: [
          { type: "number", min: 0, message: "访问量不能小于0", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    const id = this.$route.query && this.$route.query.id;
    this.form.id = id || null;
    if (id) {
      this.title = "修改达人";
      this.getInfo(id);
    } else {
      this.title = "新增达人";
      this.reset();
    }
  },
  methods: {
    /** 查询达人详细 */
    getInfo(id) {
      getCoach(id).then(response => {
        this.form = response.data;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        true_user_name: null,
        id_card: null,
        license: null,
        self_img: null,
        virtual_collect: 0,
        virtual_comment: 0,
        pv: 0
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCoach(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.goBack();
            });
          } else {
            addCoach(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.goBack();
            });
          }
        }
      });
    },
    /** 返回 */
    goBack() {
      this.$router.push({ path: "/massage/coach" });
    }
  }
};
</script>
