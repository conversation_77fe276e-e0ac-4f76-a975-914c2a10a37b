<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ title }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-tabs v-model="activeName" type="card">
        <!-- 基础信息 -->
        <el-tab-pane label="基础信息" name="basic">
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="真实姓名" prop="true_user_name">
              <el-input v-model="form.true_user_name" maxlength="15" show-word-limit placeholder="请输入真实姓名" />
            </el-form-item>

            <el-form-item label="身份证号" prop="id_card">
              <el-input v-model="form.id_card" placeholder="请输入身份证号" />
            </el-form-item>

            <el-form-item label="身份证照片" prop="license">
              <image-upload v-model="form.license" :limit="2"/>
              <div class="el-upload__tip">请上传身份证正反面照片，最多2张，支持jpg、png格式，大小不超过2M</div>
            </el-form-item>

            <el-form-item label="生活照" prop="self_img">
              <image-upload v-model="form.self_img" :limit="9"/>
              <div class="el-upload__tip">最多上传9张生活照，建议尺寸：750*750像素，支持jpg、png格式，大小不超过2M</div>
            </el-form-item>

            <el-form-item label="达人简介" prop="text">
              <el-input v-model="form.text" type="textarea" :rows="4" placeholder="请输入达人简介" />
            </el-form-item>

            <el-row>
              <el-col :span="8">
                <el-form-item label="虚拟收藏量" prop="virtual_collect">
                  <el-input-number v-model="form.virtual_collect" :min="0" placeholder="请输入虚拟收藏量" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="虚拟评论量" prop="virtual_comment">
                  <el-input-number v-model="form.virtual_comment" :min="0" placeholder="请输入虚拟评论量" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="访问量" prop="pv">
                  <el-input-number v-model="form.pv" :min="0" placeholder="请输入访问量" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <div style="text-align: center; margin-top: 20px;">
              <el-button type="primary" @click="submitForm">保 存</el-button>
              <el-button @click="goBack">取 消</el-button>
            </div>
          </el-form>
        </el-tab-pane>

        <!-- 接单时间 -->
        <el-tab-pane label="接单时间" name="workTime" v-if="form.id">
          <el-form ref="timeForm" :model="timeForm" :rules="timeRules" label-width="120px">
            <el-form-item label="是否接单" prop="is_work">
              <el-radio-group v-model="timeForm.is_work">
                <el-radio :label="1">接单</el-radio>
                <el-radio :label="0">休息</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="接单时间" prop="start_time" v-if="timeForm.is_work">
              <div style="display: flex; align-items: center;">
                <el-time-picker
                  v-model="timeForm.start_time"
                  placeholder="开始时间"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 150px"
                ></el-time-picker>
                <span style="margin: 0 10px;">至</span>
                <el-time-picker
                  v-model="timeForm.end_time"
                  placeholder="结束时间"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 150px"
                ></el-time-picker>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveWorkTime">保存接单时间</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 已关联服务 -->
        <el-tab-pane label="已关联服务" name="services" v-if="form.id">
          <div style="margin-bottom: 20px;">
            <el-button type="primary" @click="showServiceDialog = true">关联服务</el-button>
          </div>

          <el-table v-loading="serviceLoading" :data="serviceList" style="width: 100%">
            <el-table-column prop="id" label="服务ID" width="80" />
            <el-table-column prop="title" label="服务名称" />
            <el-table-column prop="price" label="服务价格" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.price }}元</span>
              </template>
            </el-table-column>
            <el-table-column prop="time_long" label="服务时长" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.time_long }}分钟</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="removeService(scope.row.id)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="serviceTotal > 0"
            :total="serviceTotal"
            :page.sync="serviceQueryParams.pageNum"
            :limit.sync="serviceQueryParams.pageSize"
            @pagination="getServiceList"
          />
        </el-tab-pane>

        <!-- 服务记录 -->
        <el-tab-pane label="服务记录" name="orders" v-if="form.id">
          <el-table v-loading="orderLoading" :data="orderList" style="width: 100%">
            <el-table-column prop="id" label="订单ID" width="80" />
            <el-table-column prop="order_code" label="订单号" width="180" />
            <el-table-column prop="goods_info" label="服务项目" />
            <el-table-column prop="pay_price" label="订单金额" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.pay_price }}元</span>
              </template>
            </el-table-column>
            <el-table-column prop="order_status" label="订单状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="getOrderStatusType(scope.row.order_status)">
                  {{ getOrderStatusText(scope.row.order_status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="下单时间" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="orderTotal > 0"
            :total="orderTotal"
            :page.sync="orderQueryParams.pageNum"
            :limit.sync="orderQueryParams.pageSize"
            @pagination="getOrderList"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 关联服务对话框 -->
    <el-dialog title="关联服务" :visible.sync="showServiceDialog" width="800px" append-to-body>
      <el-form :inline="true" :model="serviceSearchForm" class="demo-form-inline">
        <el-form-item label="服务名称">
          <el-input v-model="serviceSearchForm.title" placeholder="请输入服务名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchAvailableServices">查询</el-button>
          <el-button @click="resetServiceSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="serviceTable"
        v-loading="availableServiceLoading"
        :data="availableServiceList"
        @selection-change="handleServiceSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="服务ID" width="80" />
        <el-table-column prop="title" label="服务名称" />
        <el-table-column prop="price" label="价格" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.price }}元</span>
          </template>
        </el-table-column>
        <el-table-column prop="time_long" label="时长" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.time_long }}分钟</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="availableServiceTotal > 0"
        :total="availableServiceTotal"
        :page.sync="availableServiceQueryParams.pageNum"
        :limit.sync="availableServiceQueryParams.pageSize"
        @pagination="getAvailableServiceList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="showServiceDialog = false">取 消</el-button>
        <el-button type="primary" @click="addServices">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCoach, addCoach, updateCoach } from "@/api/massage/coach";
import {
  listCoachService,
  listAvailableService,
  addCoachService,
  delCoachService,
  getCoachWorkTime,
  saveCoachWorkTime,
  listCoachOrder
} from "@/api/massage/coachService";

export default {
  name: "CoachEdit",
  data() {
    return {
      // 标题
      title: "",
      // 激活的标签页
      activeName: "basic",
      // 表单参数
      form: {},
      // 接单时间表单
      timeForm: {
        is_work: 1,
        start_time: null,
        end_time: null
      },
      // 表单校验
      rules: {
        true_user_name: [
          { required: true, message: "真实姓名不能为空", trigger: "blur" },
          { min: 2, max: 15, message: "姓名长度在 2 到 15 个字符", trigger: "blur" }
        ],
        id_card: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: "请输入正确的身份证号", trigger: "blur" }
        ],
        virtual_collect: [
          { type: "number", min: 0, message: "虚拟收藏量不能小于0", trigger: "blur" }
        ],
        virtual_comment: [
          { type: "number", min: 0, message: "虚拟评论量不能小于0", trigger: "blur" }
        ],
        pv: [
          { type: "number", min: 0, message: "访问量不能小于0", trigger: "blur" }
        ]
      },
      // 接单时间校验
      timeRules: {
        is_work: [
          { required: true, message: "请选择是否接单", trigger: "change" }
        ]
      },
      // 已关联服务
      serviceList: [],
      serviceLoading: false,
      serviceTotal: 0,
      serviceQueryParams: {
        pageNum: 1,
        pageSize: 10,
        coachId: null
      },
      // 可关联服务
      showServiceDialog: false,
      availableServiceList: [],
      availableServiceLoading: false,
      availableServiceTotal: 0,
      availableServiceQueryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null
      },
      serviceSearchForm: {
        title: null
      },
      selectedServices: [],
      // 服务记录
      orderList: [],
      orderLoading: false,
      orderTotal: 0,
      orderQueryParams: {
        pageNum: 1,
        pageSize: 10,
        coachId: null
      }
    };
  },
  created() {
    const id = this.$route.query && this.$route.query.id;
    this.form.id = id || null;
    if (id) {
      this.title = "修改达人";
      this.getInfo(id);
      this.serviceQueryParams.coachId = id;
      this.orderQueryParams.coachId = id;
      this.getWorkTime(id);
      this.getServiceList();
      this.getOrderList();
    } else {
      this.title = "新增达人";
      this.reset();
    }
  },
  methods: {
    /** 查询达人详细 */
    getInfo(id) {
      getCoach(id).then(response => {
        this.form = response.data;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        true_user_name: null,
        id_card: null,
        license: null,
        self_img: null,
        text: null,
        virtual_collect: 0,
        virtual_comment: 0,
        pv: 0
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCoach(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.goBack();
            });
          } else {
            addCoach(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.goBack();
            });
          }
        }
      });
    },
    /** 返回 */
    goBack() {
      this.$router.push({ path: "/massage/coach" });
    },

    /** 获取接单时间 */
    getWorkTime(id) {
      getCoachWorkTime(id).then(response => {
        this.timeForm = response.data || {
          is_work: 1,
          start_time: "09:00",
          end_time: "18:00"
        };
      }).catch(() => {
        // 如果获取失败，使用默认值
        this.timeForm = {
          is_work: 1,
          start_time: "09:00",
          end_time: "18:00"
        };
      });
    },

    /** 保存接单时间 */
    saveWorkTime() {
      this.$refs["timeForm"].validate(valid => {
        if (valid) {
          const data = {
            coachId: this.form.id,
            ...this.timeForm
          };
          saveCoachWorkTime(data).then(() => {
            this.$modal.msgSuccess("接单时间保存成功");
          });
        }
      });
    },

    /** 获取已关联服务列表 */
    getServiceList() {
      this.serviceLoading = true;
      listCoachService(this.serviceQueryParams).then(response => {
        this.serviceList = response.rows;
        this.serviceTotal = response.total;
        this.serviceLoading = false;
      }).catch(() => {
        this.serviceLoading = false;
      });
    },

    /** 获取可关联服务列表 */
    getAvailableServiceList() {
      this.availableServiceLoading = true;
      listAvailableService(this.availableServiceQueryParams).then(response => {
        this.availableServiceList = response.rows;
        this.availableServiceTotal = response.total;
        this.availableServiceLoading = false;
      }).catch(() => {
        this.availableServiceLoading = false;
      });
    },

    /** 搜索可关联服务 */
    searchAvailableServices() {
      this.availableServiceQueryParams.pageNum = 1;
      this.availableServiceQueryParams.title = this.serviceSearchForm.title;
      this.getAvailableServiceList();
    },

    /** 重置服务搜索 */
    resetServiceSearch() {
      this.serviceSearchForm.title = null;
      this.availableServiceQueryParams.title = null;
      this.getAvailableServiceList();
    },

    /** 服务选择变化 */
    handleServiceSelectionChange(selection) {
      this.selectedServices = selection;
    },

    /** 添加服务 */
    addServices() {
      if (this.selectedServices.length === 0) {
        this.$modal.msgError("请选择要关联的服务");
        return;
      }
      const data = {
        coachId: this.form.id,
        serviceIds: this.selectedServices.map(item => item.id)
      };
      addCoachService(data).then(() => {
        this.$modal.msgSuccess("服务关联成功");
        this.showServiceDialog = false;
        this.getServiceList();
      });
    },

    /** 移除服务 */
    removeService(serviceId) {
      this.$modal.confirm('确认要移除该服务吗？').then(() => {
        delCoachService(this.form.id, serviceId).then(() => {
          this.$modal.msgSuccess("服务移除成功");
          this.getServiceList();
        });
      });
    },

    /** 获取服务记录列表 */
    getOrderList() {
      this.orderLoading = true;
      listCoachOrder(this.orderQueryParams).then(response => {
        this.orderList = response.rows;
        this.orderTotal = response.total;
        this.orderLoading = false;
      }).catch(() => {
        this.orderLoading = false;
      });
    },

    /** 获取订单状态类型 */
    getOrderStatusType(status) {
      const statusMap = {
        1: 'warning',
        2: 'success',
        3: 'danger',
        4: 'info'
      };
      return statusMap[status] || 'info';
    },

    /** 获取订单状态文本 */
    getOrderStatusText(status) {
      const statusMap = {
        1: '待服务',
        2: '已完成',
        3: '已取消',
        4: '已退款'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>
