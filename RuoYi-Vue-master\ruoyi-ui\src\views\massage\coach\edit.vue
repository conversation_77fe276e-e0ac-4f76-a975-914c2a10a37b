<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ title }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="基础信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="达人姓名" prop="coach_name">
                  <el-input v-model="form.coach_name" placeholder="请输入达人姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="mobile">
                  <el-input v-model="form.mobile" placeholder="请输入手机号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="性别" prop="sex">
                  <el-select v-model="form.sex" placeholder="请选择性别">
                    <el-option label="男" :value="1" />
                    <el-option label="女" :value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="从业时间" prop="work_time">
                  <el-input-number v-model="form.work_time" :min="0" :max="50" placeholder="请输入从业年限" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="身份证号" prop="id_card">
                  <el-input v-model="form.id_card" placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="城市" prop="city">
                  <el-input v-model="form.city" placeholder="请输入城市" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="服务地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入服务地址" />
            </el-form-item>
            <el-form-item label="个人简介" prop="text">
              <el-input v-model="form.text" type="textarea" placeholder="请输入个人简介" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="工作信息" name="work">
            <el-row>
              <el-col :span="12">
                <el-form-item label="是否工作" prop="is_work">
                  <el-select v-model="form.is_work" placeholder="请选择工作状态">
                    <el-option label="工作中" :value="1" />
                    <el-option label="休息中" :value="0" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否推荐" prop="recommend">
                  <el-select v-model="form.recommend" placeholder="请选择是否推荐">
                    <el-option label="否" :value="0" />
                    <el-option label="是" :value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="服务价格" prop="service_price">
                  <el-input-number v-model="form.service_price" :min="0" :precision="2" placeholder="请输入服务价格" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车费" prop="car_price">
                  <el-input-number v-model="form.car_price" :min="0" :precision="2" placeholder="请输入车费" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="开始时间" prop="start_time">
                  <el-input v-model="form.start_time" placeholder="请输入开始工作时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="end_time">
                  <el-input v-model="form.end_time" placeholder="请输入结束工作时间" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="图片信息" name="images">
            <el-form-item label="工作照片" prop="work_img">
              <image-upload v-model="form.work_img"/>
            </el-form-item>
            <el-form-item label="个人照片" prop="self_img">
              <image-upload v-model="form.self_img"/>
            </el-form-item>
            <el-form-item label="执照" prop="license">
              <image-upload v-model="form.license"/>
            </el-form-item>
            <el-form-item label="视频" prop="video">
              <image-upload v-model="form.video"/>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="位置信息" name="location">
            <el-row>
              <el-col :span="12">
                <el-form-item label="经度" prop="lng">
                  <el-input v-model="form.lng" placeholder="请输入经度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度" prop="lat">
                  <el-input v-model="form.lat" placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="真实经度" prop="true_lng">
                  <el-input v-model="form.true_lng" placeholder="请输入真实经度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="真实纬度" prop="true_lat">
                  <el-input v-model="form.true_lat" placeholder="请输入真实纬度" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        
        <div style="text-align: center; margin-top: 20px;">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="goBack">取 消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCoach, addCoach, updateCoach } from "@/api/massage/coach";

export default {
  name: "CoachEdit",
  data() {
    return {
      // 标题
      title: "",
      // 激活的标签页
      activeName: "basic",
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        coach_name: [
          { required: true, message: "达人姓名不能为空", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    const id = this.$route.query && this.$route.query.id;
    this.form.id = id || null;
    if (id) {
      this.title = "修改达人";
      this.getInfo(id);
    } else {
      this.title = "新增达人";
      this.reset();
    }
  },
  methods: {
    /** 查询达人详细 */
    getInfo(id) {
      getCoach(id).then(response => {
        this.form = response.data;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        coach_name: null,
        mobile: null,
        sex: null,
        work_time: null,
        id_card: null,
        city: null,
        lng: null,
        lat: null,
        address: null,
        text: null,
        license: null,
        work_img: null,
        self_img: null,
        is_work: 1,
        start_time: null,
        end_time: null,
        service_price: null,
        car_price: null,
        recommend: 0,
        star: 5.0,
        video: null,
        true_lng: null,
        true_lat: null
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCoach(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.goBack();
            });
          } else {
            addCoach(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.goBack();
            });
          }
        }
      });
    },
    /** 返回 */
    goBack() {
      this.$router.push("/massage/coach");
    }
  }
};
</script>
