package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 按摩订单对象 ims_massage_service_order_list
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MassageOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderCode;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 技师ID */
    @Excel(name = "技师ID")
    private Long coachId;

    /** 服务ID */
    @Excel(name = "服务ID")
    private Long serviceId;

    /** 服务名称 */
    @Excel(name = "服务名称")
    private String serviceName;

    /** 服务时长(分钟) */
    @Excel(name = "服务时长")
    private Integer serviceDuration;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private Double totalPrice;

    /** 实付金额 */
    @Excel(name = "实付金额")
    private Double payPrice;

    /** 使用余额 */
    @Excel(name = "使用余额")
    private Double balance;

    /** 技师收入 */
    @Excel(name = "技师收入")
    private Double coachIncome;

    /** 平台收入 */
    @Excel(name = "平台收入")
    private Double platformIncome;

    /** 订单状态 1待支付 2已支付 3服务中 4已完成 5已取消 6已退款 */
    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=已支付,3=服务中,4=已完成,5=已取消,6=已退款")
    private Integer status;

    /** 支付方式 1微信支付 2支付宝 3余额支付 */
    @Excel(name = "支付方式", readConverterExp = "1=微信支付,2=支付宝,3=余额支付")
    private Integer payType;

    /** 支付时间 */
    @Excel(name = "支付时间")
    private Long payTime;

    /** 预约时间 */
    @Excel(name = "预约时间")
    private Long appointmentTime;

    /** 服务开始时间 */
    @Excel(name = "服务开始时间")
    private Long startTime;

    /** 服务结束时间 */
    @Excel(name = "服务结束时间")
    private Long endTime;

    /** 服务地址 */
    @Excel(name = "服务地址")
    private String serviceAddress;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 订单备注 */
    @Excel(name = "订单备注")
    private String remark;

    /** 评价星级 */
    @Excel(name = "评价星级")
    private Integer commentStar;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String commentContent;

    /** 评价时间 */
    @Excel(name = "评价时间")
    private Long commentTime;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private Double refundAmount;

    /** 退款时间 */
    @Excel(name = "退款时间")
    private Long refundTime;



    /** 应用ID */
    @Excel(name = "应用ID")
    private String uniacid;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setCoachId(Long coachId) 
    {
        this.coachId = coachId;
    }

    public Long getCoachId() 
    {
        return coachId;
    }

    public void setServiceId(Long serviceId) 
    {
        this.serviceId = serviceId;
    }

    public Long getServiceId() 
    {
        return serviceId;
    }

    public void setServiceName(String serviceName) 
    {
        this.serviceName = serviceName;
    }

    public String getServiceName() 
    {
        return serviceName;
    }

    public void setServiceDuration(Integer serviceDuration) 
    {
        this.serviceDuration = serviceDuration;
    }

    public Integer getServiceDuration() 
    {
        return serviceDuration;
    }

    public void setTotalPrice(Double totalPrice) 
    {
        this.totalPrice = totalPrice;
    }

    public Double getTotalPrice() 
    {
        return totalPrice;
    }

    public void setPayPrice(Double payPrice) 
    {
        this.payPrice = payPrice;
    }

    public Double getPayPrice()
    {
        return payPrice;
    }

    public void setBalance(Double balance)
    {
        this.balance = balance;
    }

    public Double getBalance()
    {
        return balance;
    }

    public void setCoachIncome(Double coachIncome) 
    {
        this.coachIncome = coachIncome;
    }

    public Double getCoachIncome() 
    {
        return coachIncome;
    }

    public void setPlatformIncome(Double platformIncome) 
    {
        this.platformIncome = platformIncome;
    }

    public Double getPlatformIncome() 
    {
        return platformIncome;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setPayType(Integer payType) 
    {
        this.payType = payType;
    }

    public Integer getPayType() 
    {
        return payType;
    }

    public void setPayTime(Long payTime) 
    {
        this.payTime = payTime;
    }

    public Long getPayTime() 
    {
        return payTime;
    }

    public void setAppointmentTime(Long appointmentTime) 
    {
        this.appointmentTime = appointmentTime;
    }

    public Long getAppointmentTime() 
    {
        return appointmentTime;
    }

    public void setStartTime(Long startTime) 
    {
        this.startTime = startTime;
    }

    public Long getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Long endTime) 
    {
        this.endTime = endTime;
    }

    public Long getEndTime() 
    {
        return endTime;
    }

    public void setServiceAddress(String serviceAddress) 
    {
        this.serviceAddress = serviceAddress;
    }

    public String getServiceAddress() 
    {
        return serviceAddress;
    }

    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }

    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }

    public void setCommentStar(Integer commentStar) 
    {
        this.commentStar = commentStar;
    }

    public Integer getCommentStar() 
    {
        return commentStar;
    }

    public void setCommentContent(String commentContent) 
    {
        this.commentContent = commentContent;
    }

    public String getCommentContent() 
    {
        return commentContent;
    }

    public void setCommentTime(Long commentTime) 
    {
        this.commentTime = commentTime;
    }

    public Long getCommentTime() 
    {
        return commentTime;
    }

    public void setRefundReason(String refundReason) 
    {
        this.refundReason = refundReason;
    }

    public String getRefundReason() 
    {
        return refundReason;
    }

    public void setRefundAmount(Double refundAmount) 
    {
        this.refundAmount = refundAmount;
    }

    public Double getRefundAmount() 
    {
        return refundAmount;
    }

    public void setRefundTime(Long refundTime) 
    {
        this.refundTime = refundTime;
    }

    public Long getRefundTime() 
    {
        return refundTime;
    }



    public void setUniacid(String uniacid) 
    {
        this.uniacid = uniacid;
    }

    public String getUniacid() 
    {
        return uniacid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderCode", getOrderCode())
            .append("userId", getUserId())
            .append("coachId", getCoachId())
            .append("serviceId", getServiceId())
            .append("serviceName", getServiceName())
            .append("serviceDuration", getServiceDuration())
            .append("totalPrice", getTotalPrice())
            .append("payPrice", getPayPrice())
            .append("coachIncome", getCoachIncome())
            .append("platformIncome", getPlatformIncome())
            .append("status", getStatus())
            .append("payType", getPayType())
            .append("payTime", getPayTime())
            .append("appointmentTime", getAppointmentTime())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("serviceAddress", getServiceAddress())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("remark", getRemark())
            .append("commentStar", getCommentStar())
            .append("commentContent", getCommentContent())
            .append("commentTime", getCommentTime())
            .append("refundReason", getRefundReason())
            .append("refundAmount", getRefundAmount())
            .append("refundTime", getRefundTime())
            .append("createTime", getCreateTime())
            .append("uniacid", getUniacid())
            .toString();
    }
}
