package com.ruoyi.massage.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单评价对象 ims_massage_service_order_comment
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MassageComment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Integer uniacid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 技师ID */
    @Excel(name = "技师ID")
    private Long coachId;

    /** 管理员ID */
    @Excel(name = "管理员ID")
    private Long adminId;

    /** 评价星级(1-5) */
    @Excel(name = "评价星级", readConverterExp = "1=1星,2=2星,3=3星,4=4星,5=5星")
    private Integer star;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String text;

    /** 状态(1正常 0隐藏 -1删除) */
    @Excel(name = "状态", readConverterExp = "1=正常,0=隐藏,-1=删除")
    private Integer status;

    /** 是否置顶(1是 0否) */
    @Excel(name = "是否置顶", readConverterExp = "1=是,0=否")
    private Integer top;

    /** 创建时间戳 */
    @Excel(name = "创建时间戳")
    private Long createTimestamp;

    /** 创建时间戳2 */
    @Excel(name = "创建时间戳2")
    private Long createdTime;

    // 关联信息
    /** 用户昵称 */
    private String userNickname;

    /** 用户头像 */
    private String userAvatar;

    /** 订单编号 */
    private String orderCode;

    /** 技师姓名 */
    private String coachName;

    /** 评价标签列表 */
    private List<String> lableList;

    /** 服务评价列表 */
    private List<MassageCommentGoods> serviceList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Integer uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Integer getUniacid() 
    {
        return uniacid;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setCoachId(Long coachId) 
    {
        this.coachId = coachId;
    }

    public Long getCoachId() 
    {
        return coachId;
    }
    public void setAdminId(Long adminId) 
    {
        this.adminId = adminId;
    }

    public Long getAdminId() 
    {
        return adminId;
    }
    public void setStar(Integer star) 
    {
        this.star = star;
    }

    public Integer getStar() 
    {
        return star;
    }
    public void setText(String text) 
    {
        this.text = text;
    }

    public String getText() 
    {
        return text;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setTop(Integer top) 
    {
        this.top = top;
    }

    public Integer getTop() 
    {
        return top;
    }
    public void setCreateTimestamp(Long createTimestamp) 
    {
        this.createTimestamp = createTimestamp;
    }

    public Long getCreateTimestamp() 
    {
        return createTimestamp;
    }
    public void setCreatedTime(Long createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Long getCreatedTime() 
    {
        return createdTime;
    }

    public String getUserNickname() {
        return userNickname;
    }

    public void setUserNickname(String userNickname) {
        this.userNickname = userNickname;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getCoachName() {
        return coachName;
    }

    public void setCoachName(String coachName) {
        this.coachName = coachName;
    }

    public List<String> getLableList() {
        return lableList;
    }

    public void setLableList(List<String> lableList) {
        this.lableList = lableList;
    }

    public List<MassageCommentGoods> getServiceList() {
        return serviceList;
    }

    public void setServiceList(List<MassageCommentGoods> serviceList) {
        this.serviceList = serviceList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("userId", getUserId())
            .append("orderId", getOrderId())
            .append("coachId", getCoachId())
            .append("adminId", getAdminId())
            .append("star", getStar())
            .append("text", getText())
            .append("status", getStatus())
            .append("top", getTop())
            .append("createTimestamp", getCreateTimestamp())
            .append("createdTime", getCreatedTime())
            .toString();
    }
}
