package com.ruoyi.massage.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.massage.domain.MassageCoachOrder;
import com.ruoyi.massage.service.IMassageCoachOrderService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 达人服务记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/massage/coachOrder")
public class MassageCoachOrderController extends BaseController
{
    @Autowired
    private IMassageCoachOrderService massageCoachOrderService;

    /**
     * 查询达人服务记录列表
     */
    @PreAuthorize("@ss.hasPermi('massage:coach:query')")
    @GetMapping("/list")
    public TableDataInfo list(MassageCoachOrder massageCoachOrder)
    {
        startPage();
        List<MassageCoachOrder> list = massageCoachOrderService.selectMassageCoachOrderList(massageCoachOrder);
        return getDataTable(list);
    }
}
