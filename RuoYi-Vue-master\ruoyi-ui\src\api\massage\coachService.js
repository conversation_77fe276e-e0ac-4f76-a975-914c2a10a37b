import request from '@/utils/request'

// 查询达人已关联服务列表
export function listCoachService(query) {
  return request({
    url: '/massage/coachService/list',
    method: 'get',
    params: query
  })
}

// 查询达人可关联服务列表
export function listAvailableService(query) {
  return request({
    url: '/massage/coachService/available',
    method: 'get',
    params: query
  })
}

// 关联服务
export function addCoachService(data) {
  return request({
    url: '/massage/coachService',
    method: 'post',
    data: data
  })
}

// 移除关联服务
export function delCoachService(coachId, serviceId) {
  return request({
    url: '/massage/coachService/' + coachId + '/' + serviceId,
    method: 'delete'
  })
}

// 获取达人接单时间
export function getCoachWorkTime(coachId) {
  return request({
    url: '/massage/coachWorkTime/' + coachId,
    method: 'get'
  })
}

// 保存达人接单时间
export function saveCoachWorkTime(data) {
  return request({
    url: '/massage/coachWorkTime',
    method: 'post',
    data: data
  })
}

// 查询达人服务记录
export function listCoachOrder(query) {
  return request({
    url: '/massage/coachOrder/list',
    method: 'get',
    params: query
  })
}
