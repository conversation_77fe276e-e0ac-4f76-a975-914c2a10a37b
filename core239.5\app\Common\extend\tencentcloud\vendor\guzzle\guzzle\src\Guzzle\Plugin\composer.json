{"name": "guzzle/plugin", "description": "Guzzle plugin component containing all Guzzle HTTP plugins", "homepage": "http://guzzlephp.org/", "keywords": ["http", "client", "plugin", "extension", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/http": "self.version"}, "suggest": {"guzzle/cache": "self.version", "guzzle/log": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Plugin": ""}}, "target-dir": "Guzzle/Plugin", "replace": {"guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version"}, "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}