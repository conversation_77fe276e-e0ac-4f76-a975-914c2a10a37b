package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 达人接单时间对象
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public class MassageCoachWorkTime extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 达人ID */
    @Excel(name = "达人ID")
    private Long coachId;

    /** 是否接单(0:休息 1:接单) */
    @Excel(name = "是否接单")
    private Integer isWork;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    private String endTime;

    public void setCoachId(Long coachId) 
    {
        this.coachId = coachId;
    }

    public Long getCoachId() 
    {
        return coachId;
    }
    public void setIsWork(Integer isWork) 
    {
        this.isWork = isWork;
    }

    public Integer getIsWork() 
    {
        return isWork;
    }
    public void setStartTime(String startTime) 
    {
        this.startTime = startTime;
    }

    public String getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(String endTime) 
    {
        this.endTime = endTime;
    }

    public String getEndTime() 
    {
        return endTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("coachId", getCoachId())
            .append("isWork", getIsWork())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .toString();
    }
}
