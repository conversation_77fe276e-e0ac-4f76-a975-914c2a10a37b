{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue?vue&type=template&id=64325432&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue", "mtime": 1753765086018}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753582865879}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}