[{"name": "guzzle/guzzle", "version": "v3.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle3.git", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle3/zipball/0645b70d953bc1c067bbc8d5bc53194706b628d9", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.3", "symfony/event-dispatcher": "~2.1"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}, "require-dev": {"doctrine/cache": "~1.3", "monolog/monolog": "~1.0", "phpunit/phpunit": "3.7.*", "psr/log": "~1.0", "symfony/class-loader": "~2.1", "zendframework/zend-cache": "2.*,<2.3", "zendframework/zend-log": "2.*,<2.3"}, "suggest": {"guzzlehttp/guzzle": "Guzzle 5 has moved to a new package name. The package you have installed, Guzzle 3, is deprecated."}, "time": "2015-03-18T18:23:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "description": "PHP HTTP client. This library is deprecated in favor of https://packagist.org/packages/guzzlehttp/guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "abandoned": "guzzlehttp/guzzle"}, {"name": "symfony/event-dispatcher", "version": "v2.8.49", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "a77e974a5fecb4398833b0709210e3d5e334ffb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a77e974a5fecb4398833b0709210e3d5e334ffb0", "reference": "a77e974a5fecb4398833b0709210e3d5e334ffb0", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^2.0.5|~3.0.0", "symfony/dependency-injection": "~2.6|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2018-11-21T14:20:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com"}]