<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-content">
            <div class="stat-title">总用户数</div>
            <div class="stat-value">{{ overviewData.totalUsers || 0 }}</div>
            <div class="stat-desc">
              <span :class="overviewData.userGrowthRate >= 0 ? 'growth-up' : 'growth-down'">
                {{ overviewData.userGrowthRate >= 0 ? '↑' : '↓' }} {{ Math.abs(overviewData.userGrowthRate || 0) }}%
              </span>
              较昨日
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <i class="el-icon-s-custom"></i>
          </div>
          <div class="stat-content">
            <div class="stat-title">总技师数</div>
            <div class="stat-value">{{ overviewData.totalCoaches || 0 }}</div>
            <div class="stat-desc">
              <span :class="overviewData.coachGrowthRate >= 0 ? 'growth-up' : 'growth-down'">
                {{ overviewData.coachGrowthRate >= 0 ? '↑' : '↓' }} {{ Math.abs(overviewData.coachGrowthRate || 0) }}%
              </span>
              较昨日
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-content">
            <div class="stat-title">总订单数</div>
            <div class="stat-value">{{ overviewData.totalOrders || 0 }}</div>
            <div class="stat-desc">
              <span :class="overviewData.orderGrowthRate >= 0 ? 'growth-up' : 'growth-down'">
                {{ overviewData.orderGrowthRate >= 0 ? '↑' : '↓' }} {{ Math.abs(overviewData.orderGrowthRate || 0) }}%
              </span>
              较昨日
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            <i class="el-icon-money"></i>
          </div>
          <div class="stat-content">
            <div class="stat-title">总营收</div>
            <div class="stat-value">¥{{ (overviewData.totalRevenue || 0).toLocaleString() }}</div>
            <div class="stat-desc">
              <span :class="overviewData.revenueGrowthRate >= 0 ? 'growth-up' : 'growth-down'">
                {{ overviewData.revenueGrowthRate >= 0 ? '↑' : '↓' }} {{ Math.abs(overviewData.revenueGrowthRate || 0) }}%
              </span>
              较昨日
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 今日数据 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="24">
        <el-card class="today-stats">
          <div slot="header" class="card-header">
            <span>今日数据</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTodayStats">刷新</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">{{ todayStats.newUsers || 0 }}</div>
                <div class="today-label">新增用户</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">{{ todayStats.newOrders || 0 }}</div>
                <div class="today-label">新增订单</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">{{ todayStats.completedOrders || 0 }}</div>
                <div class="today-label">完成订单</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">¥{{ (todayStats.todayRevenue || 0).toLocaleString() }}</div>
                <div class="today-label">今日营收</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">{{ todayStats.onlineCoaches || 0 }}</div>
                <div class="today-label">在线技师</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="today-item">
                <div class="today-value">{{ todayStats.activeUsers || 0 }}</div>
                <div class="today-label">活跃用户</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <!-- 订单趋势图 -->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>订单趋势</span>
            <el-date-picker
              v-model="orderTrendDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              style="float: right;"
              @change="getOrderTrendData"
            />
          </div>
          <div ref="orderTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <!-- 收入趋势图 -->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>收入趋势</span>
            <el-date-picker
              v-model="incomeTrendDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              style="float: right;"
              @change="getIncomeTrendData"
            />
          </div>
          <div ref="incomeTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb20">
      <!-- 服务统计饼图 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>热门服务</span>
          </div>
          <div ref="serviceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <!-- 城市分布 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>城市分布</span>
          </div>
          <div ref="cityChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <!-- 订单状态分布 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>订单状态</span>
          </div>
          <div ref="orderStatusChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20">
      <!-- 优秀技师排行 -->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>优秀技师排行</span>
          </div>
          <div class="ranking-list">
            <div v-for="(coach, index) in topCoaches" :key="coach.id" class="ranking-item">
              <div class="ranking-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
              <div class="ranking-avatar">
                <img :src="coach.avatarUrl" :alt="coach.coachName" />
              </div>
              <div class="ranking-info">
                <div class="ranking-name">{{ coach.coachName }}</div>
                <div class="ranking-desc">服务{{ coach.serviceCount }}次 · 好评{{ coach.goodRate }}%</div>
              </div>
              <div class="ranking-value">¥{{ coach.totalIncome }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <!-- 热门服务排行 */
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>热门服务排行</span>
          </div>
          <div class="ranking-list">
            <div v-for="(service, index) in popularServices" :key="service.id" class="ranking-item">
              <div class="ranking-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
              <div class="ranking-avatar">
                <img :src="service.serviceImg" :alt="service.serviceName" />
              </div>
              <div class="ranking-info">
                <div class="ranking-name">{{ service.serviceName }}</div>
                <div class="ranking-desc">销量{{ service.salesCount }} · 好评{{ service.goodRate }}%</div>
              </div>
              <div class="ranking-value">¥{{ service.servicePrice }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { 
  getOverview, 
  getTodayStats, 
  getOrderTrend, 
  getIncomeTrend, 
  getServiceStats, 
  getCityDistribution, 
  getOrderStatusDistribution,
  getTopCoaches,
  getPopularServices
} from "@/api/massage/dashboard";

export default {
  name: "Dashboard",
  data() {
    return {
      // 概览数据
      overviewData: {},
      // 今日数据
      todayStats: {},
      // 日期范围
      orderTrendDateRange: [],
      incomeTrendDateRange: [],
      // 图表实例
      orderTrendChart: null,
      incomeTrendChart: null,
      serviceChart: null,
      cityChart: null,
      orderStatusChart: null,
      // 排行榜数据
      topCoaches: [],
      popularServices: []
    };
  },
  mounted() {
    this.initData();
    this.initCharts();
    // 设置定时刷新
    this.timer = setInterval(() => {
      this.refreshRealTimeData();
    }, 30000); // 30秒刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    // 销毁图表实例
    if (this.orderTrendChart) this.orderTrendChart.dispose();
    if (this.incomeTrendChart) this.incomeTrendChart.dispose();
    if (this.serviceChart) this.serviceChart.dispose();
    if (this.cityChart) this.cityChart.dispose();
    if (this.orderStatusChart) this.orderStatusChart.dispose();
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getOverviewData();
      await this.getTodayStatsData();
      await this.getRankingData();
    },
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initOrderTrendChart();
        this.initIncomeTrendChart();
        this.initServiceChart();
        this.initCityChart();
        this.initOrderStatusChart();
      });
    },
    // 获取概览数据
    async getOverviewData() {
      try {
        const response = await getOverview();
        this.overviewData = response.data;
      } catch (error) {
        console.error('获取概览数据失败:', error);
      }
    },
    // 获取今日数据
    async getTodayStatsData() {
      try {
        const response = await getTodayStats();
        this.todayStats = response.data;
      } catch (error) {
        console.error('获取今日数据失败:', error);
      }
    },
    // 获取排行榜数据
    async getRankingData() {
      try {
        const [coachResponse, serviceResponse] = await Promise.all([
          getTopCoaches(),
          getPopularServices()
        ]);
        this.topCoaches = coachResponse.data;
        this.popularServices = serviceResponse.data;
      } catch (error) {
        console.error('获取排行榜数据失败:', error);
      }
    },
    // 刷新今日数据
    refreshTodayStats() {
      this.getTodayStatsData();
    },
    // 刷新实时数据
    refreshRealTimeData() {
      this.getTodayStatsData();
    },
    // 初始化订单趋势图
    initOrderTrendChart() {
      this.orderTrendChart = echarts.init(this.$refs.orderTrendChart);
      this.getOrderTrendData();
    },
    // 初始化收入趋势图
    initIncomeTrendChart() {
      this.incomeTrendChart = echarts.init(this.$refs.incomeTrendChart);
      this.getIncomeTrendData();
    },
    // 初始化服务统计图
    initServiceChart() {
      this.serviceChart = echarts.init(this.$refs.serviceChart);
      this.getServiceStatsData();
    },
    // 初始化城市分布图
    initCityChart() {
      this.cityChart = echarts.init(this.$refs.cityChart);
      this.getCityDistributionData();
    },
    // 初始化订单状态图
    initOrderStatusChart() {
      this.orderStatusChart = echarts.init(this.$refs.orderStatusChart);
      this.getOrderStatusData();
    },
    // 获取订单趋势数据
    async getOrderTrendData() {
      try {
        const dateRange = this.orderTrendDateRange;
        const startDate = dateRange && dateRange[0] ? this.formatDate(dateRange[0]) : '';
        const endDate = dateRange && dateRange[1] ? this.formatDate(dateRange[1]) : '';
        
        const response = await getOrderTrend(startDate, endDate);
        const data = response.data;
        
        const option = {
          title: {
            text: '订单趋势',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: data.dates || []
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '订单数量',
            type: 'line',
            data: data.orders || [],
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          }]
        };
        
        this.orderTrendChart.setOption(option);
      } catch (error) {
        console.error('获取订单趋势数据失败:', error);
      }
    },
    // 获取收入趋势数据
    async getIncomeTrendData() {
      try {
        const dateRange = this.incomeTrendDateRange;
        const startDate = dateRange && dateRange[0] ? this.formatDate(dateRange[0]) : '';
        const endDate = dateRange && dateRange[1] ? this.formatDate(dateRange[1]) : '';
        
        const response = await getIncomeTrend(startDate, endDate);
        const data = response.data;
        
        const option = {
          title: {
            text: '收入趋势',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}<br/>{a}: ¥{c}'
          },
          xAxis: {
            type: 'category',
            data: data.dates || []
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '收入金额',
            type: 'bar',
            data: data.income || [],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            }
          }]
        };
        
        this.incomeTrendChart.setOption(option);
      } catch (error) {
        console.error('获取收入趋势数据失败:', error);
      }
    },
    // 获取服务统计数据
    async getServiceStatsData() {
      try {
        const response = await getServiceStats();
        const data = response.data;
        
        const option = {
          title: {
            text: '热门服务',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a}<br/>{b}: {c} ({d}%)'
          },
          series: [{
            name: '服务统计',
            type: 'pie',
            radius: '50%',
            data: data || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        
        this.serviceChart.setOption(option);
      } catch (error) {
        console.error('获取服务统计数据失败:', error);
      }
    },
    // 获取城市分布数据
    async getCityDistributionData() {
      try {
        const response = await getCityDistribution();
        const data = response.data;
        
        const option = {
          title: {
            text: '城市分布',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{
            name: '城市分布',
            type: 'pie',
            radius: ['40%', '70%'],
            data: data || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        
        this.cityChart.setOption(option);
      } catch (error) {
        console.error('获取城市分布数据失败:', error);
      }
    },
    // 获取订单状态数据
    async getOrderStatusData() {
      try {
        const response = await getOrderStatusDistribution();
        const data = response.data;
        
        const option = {
          title: {
            text: '订单状态',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{
            name: '订单状态',
            type: 'pie',
            radius: ['40%', '70%'],
            data: data || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        
        this.orderStatusChart.setOption(option);
      } catch (error) {
        console.error('获取订单状态数据失败:', error);
      }
    },
    // 格式化日期
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.mb20 {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    
    i {
      font-size: 24px;
      color: white;
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 5px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }
    
    .stat-desc {
      font-size: 12px;
      color: #999;
      
      .growth-up {
        color: #67c23a;
      }
      
      .growth-down {
        color: #f56c6c;
      }
    }
  }
}

.today-stats {
  .today-item {
    text-align: center;
    
    .today-value {
      font-size: 20px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }
    
    .today-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .ranking-number {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: white;
      margin-right: 10px;
      
      &.rank-1 {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
      }
      
      &.rank-2 {
        background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
      }
      
      &.rank-3 {
        background: linear-gradient(135deg, #cd7f32, #daa520);
      }
      
      &:not(.rank-1):not(.rank-2):not(.rank-3) {
        background: #909399;
      }
    }
    
    .ranking-avatar {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .ranking-info {
      flex: 1;
      
      .ranking-name {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        margin-bottom: 2px;
      }
      
      .ranking-desc {
        font-size: 12px;
        color: #999;
      }
    }
    
    .ranking-value {
      font-size: 14px;
      font-weight: bold;
      color: #409EFF;
    }
  }
}
</style>
