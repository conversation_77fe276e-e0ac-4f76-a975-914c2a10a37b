package com.ruoyi.massage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 达人服务关联对象 ims_massage_service_coach_service_list
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public class MassageCoachService extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private Long uniacid;

    /** 达人ID */
    @Excel(name = "达人ID")
    private Long coachId;

    /** 服务ID */
    @Excel(name = "服务ID")
    private Long serviceId;

    /** 服务标题 */
    @Excel(name = "服务标题")
    private String title;

    /** 服务价格 */
    @Excel(name = "服务价格")
    private Double price;

    /** 服务时长(分钟) */
    @Excel(name = "服务时长")
    private Integer timeLong;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUniacid(Long uniacid) 
    {
        this.uniacid = uniacid;
    }

    public Long getUniacid() 
    {
        return uniacid;
    }
    public void setCoachId(Long coachId) 
    {
        this.coachId = coachId;
    }

    public Long getCoachId() 
    {
        return coachId;
    }
    public void setServiceId(Long serviceId) 
    {
        this.serviceId = serviceId;
    }

    public Long getServiceId() 
    {
        return serviceId;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setPrice(Double price) 
    {
        this.price = price;
    }

    public Double getPrice() 
    {
        return price;
    }
    public void setTimeLong(Integer timeLong) 
    {
        this.timeLong = timeLong;
    }

    public Integer getTimeLong() 
    {
        return timeLong;
    }
    public void setCreateTime(Long createTime) 
    {
        this.createTime = createTime;
    }

    public Long getCreateTime() 
    {
        return createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uniacid", getUniacid())
            .append("coachId", getCoachId())
            .append("serviceId", getServiceId())
            .append("title", getTitle())
            .append("price", getPrice())
            .append("timeLong", getTimeLong())
            .append("createTime", getCreateTime())
            .toString();
    }
}
