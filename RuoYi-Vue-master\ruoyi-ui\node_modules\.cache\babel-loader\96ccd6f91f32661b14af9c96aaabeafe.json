{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\index.vue", "mtime": 1753759965053}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9saS9EZXNrdG9wL3BlaXdhbjIvUnVvWWktVnVlLW1hc3Rlci9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IubWFwLmpzIik7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvbGkvRGVza3RvcC9wZWl3YW4yL1J1b1lpLVZ1ZS1tYXN0ZXIvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMi5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkM6L1VzZXJzL2xpL0Rlc2t0b3AvcGVpd2FuMi9SdW9ZaS1WdWUtbWFzdGVyL3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2RlZmluZVByb3BlcnR5LmpzIikpOwp2YXIgX3VzZXIgPSByZXF1aXJlKCJAL2FwaS9tYXNzYWdlL3VzZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJVc2VyIiwKICBkaWN0czogWydzeXNfbm9ybWFsX2Rpc2FibGUnLCAnc3lzX3VzZXJfc2V4J10sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSh7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDmjInmkannlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOS9memineiwg+aVtOW8ueWHuuWxggogICAgICBiYWxhbmNlT3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5pY2tOYW1lOiBudWxsLAogICAgICAgIHBob25lOiBudWxsLAogICAgICAgIHN0YXR1czogbnVsbCwKICAgICAgICBjaXR5OiBudWxsLAogICAgICAgIGdlbmRlcjogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOW9k+WJjeaTjeS9nOeahOeUqOaItwogICAgICBjdXJyZW50VXNlcjoge30sCiAgICAgIC8vIOS9memineiwg+aVtOihqOWNlQogICAgICBiYWxhbmNlRm9ybToge30KICAgIH0sICJiYWxhbmNlT3BlbiIsIGZhbHNlKSwgImdyb3d0aEZvcm0iLCB7fSksICJncm93dGhPcGVuIiwgZmFsc2UpLCAicnVsZXMiLCB7CiAgICAgIG5pY2tOYW1lOiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIG1lc3NhZ2U6ICLnlKjmiLfmmLXnp7DkuI3og73kuLrnqboiLAogICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICB9XSwKICAgICAgcGhvbmU6IFt7CiAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgbWVzc2FnZTogIuaJi+acuuWPt+S4jeiDveS4uuepuiIsCiAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgIH0sIHsKICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgIH1dCiAgICB9KSwgImJhbGFuY2VSdWxlcyIsIHsKICAgICAgYW1vdW50OiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIG1lc3NhZ2U6ICLosIPmlbTph5Hpop3kuI3og73kuLrnqboiLAogICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICB9XSwKICAgICAgcmVtYXJrOiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIG1lc3NhZ2U6ICLosIPmlbTlpIfms6jkuI3og73kuLrnqboiLAogICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICB9XQogICAgfSksICJncm93dGhSdWxlcyIsIHsKICAgICAgZ3Jvd3RoOiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIG1lc3NhZ2U6ICLosIPmlbTmiJDplb/lgLzkuI3og73kuLrnqboiLAogICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICB9XSwKICAgICAgcmVtYXJrOiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIG1lc3NhZ2U6ICLosIPmlbTlpIfms6jkuI3og73kuLrnqboiLAogICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICB9XQogICAgfSk7CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5oyJ5pGp55So5oi35YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF91c2VyLmxpc3RVc2VyKSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLnVzZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHVuaWFjaWQ6IG51bGwsCiAgICAgICAgb3BlbmlkOiBudWxsLAogICAgICAgIG5pY2tOYW1lOiBudWxsLAogICAgICAgIGF2YXRhclVybDogbnVsbCwKICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHN0YXR1czogMSwKICAgICAgICBjYXBJZDogbnVsbCwKICAgICAgICBjaXR5OiBudWxsLAogICAgICAgIGNvdW50cnk6IG51bGwsCiAgICAgICAgZ2VuZGVyOiBudWxsLAogICAgICAgIGxhbmd1YWdlOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIGJhbGFuY2U6IDAsCiAgICAgICAgcGhvbmU6IG51bGwsCiAgICAgICAgc2Vzc2lvbktleTogbnVsbCwKICAgICAgICBwaWQ6IG51bGwsCiAgICAgICAgY2FzaDogMCwKICAgICAgICB1bmlvbmlkOiBudWxsLAogICAgICAgIGFwcE9wZW5pZDogbnVsbCwKICAgICAgICB3ZWJPcGVuaWQ6IG51bGwsCiAgICAgICAgd2VjaGF0T3BlbmlkOiBudWxsLAogICAgICAgIGxhc3RMb2dpblR5cGU6IG51bGwsCiAgICAgICAgbmV3Q2FzaDogbnVsbCwKICAgICAgICBsb2NrOiBudWxsLAogICAgICAgIGlzRng6IG51bGwsCiAgICAgICAgaW9zT3BlbmlkOiBudWxsLAogICAgICAgIHB1c2hJZDogbnVsbCwKICAgICAgICBhbGlwYXlOdW1iZXI6IG51bGwsCiAgICAgICAgYWxpcGF5TmFtZTogbnVsbCwKICAgICAgICBpcDogbnVsbCwKICAgICAgICBncm93dGg6IDAsCiAgICAgICAgbWVtYmVyQ2FsY3VsYXRlVGltZTogbnVsbCwKICAgICAgICB1c2VyU3RhdHVzOiBudWxsLAogICAgICAgIGlzUXI6IG51bGwsCiAgICAgICAgc291cmNlVHlwZTogbnVsbCwKICAgICAgICBhcmVhOiBudWxsLAogICAgICAgIGFkbWluSWQ6IG51bGwsCiAgICAgICAgaXNQb3BJbWc6IG51bGwsCiAgICAgICAgbWVtYmVyRGlzY291bnRUaW1lOiBudWxsLAogICAgICAgIG1lbWJlckRpc2NvdW50SWQ6IG51bGwsCiAgICAgICAgcGFydG5lck1vbmV5OiBudWxsLAogICAgICAgIHRvdGFsUGFydG5lck1vbmV5OiBudWxsLAogICAgICAgIGRlbFVzZXJJZDogbnVsbCwKICAgICAgICBhbGlwYXlJZENvZGU6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDmjInmkannlKjmiLciOwogICAgfSwKICAgIC8qKiDmn6XnnIvor6bmg4Xmk43kvZwgKi9oYW5kbGVWaWV3OiBmdW5jdGlvbiBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogJy9tYXNzYWdlL3VzZXIvZGV0YWlsLycgKyByb3cuaWQKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgICgwLCBfdXNlci5nZXRVc2VyKShpZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXMyLm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzMi50aXRsZSA9ICLkv67mlLnmjInmkannlKjmiLciOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgICgwLCBfdXNlci51cGRhdGVVc2VyKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAoMCwgX3VzZXIuYWRkVXNlcikoX3RoaXMzLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdmFyIGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5oyJ5pGp55So5oi357yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX3VzZXIuZGVsVXNlcikoaWRzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LmdldExpc3QoKTsKICAgICAgICBfdGhpczQuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdtYXNzYWdlL3VzZXIvZXhwb3J0JywgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKSwgInVzZXJfIi5jb25jYXQobmV3IERhdGUoKS5nZXRUaW1lKCksICIueGxzeCIpKTsKICAgIH0sCiAgICAvKiog55So5oi354q25oCB5L+u5pS5ICovaGFuZGxlU3RhdHVzQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09IDEgPyAi5ZCv55SoIiA6ICLlgZznlKgiOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93Lm5pY2tOYW1lICsgJyLnlKjmiLflkJfvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF91c2VyLmNoYW5nZVN0YXR1cykocm93LmlkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gMSA/IDIgOiAxOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5LiL5ouJ6I+c5Y2V5ZG95Luk5aSE55CGICovaGFuZGxlRHJvcGRvd25Db21tYW5kOiBmdW5jdGlvbiBoYW5kbGVEcm9wZG93bkNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXIgPSByb3c7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgJ2JhbGFuY2UnOgogICAgICAgICAgdGhpcy5oYW5kbGVBZGp1c3RCYWxhbmNlKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICdncm93dGgnOgogICAgICAgICAgdGhpcy5oYW5kbGVBZGp1c3RHcm93dGgocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ2JsYWNrbGlzdCc6CiAgICAgICAgICB0aGlzLmhhbmRsZUJsYWNrbGlzdFVzZXIocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLyoqIOS9memineiwg+aVtCAqL2hhbmRsZUFkanVzdEJhbGFuY2U6IGZ1bmN0aW9uIGhhbmRsZUFkanVzdEJhbGFuY2Uocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXIgPSByb3c7CiAgICAgIHRoaXMuYmFsYW5jZUZvcm0gPSB7CiAgICAgICAgdXNlcklkOiByb3cuaWQsCiAgICAgICAgYW1vdW50OiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLmJhbGFuY2VPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5oiQ6ZW/5YC86LCD5pW0ICovaGFuZGxlQWRqdXN0R3Jvd3RoOiBmdW5jdGlvbiBoYW5kbGVBZGp1c3RHcm93dGgocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXIgPSByb3c7CiAgICAgIHRoaXMuZ3Jvd3RoRm9ybSA9IHsKICAgICAgICB1c2VySWQ6IHJvdy5pZCwKICAgICAgICBncm93dGg6IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMuZ3Jvd3RoT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOaLiem7keeUqOaItyAqL2hhbmRsZUJsYWNrbGlzdFVzZXI6IGZ1bmN0aW9uIGhhbmRsZUJsYWNrbGlzdFVzZXIocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09IDEgPyAi5ouJ6buRIiA6ICLop6PpmaTmi4npu5EiOwogICAgICB2YXIgbmV3U3RhdHVzID0gcm93LnN0YXR1cyA9PT0gMSA/IDIgOiAxOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93Lm5pY2tOYW1lICsgJyLnlKjmiLflkJfvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF91c2VyLmNoYW5nZVN0YXR1cykocm93LmlkLCBuZXdTdGF0dXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczYuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgICBfdGhpczYuZ2V0TGlzdCgpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgLy8g5pON5L2c5Y+W5raICiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTkvZnpop3osIPmlbQgKi9zdWJtaXRCYWxhbmNlRm9ybTogZnVuY3Rpb24gc3VibWl0QmFsYW5jZUZvcm0oKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJiYWxhbmNlRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgKDAsIF91c2VyLmFkanVzdEJhbGFuY2UpKF90aGlzNy5iYWxhbmNlRm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXM3LiRtb2RhbC5tc2dTdWNjZXNzKCLkvZnpop3osIPmlbTmiJDlip8iKTsKICAgICAgICAgICAgX3RoaXM3LmJhbGFuY2VPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzNy5nZXRMaXN0KCk7CiAgICAgICAgICAgIC8vIOWPkemAgeS9memineiwg+aVtOaIkOWKn+S6i+S7tgogICAgICAgICAgICBfdGhpczcuJGV2ZW50QnVzLiRlbWl0KCd1c2VyQmFsYW5jZUFkanVzdGVkJywgX3RoaXM3LmJhbGFuY2VGb3JtLnVzZXJJZCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojkvZnpop3osIPmlbQgKi9jYW5jZWxCYWxhbmNlOiBmdW5jdGlvbiBjYW5jZWxCYWxhbmNlKCkgewogICAgICB0aGlzLmJhbGFuY2VPcGVuID0gZmFsc2U7CiAgICAgIHRoaXMuYmFsYW5jZUZvcm0gPSB7fTsKICAgICAgdGhpcy5jdXJyZW50VXNlciA9IHt9OwogICAgfSwKICAgIC8qKiDmj5DkuqTmiJDplb/lgLzosIPmlbQgKi9zdWJtaXRHcm93dGhGb3JtOiBmdW5jdGlvbiBzdWJtaXRHcm93dGhGb3JtKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZ3Jvd3RoRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgKDAsIF91c2VyLmFkanVzdEdyb3d0aCkoX3RoaXM4Lmdyb3d0aEZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzOC4kbW9kYWwubXNnU3VjY2Vzcygi5oiQ6ZW/5YC86LCD5pW05oiQ5YqfIik7CiAgICAgICAgICAgIF90aGlzOC5ncm93dGhPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzOC5nZXRMaXN0KCk7CiAgICAgICAgICAgIC8vIOWPkemAgeaIkOmVv+WAvOiwg+aVtOaIkOWKn+S6i+S7tgogICAgICAgICAgICBfdGhpczguJGV2ZW50QnVzLiRlbWl0KCd1c2VyR3Jvd3RoQWRqdXN0ZWQnLCBfdGhpczguZ3Jvd3RoRm9ybS51c2VySWQpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5oiQ6ZW/5YC86LCD5pW0ICovY2FuY2VsR3Jvd3RoOiBmdW5jdGlvbiBjYW5jZWxHcm93dGgoKSB7CiAgICAgIHRoaXMuZ3Jvd3RoT3BlbiA9IGZhbHNlOwogICAgICB0aGlzLmdyb3d0aEZvcm0gPSB7fTsKICAgICAgdGhpcy5jdXJyZW50VXNlciA9IHt9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_user", "require", "name", "dicts", "data", "_defineProperty2", "default", "loading", "ids", "single", "multiple", "showSearch", "total", "userList", "title", "open", "balanceOpen", "queryParams", "pageNum", "pageSize", "nick<PERSON><PERSON>", "phone", "status", "city", "gender", "form", "currentUser", "balanceForm", "required", "message", "trigger", "pattern", "amount", "remark", "growth", "created", "getList", "methods", "_this", "listUser", "then", "response", "rows", "cancel", "reset", "id", "uniacid", "openid", "avatarUrl", "createTime", "capId", "country", "language", "province", "balance", "<PERSON><PERSON><PERSON>", "pid", "cash", "unionid", "appOpenid", "webOpenid", "wechatOpenid", "lastLoginType", "newCash", "lock", "isFx", "iosOpenid", "pushId", "alipayNumber", "alipayName", "ip", "memberCalculateTime", "userStatus", "isQr", "sourceType", "area", "adminId", "isPopImg", "memberDiscountTime", "memberDiscountId", "partnerMoney", "totalPartnerMoney", "delUserId", "alipayIdCode", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleView", "row", "$router", "push", "path", "handleUpdate", "_this2", "getUser", "submitForm", "_this3", "$refs", "validate", "valid", "updateUser", "$modal", "msgSuccess", "addUser", "handleDelete", "_this4", "confirm", "<PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "handleStatusChange", "_this5", "text", "changeStatus", "handleDropdownCommand", "command", "handleAdjustBalance", "handleAdjustGrowth", "handleBlacklistUser", "userId", "growthForm", "growthOpen", "_this6", "newStatus", "submitBalanceForm", "_this7", "adjustBalance", "$eventBus", "$emit", "cancelBalance", "submitGrowthForm", "_this8", "adjustGrowth", "cancel<PERSON>rowth"], "sources": ["src/views/massage/user/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"用户昵称\" prop=\"nickName\">\n        <el-input\n          v-model=\"queryParams.nickName\"\n          placeholder=\"请输入用户昵称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号\" prop=\"phone\">\n        <el-input\n          v-model=\"queryParams.phone\"\n          placeholder=\"请输入手机号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"用户状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择用户状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"城市\" prop=\"city\">\n        <el-input\n          v-model=\"queryParams.city\"\n          placeholder=\"请输入城市\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-select v-model=\"queryParams.gender\" placeholder=\"请选择性别\" clearable>\n          <el-option label=\"未知\" value=\"0\" />\n          <el-option label=\"男\" value=\"1\" />\n          <el-option label=\"女\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['massage:user:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['massage:user:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['massage:user:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['massage:user:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"用户头像\" align=\"center\" prop=\"avatarUrl\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.avatarUrl\" :width=\"50\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"用户昵称\" align=\"center\" prop=\"nickName\" width=\"120\" />\n      <el-table-column label=\"手机号\" align=\"center\" prop=\"phone\" width=\"120\" />\n      <el-table-column label=\"会员等级\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.status === 1 ? 'success' : 'danger'\">\n            {{ scope.row.status === 1 ? '普通会员' : '已拉黑' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"用户余额\" align=\"center\" prop=\"balance\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #f56c6c;\">¥{{ scope.row.balance || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"现金余额\" align=\"center\" prop=\"cash\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #67c23a;\">¥{{ scope.row.cash || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"成长值\" align=\"center\" prop=\"growth\" width=\"100\" />\n      <el-table-column label=\"消费总金额\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #409eff;\">¥{{ scope.row.totalConsumption || 0 }}</span>\n          <el-tooltip content=\"包含支付下单但尚未消费的金额\" placement=\"top\">\n            <i class=\"el-icon-question\" style=\"margin-left: 5px; color: #909399;\"></i>\n          </el-tooltip>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所在城市\" align=\"center\" prop=\"city\" width=\"120\" />\n      <el-table-column label=\"注册时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime * 1000) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"280\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:user:query']\"\n          >查看</el-button>\n          <el-dropdown @command=\"(command) => handleDropdownCommand(command, scope.row)\" trigger=\"click\">\n            <el-button size=\"mini\" type=\"text\">\n              修改/充值<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"balance\">修改余额</el-dropdown-item>\n              <el-dropdown-item command=\"growth\">修改成长</el-dropdown-item>\n              <el-dropdown-item command=\"blacklist\" divided>拉黑用户</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改按摩用户对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"用户昵称\" prop=\"nickName\">\n          <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" />\n        </el-form-item>\n        <el-form-item label=\"性别\" prop=\"gender\">\n          <el-select v-model=\"form.gender\" placeholder=\"请选择性别\">\n            <el-option\n              v-for=\"dict in dict.type.sys_user_sex\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"parseInt(dict.value)\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"城市\" prop=\"city\">\n          <el-input v-model=\"form.city\" placeholder=\"请输入城市\" />\n        </el-form-item>\n        <el-form-item label=\"省份\" prop=\"province\">\n          <el-input v-model=\"form.province\" placeholder=\"请输入省份\" />\n        </el-form-item>\n        <el-form-item label=\"国家\" prop=\"country\">\n          <el-input v-model=\"form.country\" placeholder=\"请输入国家\" />\n        </el-form-item>\n        <el-form-item label=\"语言\" prop=\"language\">\n          <el-input v-model=\"form.language\" placeholder=\"请输入语言\" />\n        </el-form-item>\n        <el-form-item label=\"地区\" prop=\"area\">\n          <el-input v-model=\"form.area\" placeholder=\"请输入地区\" />\n        </el-form-item>\n        <el-form-item label=\"IP地址\" prop=\"ip\">\n          <el-input v-model=\"form.ip\" placeholder=\"请输入IP地址\" />\n        </el-form-item>\n        <el-form-item label=\"应用ID\" prop=\"uniacid\">\n          <el-input v-model=\"form.uniacid\" placeholder=\"请输入应用ID\" />\n        </el-form-item>\n        <el-form-item label=\"来源类型\" prop=\"sourceType\">\n          <el-input v-model=\"form.sourceType\" placeholder=\"请输入来源类型\" />\n        </el-form-item>\n        <el-form-item label=\"支付宝账号\" prop=\"alipayNumber\">\n          <el-input v-model=\"form.alipayNumber\" placeholder=\"请输入支付宝账号\" />\n        </el-form-item>\n        <el-form-item label=\"支付宝姓名\" prop=\"alipayName\">\n          <el-input v-model=\"form.alipayName\" placeholder=\"请输入支付宝姓名\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n\n    <!-- 余额调整对话框 -->\n    <el-dialog title=\"余额调整\" :visible.sync=\"balanceOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"balanceForm\" :model=\"balanceForm\" :rules=\"balanceRules\" label-width=\"80px\">\n        <el-form-item label=\"当前余额\">\n          <span style=\"color: #f56c6c; font-weight: bold;\">¥{{ currentUser.balance || 0 }}</span>\n        </el-form-item>\n        <el-form-item label=\"调整金额\" prop=\"amount\">\n          <el-input v-model=\"balanceForm.amount\" placeholder=\"请输入调整金额（正数为增加，负数为减少）\" />\n        </el-form-item>\n        <el-form-item label=\"调整备注\" prop=\"remark\">\n          <el-input v-model=\"balanceForm.remark\" type=\"textarea\" placeholder=\"请输入调整备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitBalanceForm\">确 定</el-button>\n        <el-button @click=\"cancelBalance\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 成长值调整对话框 -->\n    <el-dialog title=\"成长值调整\" :visible.sync=\"growthOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"growthForm\" :model=\"growthForm\" :rules=\"growthRules\" label-width=\"80px\">\n        <el-form-item label=\"当前成长值\">\n          <span style=\"color: #67c23a; font-weight: bold;\">{{ currentUser.growth || 0 }}</span>\n        </el-form-item>\n        <el-form-item label=\"调整成长值\" prop=\"growth\">\n          <el-input v-model=\"growthForm.growth\" placeholder=\"请输入调整成长值（正数为增加，负数为减少）\" />\n        </el-form-item>\n        <el-form-item label=\"调整备注\" prop=\"remark\">\n          <el-input v-model=\"growthForm.remark\" type=\"textarea\" placeholder=\"请输入调整备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitGrowthForm\">确 定</el-button>\n        <el-button @click=\"cancelGrowth\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listUser, getUser, delUser, addUser, updateUser, adjustBalance, adjustGrowth, changeStatus } from \"@/api/massage/user\";\n\nexport default {\n  name: \"User\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 按摩用户表格数据\n      userList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 余额调整弹出层\n      balanceOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        nickName: null,\n        phone: null,\n        status: null,\n        city: null,\n        gender: null\n      },\n      // 表单参数\n      form: {},\n      // 当前操作的用户\n      currentUser: {},\n      // 余额调整表单\n      balanceForm: {},\n      balanceOpen: false,\n      // 成长值调整表单\n      growthForm: {},\n      growthOpen: false,\n      // 表单校验\n      rules: {\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      },\n      // 余额调整校验\n      balanceRules: {\n        amount: [\n          { required: true, message: \"调整金额不能为空\", trigger: \"blur\" }\n        ],\n        remark: [\n          { required: true, message: \"调整备注不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 成长值调整校验\n      growthRules: {\n        growth: [\n          { required: true, message: \"调整成长值不能为空\", trigger: \"blur\" }\n        ],\n        remark: [\n          { required: true, message: \"调整备注不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询按摩用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.queryParams).then(response => {\n        this.userList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        uniacid: null,\n        openid: null,\n        nickName: null,\n        avatarUrl: null,\n        createTime: null,\n        status: 1,\n        capId: null,\n        city: null,\n        country: null,\n        gender: null,\n        language: null,\n        province: null,\n        balance: 0,\n        phone: null,\n        sessionKey: null,\n        pid: null,\n        cash: 0,\n        unionid: null,\n        appOpenid: null,\n        webOpenid: null,\n        wechatOpenid: null,\n        lastLoginType: null,\n        newCash: null,\n        lock: null,\n        isFx: null,\n        iosOpenid: null,\n        pushId: null,\n        alipayNumber: null,\n        alipayName: null,\n        ip: null,\n        growth: 0,\n        memberCalculateTime: null,\n        userStatus: null,\n        isQr: null,\n        sourceType: null,\n        area: null,\n        adminId: null,\n        isPopImg: null,\n        memberDiscountTime: null,\n        memberDiscountId: null,\n        partnerMoney: null,\n        totalPartnerMoney: null,\n        delUserId: null,\n        alipayIdCode: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加按摩用户\";\n    },\n    /** 查看详情操作 */\n    handleView(row) {\n      this.$router.push({\n        path: '/massage/user/detail/' + row.id\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getUser(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改按摩用户\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除按摩用户编号为\"' + ids + '\"的数据项？').then(function() {\n        return delUser(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('massage/user/export', {\n        ...this.queryParams\n      }, `user_${new Date().getTime()}.xlsx`)\n    },\n    /** 用户状态修改 */\n    handleStatusChange(row) {\n      let text = row.status === 1 ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.nickName + '\"用户吗？').then(function() {\n        return changeStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === 1 ? 2 : 1;\n      });\n    },\n    /** 下拉菜单命令处理 */\n    handleDropdownCommand(command, row) {\n      this.currentUser = row;\n      switch (command) {\n        case 'balance':\n          this.handleAdjustBalance(row);\n          break;\n        case 'growth':\n          this.handleAdjustGrowth(row);\n          break;\n        case 'blacklist':\n          this.handleBlacklistUser(row);\n          break;\n      }\n    },\n    /** 余额调整 */\n    handleAdjustBalance(row) {\n      this.currentUser = row;\n      this.balanceForm = {\n        userId: row.id,\n        amount: null,\n        remark: null\n      };\n      this.balanceOpen = true;\n    },\n    /** 成长值调整 */\n    handleAdjustGrowth(row) {\n      this.currentUser = row;\n      this.growthForm = {\n        userId: row.id,\n        growth: null,\n        remark: null\n      };\n      this.growthOpen = true;\n    },\n    /** 拉黑用户 */\n    handleBlacklistUser(row) {\n      const text = row.status === 1 ? \"拉黑\" : \"解除拉黑\";\n      const newStatus = row.status === 1 ? 2 : 1;\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.nickName + '\"用户吗？').then(function() {\n        return changeStatus(row.id, newStatus);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n        this.getList();\n      }).catch(function() {\n        // 操作取消\n      });\n    },\n    /** 提交余额调整 */\n    submitBalanceForm() {\n      this.$refs[\"balanceForm\"].validate(valid => {\n        if (valid) {\n          adjustBalance(this.balanceForm).then(response => {\n            this.$modal.msgSuccess(\"余额调整成功\");\n            this.balanceOpen = false;\n            this.getList();\n            // 发送余额调整成功事件\n            this.$eventBus.$emit('userBalanceAdjusted', this.balanceForm.userId);\n          });\n        }\n      });\n    },\n    /** 取消余额调整 */\n    cancelBalance() {\n      this.balanceOpen = false;\n      this.balanceForm = {};\n      this.currentUser = {};\n    },\n    /** 提交成长值调整 */\n    submitGrowthForm() {\n      this.$refs[\"growthForm\"].validate(valid => {\n        if (valid) {\n          adjustGrowth(this.growthForm).then(response => {\n            this.$modal.msgSuccess(\"成长值调整成功\");\n            this.growthOpen = false;\n            this.getList();\n            // 发送成长值调整成功事件\n            this.$eventBus.$emit('userGrowthAdjusted', this.growthForm.userId);\n          });\n        }\n      });\n    },\n    /** 取消成长值调整 */\n    cancelGrowth() {\n      this.growthOpen = false;\n      this.growthForm = {};\n      this.currentUser = {};\n    }\n  }\n};\n</script>\n\n<style scoped>\n.user-detail {\n  padding: 20px;\n}\n\n.user-avatar {\n  text-align: center;\n  padding: 20px;\n}\n\n.user-avatar img {\n  border: 3px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar h3 {\n  margin-top: 15px;\n  color: #333;\n  font-weight: 500;\n}\n\n.user-info {\n  padding: 20px 0;\n}\n\n.el-descriptions {\n  margin-top: 20px;\n}\n\n.el-descriptions-item__label {\n  font-weight: 600;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AA4QA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;IAAA,kBACA,sBAEA,mBACA,iBAEA;MACAP,QAAA,GACA;QAAAQ,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA,EACA;MACAT,KAAA,GACA;QAAAO,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA,GACA;QAAAC,OAAA;QAAAF,OAAA;QAAAC,OAAA;MAAA;IAEA,oBAEA;MACAE,MAAA,GACA;QAAAJ,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA,EACA;MACAG,MAAA,GACA;QAAAL,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;IAEA,mBAEA;MACAI,MAAA,GACA;QAAAN,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA,EACA;MACAG,MAAA,GACA;QAAAL,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;IAEA;EAEA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA/B,OAAA;MACA,IAAAgC,cAAA,OAAAtB,WAAA,EAAAuB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAzB,QAAA,GAAA4B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA1B,KAAA,GAAA6B,QAAA,CAAA7B,KAAA;QACA0B,KAAA,CAAA/B,OAAA;MACA;IACA;IACA;IACAoC,MAAA,WAAAA,OAAA;MACA,KAAA5B,IAAA;MACA,KAAA6B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnB,IAAA;QACAoB,EAAA;QACAC,OAAA;QACAC,MAAA;QACA3B,QAAA;QACA4B,SAAA;QACAC,UAAA;QACA3B,MAAA;QACA4B,KAAA;QACA3B,IAAA;QACA4B,OAAA;QACA3B,MAAA;QACA4B,QAAA;QACAC,QAAA;QACAC,OAAA;QACAjC,KAAA;QACAkC,UAAA;QACAC,GAAA;QACAC,IAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,YAAA;QACAC,aAAA;QACAC,OAAA;QACAC,IAAA;QACAC,IAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,EAAA;QACApC,MAAA;QACAqC,mBAAA;QACAC,UAAA;QACAC,IAAA;QACAC,UAAA;QACAC,IAAA;QACAC,OAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApE,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAkD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhF,GAAA,GAAAgF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA7C,EAAA;MAAA;MACA,KAAApC,MAAA,GAAA+E,SAAA,CAAAG,MAAA;MACA,KAAAjF,QAAA,IAAA8E,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAhD,KAAA;MACA,KAAA7B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA+E,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA,4BAAAH,GAAA,CAAAjD;MACA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAvD,KAAA;MACA,IAAAC,EAAA,GAAAiD,GAAA,CAAAjD,EAAA,SAAArC,GAAA;MACA,IAAA4F,aAAA,EAAAvD,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACA0D,MAAA,CAAA1E,IAAA,GAAAgB,QAAA,CAAArC,IAAA;QACA+F,MAAA,CAAApF,IAAA;QACAoF,MAAA,CAAArF,KAAA;MACA;IACA;IACA,WACAuF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA7E,IAAA,CAAAoB,EAAA;YACA,IAAA6D,gBAAA,EAAAJ,MAAA,CAAA7E,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACA6D,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAlE,OAAA;YACA;UACA;YACA,IAAAyE,aAAA,EAAAP,MAAA,CAAA7E,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACA6D,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAlE,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA0E,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,IAAAvG,GAAA,GAAAsF,GAAA,CAAAjD,EAAA,SAAArC,GAAA;MACA,KAAAmG,MAAA,CAAAK,OAAA,oBAAAxG,GAAA,aAAAgC,IAAA;QACA,WAAAyE,aAAA,EAAAzG,GAAA;MACA,GAAAgC,IAAA;QACAuE,MAAA,CAAA3E,OAAA;QACA2E,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,4BAAAC,cAAA,CAAA/G,OAAA,MACA,KAAAW,WAAA,WAAAqG,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,aACAC,kBAAA,WAAAA,mBAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAAC,IAAA,GAAA7B,GAAA,CAAAxE,MAAA;MACA,KAAAqF,MAAA,CAAAK,OAAA,UAAAW,IAAA,UAAA7B,GAAA,CAAA1E,QAAA,YAAAoB,IAAA;QACA,WAAAoF,kBAAA,EAAA9B,GAAA,CAAAjD,EAAA,EAAAiD,GAAA,CAAAxE,MAAA;MACA,GAAAkB,IAAA;QACAkF,MAAA,CAAAf,MAAA,CAAAC,UAAA,CAAAe,IAAA;MACA,GAAAT,KAAA;QACApB,GAAA,CAAAxE,MAAA,GAAAwE,GAAA,CAAAxE,MAAA;MACA;IACA;IACA,eACAuG,qBAAA,WAAAA,sBAAAC,OAAA,EAAAhC,GAAA;MACA,KAAApE,WAAA,GAAAoE,GAAA;MACA,QAAAgC,OAAA;QACA;UACA,KAAAC,mBAAA,CAAAjC,GAAA;UACA;QACA;UACA,KAAAkC,kBAAA,CAAAlC,GAAA;UACA;QACA;UACA,KAAAmC,mBAAA,CAAAnC,GAAA;UACA;MACA;IACA;IACA,WACAiC,mBAAA,WAAAA,oBAAAjC,GAAA;MACA,KAAApE,WAAA,GAAAoE,GAAA;MACA,KAAAnE,WAAA;QACAuG,MAAA,EAAApC,GAAA,CAAAjD,EAAA;QACAb,MAAA;QACAC,MAAA;MACA;MACA,KAAAjB,WAAA;IACA;IACA,YACAgH,kBAAA,WAAAA,mBAAAlC,GAAA;MACA,KAAApE,WAAA,GAAAoE,GAAA;MACA,KAAAqC,UAAA;QACAD,MAAA,EAAApC,GAAA,CAAAjD,EAAA;QACAX,MAAA;QACAD,MAAA;MACA;MACA,KAAAmG,UAAA;IACA;IACA,WACAH,mBAAA,WAAAA,oBAAAnC,GAAA;MAAA,IAAAuC,MAAA;MACA,IAAAV,IAAA,GAAA7B,GAAA,CAAAxE,MAAA;MACA,IAAAgH,SAAA,GAAAxC,GAAA,CAAAxE,MAAA;MACA,KAAAqF,MAAA,CAAAK,OAAA,UAAAW,IAAA,UAAA7B,GAAA,CAAA1E,QAAA,YAAAoB,IAAA;QACA,WAAAoF,kBAAA,EAAA9B,GAAA,CAAAjD,EAAA,EAAAyF,SAAA;MACA,GAAA9F,IAAA;QACA6F,MAAA,CAAA1B,MAAA,CAAAC,UAAA,CAAAe,IAAA;QACAU,MAAA,CAAAjG,OAAA;MACA,GAAA8E,KAAA;QACA;MAAA,CACA;IACA;IACA,aACAqB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjC,KAAA,gBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAgC,mBAAA,EAAAD,MAAA,CAAA7G,WAAA,EAAAa,IAAA,WAAAC,QAAA;YACA+F,MAAA,CAAA7B,MAAA,CAAAC,UAAA;YACA4B,MAAA,CAAAxH,WAAA;YACAwH,MAAA,CAAApG,OAAA;YACA;YACAoG,MAAA,CAAAE,SAAA,CAAAC,KAAA,wBAAAH,MAAA,CAAA7G,WAAA,CAAAuG,MAAA;UACA;QACA;MACA;IACA;IACA,aACAU,aAAA,WAAAA,cAAA;MACA,KAAA5H,WAAA;MACA,KAAAW,WAAA;MACA,KAAAD,WAAA;IACA;IACA,cACAmH,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAsC,kBAAA,EAAAD,MAAA,CAAAX,UAAA,EAAA3F,IAAA,WAAAC,QAAA;YACAqG,MAAA,CAAAnC,MAAA,CAAAC,UAAA;YACAkC,MAAA,CAAAV,UAAA;YACAU,MAAA,CAAA1G,OAAA;YACA;YACA0G,MAAA,CAAAJ,SAAA,CAAAC,KAAA,uBAAAG,MAAA,CAAAX,UAAA,CAAAD,MAAA;UACA;QACA;MACA;IACA;IACA,cACAc,YAAA,WAAAA,aAAA;MACA,KAAAZ,UAAA;MACA,KAAAD,UAAA;MACA,KAAAzG,WAAA;IACA;EACA;AACA", "ignoreList": []}]}