package com.ruoyi.massage.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.massage.mapper.MassageOrderMapper;
import com.ruoyi.massage.mapper.MassageUserMapper;
import com.ruoyi.massage.mapper.MassageBalanceRecordMapper;
import com.ruoyi.massage.mapper.MassageRechargeOrderMapper;
import com.ruoyi.massage.domain.MassageOrder;
import com.ruoyi.massage.domain.MassageUser;
import com.ruoyi.massage.domain.MassageBalanceRecord;
import com.ruoyi.massage.domain.MassageRechargeOrder;
import com.ruoyi.massage.service.IMassageOrderService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 按摩订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class MassageOrderServiceImpl implements IMassageOrderService 
{
    @Autowired
    private MassageOrderMapper massageOrderMapper;

    @Autowired
    private MassageUserMapper massageUserMapper;

    @Autowired
    private MassageBalanceRecordMapper massageBalanceRecordMapper;

    @Autowired
    private MassageRechargeOrderMapper massageRechargeOrderMapper;

    /**
     * 查询按摩订单
     * 
     * @param id 按摩订单主键
     * @return 按摩订单
     */
    @Override
    public MassageOrder selectMassageOrderById(Long id)
    {
        return massageOrderMapper.selectMassageOrderById(id);
    }

    /**
     * 查询按摩订单列表
     * 
     * @param massageOrder 按摩订单
     * @return 按摩订单
     */
    @Override
    public List<MassageOrder> selectMassageOrderList(MassageOrder massageOrder)
    {
        return massageOrderMapper.selectMassageOrderList(massageOrder);
    }

    /**
     * 新增按摩订单
     * 
     * @param massageOrder 按摩订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMassageOrder(MassageOrder massageOrder)
    {
        massageOrder.setCreateTime(new Date());
        return massageOrderMapper.insertMassageOrder(massageOrder);
    }

    /**
     * 修改按摩订单
     * 
     * @param massageOrder 按摩订单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMassageOrder(MassageOrder massageOrder)
    {
        return massageOrderMapper.updateMassageOrder(massageOrder);
    }

    /**
     * 批量删除按摩订单
     * 
     * @param ids 需要删除的按摩订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMassageOrderByIds(Long[] ids)
    {
        return massageOrderMapper.deleteMassageOrderByIds(ids);
    }

    /**
     * 删除按摩订单信息
     * 
     * @param id 按摩订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMassageOrderById(Long id)
    {
        return massageOrderMapper.deleteMassageOrderById(id);
    }

    /**
     * 根据订单编号查询订单
     * 
     * @param orderCode 订单编号
     * @return 订单信息
     */
    @Override
    public MassageOrder selectMassageOrderByCode(String orderCode)
    {
        return massageOrderMapper.selectMassageOrderByCode(orderCode);
    }

    /**
     * 订单派单
     *
     * @param massageOrder 订单信息（包含技师ID）
     * @return 结果
     */
    @Override
    @Transactional
    public int assignOrder(MassageOrder massageOrder)
    {
        return massageOrderMapper.updateMassageOrder(massageOrder);
    }

    /**
     * 订单退款
     *
     * @param massageOrder 订单信息（包含退款金额和退款原因）
     * @return 结果
     */
    @Override
    @Transactional
    public int refundOrder(MassageOrder massageOrder)
    {
        return massageOrderMapper.updateMassageOrder(massageOrder);
    }

    /**
     * 完成订单
     *
     * @param massageOrder 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int completeOrder(MassageOrder massageOrder)
    {
        return massageOrderMapper.updateMassageOrder(massageOrder);
    }

    /**
     * 获取订单统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getOrderStatistics(String startDate, String endDate)
    {
        return massageOrderMapper.getOrderStatistics(startDate, endDate);
    }

    /**
     * 获取收入统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型
     * @return 收入统计
     */
    @Override
    public Map<String, Object> getIncomeStatistics(String startDate, String endDate, String type)
    {
        // TODO: 实现具体的收入统计逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("totalIncome", 0.0);
        result.put("orderCount", 0);
        return result;
    }

    /**
     * 获取待处理订单列表
     * 
     * @param massageOrder 查询条件
     * @return 待处理订单列表
     */
    @Override
    public List<MassageOrder> selectPendingOrders(MassageOrder massageOrder)
    {
        return massageOrderMapper.selectPendingOrders(massageOrder);
    }

    /**
     * 获取用户订单列表
     * 
     * @param userId 用户ID
     * @param status 订单状态
     * @return 订单列表
     */
    @Override
    public List<MassageOrder> selectOrdersByUserId(Long userId, Integer status)
    {
        return massageOrderMapper.selectOrdersByUserId(userId, status);
    }

    /**
     * 获取技师订单列表
     *
     * @param coachId 技师ID
     * @param status 订单状态
     * @return 订单列表
     */
    @Override
    public List<MassageOrder> selectOrdersByCoachId(Long coachId, Integer status)
    {
        return massageOrderMapper.selectOrdersByCoachId(coachId, status);
    }

    /**
     * 转派技师
     *
     * @param massageOrder 订单信息（包含新技师ID）
     * @return 结果
     */
    @Override
    @Transactional
    public int changeCoach(MassageOrder massageOrder)
    {
        return massageOrderMapper.updateMassageOrder(massageOrder);
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(Long orderId, String cancelReason)
    {
        MassageOrder order = new MassageOrder();
        order.setId(orderId);
        order.setStatus(5); // 已取消
        order.setRemark(cancelReason); // 使用备注字段存储取消原因
        return massageOrderMapper.updateMassageOrder(order);
    }

    /**
     * 订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param payType 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    @Override
    @Transactional
    public int paySuccess(String orderCode, Integer payType, String transactionId)
    {
        MassageOrder order = selectMassageOrderByCode(orderCode);
        if (order != null) {
            order.setStatus(2); // 已支付
            order.setPayType(payType);
            order.setPayTime(System.currentTimeMillis());
            // 交易流水号可以存储在备注中或者扩展字段
            order.setRemark("交易流水号: " + transactionId);

            // 如果使用了余额支付，需要扣减余额并创建记录
            if (order.getBalance() != null && order.getBalance() > 0) {
                createBalanceRecord(order, 2); // type=2 表示下单消费
            }

            return massageOrderMapper.updateMassageOrder(order);
        }
        return 0;
    }

    /**
     * 创建余额记录
     *
     * @param order 订单信息
     * @param type 记录类型
     */
    private void createBalanceRecord(MassageOrder order, Integer type) {
        // 获取用户信息
        MassageUser user = massageUserMapper.selectMassageUserById(order.getUserId());
        if (user != null) {
            Double currentBalance = user.getBalance() != null ? user.getBalance() : 0.0;
            Double balanceUsed = order.getBalance();
            Double newBalance = currentBalance - balanceUsed;

            // 创建余额记录
            MassageBalanceRecord balanceRecord = new MassageBalanceRecord();
            balanceRecord.setUniacid(user.getUniacid());
            balanceRecord.setUserId(order.getUserId());
            balanceRecord.setOrderId(order.getId());
            balanceRecord.setType(type);
            balanceRecord.setAdd(0); // 0表示减少
            balanceRecord.setPrice(BigDecimal.valueOf(balanceUsed));
            balanceRecord.setBeforeBalance(BigDecimal.valueOf(currentBalance));
            balanceRecord.setAfterBalance(BigDecimal.valueOf(newBalance));
            balanceRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

            // 插入余额记录
            massageBalanceRecordMapper.insertMassageBalanceRecord(balanceRecord);

            // 更新用户余额
            user.setBalance(newBalance);
            massageUserMapper.updateMassageUser(user);
        }
    }

    /**
     * 充值订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param transactionId 交易流水号
     * @return 结果
     */
    @Transactional
    public int rechargePaySuccess(String orderCode, String transactionId) {
        MassageRechargeOrder rechargeOrder = massageRechargeOrderMapper.selectMassageRechargeOrderByCode(orderCode);
        if (rechargeOrder != null && rechargeOrder.getStatus() == 1) {
            // 更新充值订单状态
            rechargeOrder.setStatus(2); // 已支付
            rechargeOrder.setTransactionId(transactionId);
            rechargeOrder.setPayTime(System.currentTimeMillis() / 1000);

            // 获取用户信息
            MassageUser user = massageUserMapper.selectMassageUserById(rechargeOrder.getUserId());
            if (user != null) {
                Double currentBalance = user.getBalance() != null ? user.getBalance() : 0.0;
                Double rechargeAmount = rechargeOrder.getTruePrice().doubleValue();
                Double newBalance = currentBalance + rechargeAmount;

                // 更新充值订单的当前余额
                rechargeOrder.setNowBalance(BigDecimal.valueOf(newBalance));

                // 创建余额记录 - type=1 表示用户充值
                createRechargeBalanceRecord(rechargeOrder, user, currentBalance, newBalance);

                // 更新用户余额
                user.setBalance(newBalance);
                massageUserMapper.updateMassageUser(user);

                return massageRechargeOrderMapper.updateMassageRechargeOrder(rechargeOrder);
            }
        }
        return 0;
    }

    /**
     * 创建充值余额记录
     *
     * @param rechargeOrder 充值订单
     * @param user 用户信息
     * @param currentBalance 当前余额
     * @param newBalance 新余额
     */
    private void createRechargeBalanceRecord(MassageRechargeOrder rechargeOrder, MassageUser user,
                                           Double currentBalance, Double newBalance) {
        MassageBalanceRecord balanceRecord = new MassageBalanceRecord();
        balanceRecord.setUniacid(user.getUniacid());
        balanceRecord.setUserId(rechargeOrder.getUserId());
        balanceRecord.setOrderId(rechargeOrder.getId());
        balanceRecord.setType(1); // 1表示用户充值
        balanceRecord.setAdd(1); // 1表示增加
        balanceRecord.setPrice(rechargeOrder.getTruePrice());
        balanceRecord.setBeforeBalance(BigDecimal.valueOf(currentBalance));
        balanceRecord.setAfterBalance(BigDecimal.valueOf(newBalance));
        balanceRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

        massageBalanceRecordMapper.insertMassageBalanceRecord(balanceRecord);
    }

    /**
     * 订单退款处理
     *
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @return 结果
     */
    @Transactional
    public int processOrderRefund(Long orderId, Double refundAmount) {
        MassageOrder order = massageOrderMapper.selectMassageOrderById(orderId);
        if (order != null) {
            // 获取用户信息
            MassageUser user = massageUserMapper.selectMassageUserById(order.getUserId());
            if (user != null) {
                Double currentBalance = user.getBalance() != null ? user.getBalance() : 0.0;
                Double newBalance = currentBalance + refundAmount;

                // 创建余额记录 - type=3 表示订单退款
                MassageBalanceRecord balanceRecord = new MassageBalanceRecord();
                balanceRecord.setUniacid(user.getUniacid());
                balanceRecord.setUserId(order.getUserId());
                balanceRecord.setOrderId(order.getId());
                balanceRecord.setType(3); // 3表示订单退款
                balanceRecord.setAdd(1); // 1表示增加
                balanceRecord.setPrice(BigDecimal.valueOf(refundAmount));
                balanceRecord.setBeforeBalance(BigDecimal.valueOf(currentBalance));
                balanceRecord.setAfterBalance(BigDecimal.valueOf(newBalance));
                balanceRecord.setCreateTimestamp(System.currentTimeMillis() / 1000);

                // 插入余额记录
                massageBalanceRecordMapper.insertMassageBalanceRecord(balanceRecord);

                // 更新用户余额
                user.setBalance(newBalance);
                massageUserMapper.updateMassageUser(user);

                // 更新订单状态为已退款
                order.setStatus(6); // 6表示已退款
                return massageOrderMapper.updateMassageOrder(order);
            }
        }
        return 0;
    }

    /**
     * 升级订单支付成功处理
     *
     * @param orderCode 订单编号
     * @param payType 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    @Transactional
    public int upgradePaySuccess(String orderCode, Integer payType, String transactionId) {
        // 这里假设升级订单也使用普通订单表，但有特殊标识
        MassageOrder order = selectMassageOrderByCode(orderCode);
        if (order != null) {
            order.setStatus(2); // 已支付
            order.setPayType(payType);
            order.setPayTime(System.currentTimeMillis());
            order.setRemark("升级订单交易流水号: " + transactionId);

            // 如果使用了余额支付，需要扣减余额并创建记录
            if (order.getBalance() != null && order.getBalance() > 0) {
                createBalanceRecord(order, 4); // type=4 表示升级消费
            }

            return massageOrderMapper.updateMassageOrder(order);
        }
        return 0;
    }
}
