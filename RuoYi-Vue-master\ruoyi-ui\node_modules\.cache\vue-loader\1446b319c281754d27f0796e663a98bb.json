{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue?vue&type=template&id=6cee415a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\RechargeRecordTab.vue", "mtime": 1753754352055}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753582865879}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}