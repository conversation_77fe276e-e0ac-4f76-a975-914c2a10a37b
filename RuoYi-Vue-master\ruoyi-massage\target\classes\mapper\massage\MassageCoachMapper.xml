<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.massage.mapper.MassageCoachMapper">
    
    <resultMap type="MassageCoach" id="MassageCoachResult">
        <result property="id"    column="id"    />
        <result property="uniacid"    column="uniacid"    />
        <result property="coach_name"    column="coach_name"    />
        <result property="user_id"    column="user_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="id_card"    column="id_card"    />
        <result property="sex"    column="sex"    />
        <result property="work_time"    column="work_time"    />
        <result property="city"    column="city"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="address"    column="address"    />
        <result property="text"    column="text"    />
        <result property="license"    column="license"    />
        <result property="work_img"    column="work_img"    />
        <result property="status"    column="status"    />
        <result property="auth_status"    column="auth_status"    />
        <result property="sh_time"    column="sh_time"    />
        <result property="sh_text"    column="sh_text"    />
        <result property="create_time"    column="create_time"    />
        <result property="self_img"    column="self_img"    />
        <result property="is_work"    column="is_work"    />
        <result property="start_time"    column="start_time"    />
        <result property="end_time"    column="end_time"    />
        <result property="service_price"    column="service_price"    />
        <result property="car_price"    column="car_price"    />
        <result property="id_code"    column="id_code"    />
        <result property="star"    column="star"    />
        <result property="admin_add"    column="admin_add"    />
        <result property="city_id"    column="city_id"    />
        <result property="video"    column="video"    />
        <result property="is_update"    column="is_update"    />
        <result property="integral"    column="integral"    />
        <result property="order_num"    column="order_num"    />
        <result property="recommend"    column="recommend"    />
        <result property="balance_cash"    column="balance_cash"    />
        <result property="index_top"    column="index_top"    />
        <result property="coach_position"    column="coach_position"    />
        <result property="near_time"    column="near_time"    />
        <result property="check_cash"    column="check_cash"    />
        <result property="agent_type"    column="agent_type"    />
        <result property="partner_id"    column="partner_id"    />
        <result property="partner_time"    column="partner_time"    />
        <result property="total_order_num"    column="total_order_num"    />
        <result property="birthday"    column="birthday"    />
        <result property="credit_value"    column="credit_value"    />
        <result property="credit_top"    column="credit_top"    />
        <result property="recommend_icon"    column="recommend_icon"    />
        <result property="free_fare_distance"    column="free_fare_distance"    />
        <result property="show_salenum"    column="show_salenum"    />
        <result property="coach_icon"    column="coach_icon"    />
        <result property="true_lng"    column="true_lng"    />
        <result property="true_lat"    column="true_lat"    />
        <result property="type_id"    column="type_id"    />
        <result property="version"    column="version"    />
        <result property="true_user_name"    column="true_user_name"    />
        <result property="industry_type"    column="industry_type"    />
        <result property="height"    column="height"    />
        <result property="weight"    column="weight"    />
        <result property="constellation"    column="constellation"    />
        <result property="free_fare_bear"    column="free_fare_bear"    />
        <result property="personality_icon"    column="personality_icon"    />
        <result property="station_icon"    column="station_icon"    />
        <result property="address_update_time"    column="address_update_time"    />
        <result property="pv"    column="pv"    />
        <result property="virtual_collect"    column="virtual_collect"    />
        <result property="virtual_comment"    column="virtual_comment"    />
        <result property="work_type"    column="work_type"    />
        <result property="data"    column="data"    />
    </resultMap>

    <sql id="selectMassageCoachVo">
        select id, uniacid, coach_name, user_id, mobile, id_card, sex, work_time, city, lng, lat, address, text, license, work_img, status, auth_status, sh_time, sh_text, create_time, self_img, is_work, start_time, end_time, service_price, car_price, id_code, star, admin_add, city_id, video, is_update, integral, order_num, recommend, balance_cash, index_top, coach_position, near_time, check_cash, agent_type, partner_id, partner_time, total_order_num, birthday, credit_value, credit_top, recommend_icon, free_fare_distance, show_salenum, coach_icon, true_lng, true_lat, type_id, version, true_user_name, industry_type, height, weight, constellation, free_fare_bear, personality_icon, station_icon, address_update_time, pv, virtual_collect, virtual_comment, work_type, data from ims_massage_service_coach_list
    </sql>

    <select id="selectMassageCoachList" parameterType="MassageCoach" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        <where>  
            <if test="coach_name != null  and coach_name != ''"> and coach_name like concat('%', #{coach_name}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="auth_status != null "> and auth_status = #{auth_status}</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="city != null  and city != ''"> and city like concat('%', #{city}, '%')</if>
            <if test="is_work != null "> and is_work = #{is_work}</if>
            <if test="recommend != null "> and recommend = #{recommend}</if>
            <if test="city_id != null "> and city_id = #{city_id}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMassageCoachById" parameterType="Long" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMassageCoach" parameterType="MassageCoach" useGeneratedKeys="true" keyProperty="id">
        insert into ims_massage_service_coach_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">uniacid,</if>
            <if test="coach_name != null and coach_name != ''">coach_name,</if>
            <if test="user_id != null">user_id,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="id_card != null">id_card,</if>
            <if test="sex != null">sex,</if>
            <if test="work_time != null">work_time,</if>
            <if test="city != null">city,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="address != null">address,</if>
            <if test="text != null">text,</if>
            <if test="license != null">license,</if>
            <if test="work_img != null">work_img,</if>
            <if test="status != null">status,</if>
            <if test="auth_status != null">auth_status,</if>
            <if test="sh_time != null">sh_time,</if>
            <if test="sh_text != null">sh_text,</if>
            <if test="create_time != null">create_time,</if>
            <if test="self_img != null">self_img,</if>
            <if test="is_work != null">is_work,</if>
            <if test="start_time != null">start_time,</if>
            <if test="end_time != null">end_time,</if>
            <if test="service_price != null">service_price,</if>
            <if test="car_price != null">car_price,</if>
            <if test="id_code != null">id_code,</if>
            <if test="star != null">star,</if>
            <if test="admin_add != null">admin_add,</if>
            <if test="city_id != null">city_id,</if>
            <if test="video != null">video,</if>
            <if test="is_update != null">is_update,</if>
            <if test="integral != null">integral,</if>
            <if test="order_num != null">order_num,</if>
            <if test="recommend != null">recommend,</if>
            <if test="balance_cash != null">balance_cash,</if>
            <if test="index_top != null">index_top,</if>
            <if test="coach_position != null">coach_position,</if>
            <if test="near_time != null">near_time,</if>
            <if test="check_cash != null">check_cash,</if>
            <if test="agent_type != null">agent_type,</if>
            <if test="partner_id != null">partner_id,</if>
            <if test="partner_time != null">partner_time,</if>
            <if test="total_order_num != null">total_order_num,</if>
            <if test="birthday != null">birthday,</if>
            <if test="credit_value != null">credit_value,</if>
            <if test="credit_top != null">credit_top,</if>
            <if test="recommend_icon != null">recommend_icon,</if>
            <if test="free_fare_distance != null">free_fare_distance,</if>
            <if test="show_salenum != null">show_salenum,</if>
            <if test="coach_icon != null">coach_icon,</if>
            <if test="true_lng != null">true_lng,</if>
            <if test="true_lat != null">true_lat,</if>
            <if test="type_id != null">type_id,</if>
            <if test="version != null">version,</if>
            <if test="true_user_name != null">true_user_name,</if>
            <if test="industry_type != null">industry_type,</if>
            <if test="height != null">height,</if>
            <if test="weight != null">weight,</if>
            <if test="constellation != null">constellation,</if>
            <if test="free_fare_bear != null">free_fare_bear,</if>
            <if test="personality_icon != null">personality_icon,</if>
            <if test="station_icon != null">station_icon,</if>
            <if test="address_update_time != null">address_update_time,</if>
            <if test="pv != null">pv,</if>
            <if test="virtual_collect != null">virtual_collect,</if>
            <if test="virtual_comment != null">virtual_comment,</if>
            <if test="work_type != null">work_type,</if>
            <if test="data != null">data,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniacid != null">#{uniacid},</if>
            <if test="coach_name != null and coach_name != ''">#{coach_name},</if>
            <if test="user_id != null">#{user_id},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="id_card != null">#{id_card},</if>
            <if test="sex != null">#{sex},</if>
            <if test="work_time != null">#{work_time},</if>
            <if test="city != null">#{city},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="address != null">#{address},</if>
            <if test="text != null">#{text},</if>
            <if test="license != null">#{license},</if>
            <if test="work_img != null">#{work_img},</if>
            <if test="status != null">#{status},</if>
            <if test="auth_status != null">#{auth_status},</if>
            <if test="sh_time != null">#{sh_time},</if>
            <if test="sh_text != null">#{sh_text},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="self_img != null">#{self_img},</if>
            <if test="is_work != null">#{is_work},</if>
            <if test="start_time != null">#{start_time},</if>
            <if test="end_time != null">#{end_time},</if>
            <if test="service_price != null">#{service_price},</if>
            <if test="car_price != null">#{car_price},</if>
            <if test="id_code != null">#{id_code},</if>
            <if test="star != null">#{star},</if>
            <if test="admin_add != null">#{admin_add},</if>
            <if test="city_id != null">#{city_id},</if>
            <if test="video != null">#{video},</if>
            <if test="is_update != null">#{is_update},</if>
            <if test="integral != null">#{integral},</if>
            <if test="order_num != null">#{order_num},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="balance_cash != null">#{balance_cash},</if>
            <if test="index_top != null">#{index_top},</if>
            <if test="coach_position != null">#{coach_position},</if>
            <if test="near_time != null">#{near_time},</if>
            <if test="check_cash != null">#{check_cash},</if>
            <if test="agent_type != null">#{agent_type},</if>
            <if test="partner_id != null">#{partner_id},</if>
            <if test="partner_time != null">#{partner_time},</if>
            <if test="total_order_num != null">#{total_order_num},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="credit_value != null">#{credit_value},</if>
            <if test="credit_top != null">#{credit_top},</if>
            <if test="recommend_icon != null">#{recommend_icon},</if>
            <if test="free_fare_distance != null">#{free_fare_distance},</if>
            <if test="show_salenum != null">#{show_salenum},</if>
            <if test="coach_icon != null">#{coach_icon},</if>
            <if test="true_lng != null">#{true_lng},</if>
            <if test="true_lat != null">#{true_lat},</if>
            <if test="type_id != null">#{type_id},</if>
            <if test="version != null">#{version},</if>
            <if test="true_user_name != null">#{true_user_name},</if>
            <if test="industry_type != null">#{industry_type},</if>
            <if test="height != null">#{height},</if>
            <if test="weight != null">#{weight},</if>
            <if test="constellation != null">#{constellation},</if>
            <if test="free_fare_bear != null">#{free_fare_bear},</if>
            <if test="personality_icon != null">#{personality_icon},</if>
            <if test="station_icon != null">#{station_icon},</if>
            <if test="address_update_time != null">#{address_update_time},</if>
            <if test="pv != null">#{pv},</if>
            <if test="virtual_collect != null">#{virtual_collect},</if>
            <if test="virtual_comment != null">#{virtual_comment},</if>
            <if test="work_type != null">#{work_type},</if>
            <if test="data != null">#{data},</if>
         </trim>
    </insert>

    <update id="updateMassageCoach" parameterType="MassageCoach">
        update ims_massage_service_coach_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniacid != null">uniacid = #{uniacid},</if>
            <if test="coach_name != null and coach_name != ''">coach_name = #{coach_name},</if>
            <if test="user_id != null">user_id = #{user_id},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="id_card != null">id_card = #{id_card},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="work_time != null">work_time = #{work_time},</if>
            <if test="city != null">city = #{city},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="address != null">address = #{address},</if>
            <if test="text != null">text = #{text},</if>
            <if test="license != null">license = #{license},</if>
            <if test="work_img != null">work_img = #{work_img},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auth_status != null">auth_status = #{auth_status},</if>
            <if test="sh_time != null">sh_time = #{sh_time},</if>
            <if test="sh_text != null">sh_text = #{sh_text},</if>
            <if test="create_time != null">create_time = #{create_time},</if>
            <if test="self_img != null">self_img = #{self_img},</if>
            <if test="is_work != null">is_work = #{is_work},</if>
            <if test="start_time != null">start_time = #{start_time},</if>
            <if test="end_time != null">end_time = #{end_time},</if>
            <if test="service_price != null">service_price = #{service_price},</if>
            <if test="car_price != null">car_price = #{car_price},</if>
            <if test="id_code != null">id_code = #{id_code},</if>
            <if test="star != null">star = #{star},</if>
            <if test="admin_add != null">admin_add = #{admin_add},</if>
            <if test="city_id != null">city_id = #{city_id},</if>
            <if test="video != null">video = #{video},</if>
            <if test="is_update != null">is_update = #{is_update},</if>
            <if test="integral != null">integral = #{integral},</if>
            <if test="order_num != null">order_num = #{order_num},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="balance_cash != null">balance_cash = #{balance_cash},</if>
            <if test="index_top != null">index_top = #{index_top},</if>
            <if test="coach_position != null">coach_position = #{coach_position},</if>
            <if test="near_time != null">near_time = #{near_time},</if>
            <if test="check_cash != null">check_cash = #{check_cash},</if>
            <if test="agent_type != null">agent_type = #{agent_type},</if>
            <if test="partner_id != null">partner_id = #{partner_id},</if>
            <if test="partner_time != null">partner_time = #{partner_time},</if>
            <if test="total_order_num != null">total_order_num = #{total_order_num},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="credit_value != null">credit_value = #{credit_value},</if>
            <if test="credit_top != null">credit_top = #{credit_top},</if>
            <if test="recommend_icon != null">recommend_icon = #{recommend_icon},</if>
            <if test="free_fare_distance != null">free_fare_distance = #{free_fare_distance},</if>
            <if test="show_salenum != null">show_salenum = #{show_salenum},</if>
            <if test="coach_icon != null">coach_icon = #{coach_icon},</if>
            <if test="true_lng != null">true_lng = #{true_lng},</if>
            <if test="true_lat != null">true_lat = #{true_lat},</if>
            <if test="type_id != null">type_id = #{type_id},</if>
            <if test="version != null">version = #{version},</if>
            <if test="true_user_name != null">true_user_name = #{true_user_name},</if>
            <if test="industry_type != null">industry_type = #{industry_type},</if>
            <if test="height != null">height = #{height},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="constellation != null">constellation = #{constellation},</if>
            <if test="free_fare_bear != null">free_fare_bear = #{free_fare_bear},</if>
            <if test="personality_icon != null">personality_icon = #{personality_icon},</if>
            <if test="station_icon != null">station_icon = #{station_icon},</if>
            <if test="address_update_time != null">address_update_time = #{address_update_time},</if>
            <if test="pv != null">pv = #{pv},</if>
            <if test="virtual_collect != null">virtual_collect = #{virtual_collect},</if>
            <if test="virtual_comment != null">virtual_comment = #{virtual_comment},</if>
            <if test="work_type != null">work_type = #{work_type},</if>
            <if test="data != null">data = #{data},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMassageCoachById" parameterType="Long">
        delete from ims_massage_service_coach_list where id = #{id}
    </delete>

    <delete id="deleteMassageCoachByIds" parameterType="String">
        delete from ims_massage_service_coach_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMassageCoachByPhone" parameterType="String" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where mobile = #{phone}
    </select>

    <select id="selectMassageCoachByIdCard" parameterType="String" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where id_card = #{idCard}
    </select>

    <select id="getCoachStatistics" resultType="java.util.Map">
        select
            count(*) as totalCoaches,
            count(case when status = 1 then 1 end) as pendingAudit,
            count(case when status = 2 then 1 end) as normalCoaches,
            count(case when status = 3 then 1 end) as disabledCoaches
        from ims_massage_service_coach_list
    </select>

    <select id="getOnlineCoaches" resultMap="MassageCoachResult">
        <include refid="selectMassageCoachVo"/>
        where status = 2
        <if test="city != null and city != ''">
            and city = #{city}
        </if>
        order by create_time desc
    </select>

    <select id="getCoachIncomeStats" resultType="java.util.Map">
        select 
            c.id,
            c.coach_name,
            c.mobile,
            coalesce(sum(o.coach_income), 0) as totalIncome,
            count(o.id) as orderCount
        from ims_massage_service_coach_list c
        left join ims_massage_service_order_list o on c.id = o.coach_id 
            and o.pay_type in (2,3,4,5,6,8)
            and date_format(from_unixtime(o.pay_time),'%Y-%m-%d') between #{startDate} and #{endDate}
        where c.status = 2
        group by c.id
        order by totalIncome desc
    </select>

    <select id="getCoachServiceStats" parameterType="Long" resultType="java.util.Map">
        select 
            count(*) as totalOrders,
            coalesce(sum(coach_income), 0) as totalIncome,
            coalesce(avg(star), 0) as avgRating,
            count(case when star >= 4 then 1 end) as goodComments
        from ims_massage_service_order_list
        where coach_id = #{coachId} and pay_type in (2,3,4,5,6,8)
    </select>

    <select id="getTopCoaches" parameterType="Integer" resultType="java.util.Map">
        select
            c.id,
            c.coach_name,
            c.work_img,
            c.status
        from ims_massage_service_coach_list c
        where c.status = 2
        order by c.create_time desc
        limit #{limit}
    </select>

    <select id="getCoachLevelDistribution" resultType="java.util.Map">
        select
            status,
            count(*) as count
        from ims_massage_service_coach_list
        where status = 2
        group by status
        order by status
    </select>

    <select id="getCoachCityDistribution" resultType="java.util.Map">
        select 
            city,
            count(*) as count
        from ims_massage_service_coach_list
        where status = 2 and city is not null and city != ''
        group by city
        order by count desc
        limit 10
    </select>

    <update id="batchAuditCoach">
        update ims_massage_service_coach_list
        set status = #{status}, sh_time = unix_timestamp(), sh_text = #{auditRemark}
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getPendingAuditCount" resultType="Long">
        select count(*) from ims_massage_service_coach_list where status = 1
    </select>

    <select id="getTodayNewCoaches" resultType="Long">
        select count(*) from ims_massage_service_coach_list 
        where date_format(from_unixtime(create_time),'%Y-%m-%d') = curdate()
    </select>

    <select id="getCoachWorkTimeStats" resultType="java.util.Map">
        select 
            c.id,
            c.coach_name,
            count(o.id) as orderCount,
            sum(s.service_duration) as totalWorkMinutes
        from ims_massage_service_coach_list c
        left join ims_massage_service_order_list o on c.id = o.coach_id 
            and o.pay_type in (2,3,4,5,6,8)
            and date_format(from_unixtime(o.pay_time),'%Y-%m-%d') between #{startDate} and #{endDate}
        left join ims_massage_service_service_list s on o.service_id = s.id
        where c.status = 2
        group by c.id
        order by totalWorkMinutes desc
    </select>

</mapper>
