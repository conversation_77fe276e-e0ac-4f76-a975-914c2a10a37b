{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\api\\massage\\coachService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\api\\massage\\coachService.js", "mtime": 1753803150125}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\babel.config.js", "mtime": 1750390760000}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCoachService", "query", "request", "url", "method", "params", "listAvailableService", "addCoachService", "data", "delCoachService", "coachId", "serviceId", "getCoachWorkTime", "saveCoachWorkTime", "listCoachOrder"], "sources": ["C:/Users/<USER>/Desktop/peiwan2/Ruo<PERSON><PERSON>-<PERSON><PERSON>-master/ruoyi-ui/src/api/massage/coachService.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询达人已关联服务列表\nexport function listCoachService(query) {\n  return request({\n    url: '/massage/coachService/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询达人可关联服务列表\nexport function listAvailableService(query) {\n  return request({\n    url: '/massage/coachService/available',\n    method: 'get',\n    params: query\n  })\n}\n\n// 关联服务\nexport function addCoachService(data) {\n  return request({\n    url: '/massage/coachService',\n    method: 'post',\n    data: data\n  })\n}\n\n// 移除关联服务\nexport function delCoachService(coachId, serviceId) {\n  return request({\n    url: '/massage/coachService/' + coachId + '/' + serviceId,\n    method: 'delete'\n  })\n}\n\n// 获取达人接单时间\nexport function getCoachWorkTime(coachId) {\n  return request({\n    url: '/massage/coachWorkTime/' + coachId,\n    method: 'get'\n  })\n}\n\n// 保存达人接单时间\nexport function saveCoachWorkTime(data) {\n  return request({\n    url: '/massage/coachWorkTime',\n    method: 'post',\n    data: data\n  })\n}\n\n// 查询达人服务记录\nexport function listCoachOrder(query) {\n  return request({\n    url: '/massage/coachOrder/list',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACL,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAClD,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGO,OAAO,GAAG,GAAG,GAAGC,SAAS;IACzDP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,gBAAgBA,CAACF,OAAO,EAAE;EACxC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGO,OAAO;IACxCN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,iBAAiBA,CAACL,IAAI,EAAE;EACtC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,cAAcA,CAACb,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}