{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue", "mtime": 1753764978961}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXJDb21tZW50TGlzdCwgdXBkYXRlQ29tbWVudCB9IGZyb20gIkAvYXBpL21hc3NhZ2UvY29tbWVudCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkNvbW1lbnRMaXN0VGFiIiwKICBkaWN0czogWydtYXNzYWdlX2NvbW1lbnRfc3RhdHVzJ10sCiAgcHJvcHM6IHsKICAgIHVzZXJJZDogewogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDor4Tku7fooajmoLzmlbDmja4KICAgICAgY29tbWVudExpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgZGV0YWlsT3BlbjogZmFsc2UsCiAgICAgIGVkaXRPcGVuOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsCiAgICAgICAgc3RhcjogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGRldGFpbEZvcm06IHt9LAogICAgICBlZGl0Rm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBlZGl0UnVsZXM6IHsKICAgICAgICBzdGF0dXM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor4Tku7fnirbmgIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvLyDnu5/orqHkv6Hmga8KICAgICAgc3RhdGlzdGljczogewogICAgICAgIHRvdGFsQ29tbWVudHM6IDAsCiAgICAgICAgYXZnU3RhcjogMCwKICAgICAgICBnb29kQ29tbWVudHM6IDAsCiAgICAgICAgZ29vZFJhdGU6IDAKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lor4Tku7fliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKTsKICAgICAgZ2V0VXNlckNvbW1lbnRMaXN0KHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jb21tZW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6K6h566X57uf6K6h5L+h5oGvICovCiAgICBnZXRTdGF0aXN0aWNzKCkgewogICAgICB0aGlzLnN0YXRpc3RpY3MgPSB7CiAgICAgICAgdG90YWxDb21tZW50czogdGhpcy5jb21tZW50TGlzdC5sZW5ndGgsCiAgICAgICAgYXZnU3RhcjogMCwKICAgICAgICBnb29kQ29tbWVudHM6IDAsCiAgICAgICAgZ29vZFJhdGU6IDAKICAgICAgfTsKICAgICAgCiAgICAgIGlmICh0aGlzLmNvbW1lbnRMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zdCB0b3RhbFN0YXIgPSB0aGlzLmNvbW1lbnRMaXN0LnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyAoaXRlbS5zdGFyIHx8IDApLCAwKTsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MuYXZnU3RhciA9ICh0b3RhbFN0YXIgLyB0aGlzLmNvbW1lbnRMaXN0Lmxlbmd0aCkudG9GaXhlZCgxKTsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MuZ29vZENvbW1lbnRzID0gdGhpcy5jb21tZW50TGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnN0YXIgPj0gNCkubGVuZ3RoOwogICAgICAgIHRoaXMuc3RhdGlzdGljcy5nb29kUmF0ZSA9ICgodGhpcy5zdGF0aXN0aWNzLmdvb2RDb21tZW50cyAvIHRoaXMuY29tbWVudExpc3QubGVuZ3RoKSAqIDEwMCkudG9GaXhlZCgxKTsKICAgICAgfQogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5p+l55yL6K+E5Lu36K+m5oOFICovCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLmRldGFpbEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmRldGFpbE9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDnvJbovpHor4Tku7cgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5lZGl0Rm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHN0YXR1czogcm93LnN0YXR1cywKICAgICAgICB0b3A6IHJvdy50b3AKICAgICAgfTsKICAgICAgdGhpcy5lZGl0T3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOaPkOS6pOe8lui+kSAqLwogICAgc3VibWl0RWRpdCgpIHsKICAgICAgdGhpcy4kcmVmc1siZWRpdEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB1cGRhdGVDb21tZW50KHRoaXMuZWRpdEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5lZGl0T3BlbiA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5o+Q5L6b57uZ54i257uE5Lu26LCD55So55qE5Yi35paw5pa55rOVCiAgICByZWZyZXNoKCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["CommentListTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CommentListTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"评价星级\" prop=\"star\">\n        <el-select v-model=\"queryParams.star\" placeholder=\"请选择评价星级\" clearable>\n          <el-option label=\"5星\" value=\"5\" />\n          <el-option label=\"4星\" value=\"4\" />\n          <el-option label=\"3星\" value=\"3\" />\n          <el-option label=\"2星\" value=\"2\" />\n          <el-option label=\"1星\" value=\"1\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择评价状态\" clearable>\n          <el-option label=\"正常\" value=\"1\" />\n          <el-option label=\"隐藏\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"评价时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"mb8\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>总评价数</span>\n            <div class=\"number\">{{ statistics.totalComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>平均星级</span>\n            <div class=\"number\">{{ statistics.avgStar }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评数(4-5星)</span>\n            <div class=\"number\">{{ statistics.goodComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评率</span>\n            <div class=\"number\">{{ statistics.goodRate }}%</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 评价列表 -->\n    <el-table v-loading=\"loading\" :data=\"commentList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"orderCode\" width=\"120\" />\n      <el-table-column label=\"评价星级\" align=\"center\" prop=\"star\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            disabled\n            show-score\n            text-color=\"#ff9900\"\n            score-template=\"{value}星\">\n          </el-rate>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"text\" :show-overflow-tooltip=\"true\" min-width=\"200\" />\n      <el-table-column label=\"评价标签\" align=\"center\" prop=\"lableList\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"tag in scope.row.lableList\"\n            :key=\"tag\"\n            size=\"mini\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"技师\" align=\"center\" prop=\"coachName\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.massage_comment_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:comment:query']\"\n          >详情</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['massage:comment:edit']\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 评价详情对话框 -->\n    <el-dialog title=\"评价详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\n      <el-form ref=\"detailForm\" :model=\"detailForm\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"订单编号\">\n              <span>{{ detailForm.orderCode }}</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"技师\">\n              <span>{{ detailForm.coachName }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价星级\">\n              <el-rate\n                v-model=\"detailForm.star\"\n                disabled\n                show-score\n                text-color=\"#ff9900\"\n                score-template=\"{value}星\">\n              </el-rate>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价时间\">\n              <span>{{ parseTime(detailForm.createTimestamp * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"评价内容\">\n          <span>{{ detailForm.text }}</span>\n        </el-form-item>\n        <el-form-item label=\"评价标签\" v-if=\"detailForm.lableList && detailForm.lableList.length > 0\">\n          <el-tag\n            v-for=\"tag in detailForm.lableList\"\n            :key=\"tag\"\n            size=\"small\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"服务评价\" v-if=\"detailForm.serviceList && detailForm.serviceList.length > 0\">\n          <div v-for=\"service in detailForm.serviceList\" :key=\"service.serviceId\" style=\"margin-bottom: 10px;\">\n            <span>{{ service.serviceName }}：</span>\n            <el-rate\n              v-model=\"service.star\"\n              disabled\n              show-score\n              text-color=\"#ff9900\"\n              score-template=\"{value}星\">\n            </el-rate>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 编辑评价对话框 -->\n    <el-dialog title=\"编辑评价\" :visible.sync=\"editOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"editForm\" :model=\"editForm\" :rules=\"editRules\" label-width=\"80px\">\n        <el-form-item label=\"评价状态\" prop=\"status\">\n          <el-radio-group v-model=\"editForm.status\">\n            <el-radio :label=\"1\">正常</el-radio>\n            <el-radio :label=\"0\">隐藏</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"是否置顶\" prop=\"top\">\n          <el-radio-group v-model=\"editForm.top\">\n            <el-radio :label=\"1\">是</el-radio>\n            <el-radio :label=\"0\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitEdit\">确 定</el-button>\n        <el-button @click=\"editOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUserCommentList, updateComment } from \"@/api/massage/comment\";\n\nexport default {\n  name: \"CommentListTab\",\n  dicts: ['massage_comment_status'],\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 评价表格数据\n      commentList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      detailOpen: false,\n      editOpen: false,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        star: null,\n        status: null\n      },\n      // 表单参数\n      detailForm: {},\n      editForm: {},\n      // 表单校验\n      editRules: {\n        status: [\n          { required: true, message: \"评价状态不能为空\", trigger: \"change\" }\n        ]\n      },\n      // 统计信息\n      statistics: {\n        totalComments: 0,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询评价列表 */\n    getList() {\n      this.loading = true;\n      const params = this.addDateRange(this.queryParams, this.dateRange);\n      getUserCommentList(params).then(response => {\n        this.commentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n        this.getStatistics();\n      });\n    },\n    /** 计算统计信息 */\n    getStatistics() {\n      this.statistics = {\n        totalComments: this.commentList.length,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      };\n      \n      if (this.commentList.length > 0) {\n        const totalStar = this.commentList.reduce((sum, item) => sum + (item.star || 0), 0);\n        this.statistics.avgStar = (totalStar / this.commentList.length).toFixed(1);\n        this.statistics.goodComments = this.commentList.filter(item => item.star >= 4).length;\n        this.statistics.goodRate = ((this.statistics.goodComments / this.commentList.length) * 100).toFixed(1);\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 查看评价详情 */\n    handleView(row) {\n      this.detailForm = { ...row };\n      this.detailOpen = true;\n    },\n    /** 编辑评价 */\n    handleUpdate(row) {\n      this.editForm = {\n        id: row.id,\n        status: row.status,\n        top: row.top\n      };\n      this.editOpen = true;\n    },\n    /** 提交编辑 */\n    submitEdit() {\n      this.$refs[\"editForm\"].validate(valid => {\n        if (valid) {\n          updateComment(this.editForm).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.editOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  text-align: center;\n}\n.text.item {\n  padding: 10px;\n}\n.number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-top: 5px;\n}\n</style>\n"]}]}