{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\CommentListTab.vue", "mtime": 1753765892556}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXJDb21tZW50TGlzdCB9IGZyb20gIkAvYXBpL21hc3NhZ2UvY29tbWVudCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkNvbW1lbnRMaXN0VGFiIiwKICBwcm9wczogewogICAgdXNlcklkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6K+E5Lu36KGo5qC85pWw5o2uCiAgICAgIGNvbW1lbnRMaXN0OiBbXSwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIGRldGFpbE9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBkZXRhaWxGb3JtOiB7fSwKICAgICAgLy8g57uf6K6h5L+h5oGvCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICB0b3RhbENvbW1lbnRzOiAwLAogICAgICAgIGF2Z1N0YXI6IDAsCiAgICAgICAgZ29vZENvbW1lbnRzOiAwLAogICAgICAgIGdvb2RSYXRlOiAwCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6K+E5Lu35YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRVc2VyQ29tbWVudExpc3QodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jb21tZW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6K6h566X57uf6K6h5L+h5oGvICovCiAgICBnZXRTdGF0aXN0aWNzKCkgewogICAgICB0aGlzLnN0YXRpc3RpY3MgPSB7CiAgICAgICAgdG90YWxDb21tZW50czogdGhpcy5jb21tZW50TGlzdC5sZW5ndGgsCiAgICAgICAgYXZnU3RhcjogMCwKICAgICAgICBnb29kQ29tbWVudHM6IDAsCiAgICAgICAgZ29vZFJhdGU6IDAKICAgICAgfTsKCiAgICAgIGlmICh0aGlzLmNvbW1lbnRMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zdCB0b3RhbFN0YXIgPSB0aGlzLmNvbW1lbnRMaXN0LnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyAoaXRlbS5zdGFyIHx8IDApLCAwKTsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MuYXZnU3RhciA9ICh0b3RhbFN0YXIgLyB0aGlzLmNvbW1lbnRMaXN0Lmxlbmd0aCkudG9GaXhlZCgxKTsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MuZ29vZENvbW1lbnRzID0gdGhpcy5jb21tZW50TGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnN0YXIgPj0gNCkubGVuZ3RoOwogICAgICAgIHRoaXMuc3RhdGlzdGljcy5nb29kUmF0ZSA9ICgodGhpcy5zdGF0aXN0aWNzLmdvb2RDb21tZW50cyAvIHRoaXMuY29tbWVudExpc3QubGVuZ3RoKSAqIDEwMCkudG9GaXhlZCgxKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmn6XnnIvor4Tku7for6bmg4UgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIHRoaXMuZGV0YWlsRm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOWkhOeQhuagh+etvuaWh+acrCAqLwogICAgZ2V0TGFibGVUYWdzKGxhYmxlVGV4dCkgewogICAgICBpZiAoIWxhYmxlVGV4dCkgcmV0dXJuIFtdOwogICAgICByZXR1cm4gbGFibGVUZXh0LnNwbGl0KCcsJykuZmlsdGVyKHRhZyA9PiB0YWcgJiYgdGFnLnRyaW0oKSk7CiAgICB9LAogICAgLy8g5o+Q5L6b57uZ54i257uE5Lu26LCD55So55qE5Yi35paw5pa55rOVCiAgICByZWZyZXNoKCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["CommentListTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CommentListTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"mb8\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>总评价数</span>\n            <div class=\"number\">{{ statistics.totalComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>平均星级</span>\n            <div class=\"number\">{{ statistics.avgStar }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评数(4-5星)</span>\n            <div class=\"number\">{{ statistics.goodComments }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"text item\">\n            <span>好评率</span>\n            <div class=\"number\">{{ statistics.goodRate }}%</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 评价列表 -->\n    <el-table v-loading=\"loading\" :data=\"commentList\">\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"order_code\" width=\"150\" />\n      <el-table-column label=\"评价星级\" align=\"center\" prop=\"star\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            disabled\n            show-score\n            text-color=\"#ff9900\"\n            score-template=\"{value}星\">\n          </el-rate>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"text\" :show-overflow-tooltip=\"true\" min-width=\"200\" />\n      <el-table-column label=\"评价标签\" align=\"center\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"tag in getLableTags(scope.row.lable_text)\"\n            :key=\"tag\"\n            size=\"mini\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"技师\" align=\"center\" prop=\"coach_name\" width=\"100\" />\n      <el-table-column label=\"评价时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n          >详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 评价详情对话框 -->\n    <el-dialog title=\"评价详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\n      <el-form ref=\"detailForm\" :model=\"detailForm\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"订单编号\">\n              <span>{{ detailForm.order_code }}</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"技师\">\n              <span>{{ detailForm.coach_name }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价星级\">\n              <el-rate\n                v-model=\"detailForm.star\"\n                disabled\n                show-score\n                text-color=\"#ff9900\"\n                score-template=\"{value}星\">\n              </el-rate>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价时间\">\n              <span>{{ parseTime(detailForm.create_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"评价内容\">\n          <span>{{ detailForm.text }}</span>\n        </el-form-item>\n        <el-form-item label=\"评价标签\" v-if=\"getLableTags(detailForm.lable_text).length > 0\">\n          <el-tag\n            v-for=\"tag in getLableTags(detailForm.lable_text)\"\n            :key=\"tag\"\n            size=\"small\"\n            style=\"margin: 2px;\">\n            {{ tag }}\n          </el-tag>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport { getUserCommentList } from \"@/api/massage/comment\";\n\nexport default {\n  name: \"CommentListTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 评价表格数据\n      commentList: [],\n      // 是否显示弹出层\n      detailOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId\n      },\n      // 表单参数\n      detailForm: {},\n      // 统计信息\n      statistics: {\n        totalComments: 0,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询评价列表 */\n    getList() {\n      this.loading = true;\n      getUserCommentList(this.queryParams).then(response => {\n        this.commentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n        this.getStatistics();\n      });\n    },\n    /** 计算统计信息 */\n    getStatistics() {\n      this.statistics = {\n        totalComments: this.commentList.length,\n        avgStar: 0,\n        goodComments: 0,\n        goodRate: 0\n      };\n\n      if (this.commentList.length > 0) {\n        const totalStar = this.commentList.reduce((sum, item) => sum + (item.star || 0), 0);\n        this.statistics.avgStar = (totalStar / this.commentList.length).toFixed(1);\n        this.statistics.goodComments = this.commentList.filter(item => item.star >= 4).length;\n        this.statistics.goodRate = ((this.statistics.goodComments / this.commentList.length) * 100).toFixed(1);\n      }\n    },\n    /** 查看评价详情 */\n    handleView(row) {\n      this.detailForm = { ...row };\n      this.detailOpen = true;\n    },\n    /** 处理标签文本 */\n    getLableTags(lableText) {\n      if (!lableText) return [];\n      return lableText.split(',').filter(tag => tag && tag.trim());\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  text-align: center;\n}\n.text.item {\n  padding: 10px;\n}\n.number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-top: 5px;\n}\n</style>\n"]}]}