{"name": "guzzle/plugin-history", "description": "Guzzle history plugin", "homepage": "http://guzzlephp.org/", "keywords": ["plugin", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/http": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Plugin\\History": ""}}, "target-dir": "Guzzle/Plugin/History", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}