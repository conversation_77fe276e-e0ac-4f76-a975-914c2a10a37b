<?php
namespace app\coachbroker\controller;
use app\AdminRest;
use app\massage\model\Config;
use app\massage\model\ConfigSetting;
use think\App;



class AdminSetting extends AdminRest
{


    protected $model;

    protected $admin_model;


    public function __construct(App $app) {

        parent::__construct($app);

        $this->model = new Config();
    }


    /**
     * <AUTHOR>
     * @DataTime: 2021-03-12 15:04
     * @功能说明:配置详情
     */
    public function configInfo(){

        $arr = [

            'broker_apply_port',
            'broker_cash_type',
            'broker_poster',
            'coach_agent_balance',
            'partner_admin_balance',
            'partner_coach_balance',
            'broker_menu_name',
            'broker_status',
        ];

        $data = getConfigSettingArr($this->_uniacid,$arr);

        return $this->success($data);
    }


    /**
     * <AUTHOR>
     * @DataTime: 2021-03-12 16:14
     * @功能说明:编辑配置
     */
    public function configUpdate(){

        $input = $this->_input;

        $update = [

            'broker_apply_port' => $input['broker_apply_port'],
            'broker_cash_type' => $input['broker_cash_type'],
            'broker_poster' => $input['broker_poster'],
            'coach_agent_balance' => $input['coach_agent_balance'],
            'partner_admin_balance' => $input['partner_admin_balance'],
            'partner_coach_balance' => $input['partner_coach_balance'],
            'broker_menu_name' => $input['broker_menu_name'],
            'broker_status' => $input['broker_status'],
        ];

        $config_model = new ConfigSetting();

        $res = $config_model->dataUpdate($update,$this->_uniacid);

        return $this->success($res);
    }








}
