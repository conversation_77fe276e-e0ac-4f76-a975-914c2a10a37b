{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\coach\\index.vue", "mtime": 1753800869549}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RDb2FjaCwgZ2V0Q29hY2gsIGRlbENvYWNoLCB1cGRhdGVDb2FjaCwgYXVkaXRDb2FjaCB9IGZyb20gIkAvYXBpL21hc3NhZ2UvY29hY2giOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJDb2FjaCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDovr7kurrooajmoLzmlbDmja4KICAgICAgY29hY2hMaXN0OiBbXSwKCiAgICAgIGF1ZGl0T3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGNvYWNoX25hbWU6IG51bGwsCiAgICAgICAgbW9iaWxlOiBudWxsLAogICAgICAgIHN0YXR1czogbnVsbCwKICAgICAgICBhdXRoX3N0YXR1czogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIGF1ZGl0Rm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGNvYWNoX25hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLovr7kurrlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgbW9iaWxlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omL5py65Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBhdWRpdFJ1bGVzOiB7CiAgICAgICAgc3RhdHVzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6h5qC454q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lovr7kurrliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RDb2FjaCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmNvYWNoTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL21hc3NhZ2UvY29hY2gvZWRpdCIgfSk7CiAgICB9LAogICAgLyoqIOe8lui+keaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL21hc3NhZ2UvY29hY2gvZWRpdCIsIHF1ZXJ5OiB7IGlkOiByb3cuaWQgfSB9KTsKICAgIH0sCiAgICAvKiog5om56YeP57yW6L6R5pON5L2cICovCiAgICBoYW5kbGVCYXRjaEVkaXQoKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDEpIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL21hc3NhZ2UvY29hY2gvZWRpdD9pZD0iICsgdGhpcy5pZHNbMF0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nkuIDmnaHmlbDmja7ov5vooYznvJbovpEiKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmjqjojZDnirbmgIHliIfmjaIgKi8KICAgIGhhbmRsZVJlY29tbWVuZENoYW5nZShyb3cpIHsKICAgICAgY29uc3QgdGV4dCA9IHJvdy5yZWNvbW1lbmQgPT09IDEgPyAi5o6o6I2QIiA6ICLlj5bmtojmjqjojZAiOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LmNvYWNoX25hbWUgKyAnIui+vuS6uuWQl++8nycpLnRoZW4oKCkgPT4gewogICAgICAgIHJldHVybiB1cGRhdGVDb2FjaCh7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgcmVjb21tZW5kOiByb3cucmVjb21tZW5kCiAgICAgICAgfSk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHJvdy5yZWNvbW1lbmQgPSByb3cucmVjb21tZW5kID09PSAxID8gMCA6IDE7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmm7TlpJroj5zljZXmk43kvZwgKi8KICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgImF1dGgiOgogICAgICAgICAgdGhpcy5oYW5kbGVBdXRoQ29hY2gocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImRpc2FibGUiOgogICAgICAgICAgdGhpcy5oYW5kbGVTdGF0dXNDaGFuZ2Uocm93LCAzLCAi56aB55SoIik7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJlbmFibGUiOgogICAgICAgICAgdGhpcy5oYW5kbGVTdGF0dXNDaGFuZ2Uocm93LCAyLCAi5ZCv55SoIik7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJkZWxldGUiOgogICAgICAgICAgdGhpcy5oYW5kbGVEZWxldGUocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLyoqIOaOiOadg+i+vuS6uiAqLwogICAgaGFuZGxlQXV0aENvYWNoKHJvdykgewogICAgICBsZXQgc3RhdHVzVGV4dCA9ICIiOwogICAgICBsZXQgbmV3U3RhdHVzID0gMjsKICAgICAgbGV0IG5ld0F1dGhTdGF0dXMgPSAyOwoKICAgICAgaWYgKHJvdy5zdGF0dXMgPT0gMSkgewogICAgICAgIHN0YXR1c1RleHQgPSAi6YCa6L+H6L6+5Lq65a6h5qC4IjsKICAgICAgfSBlbHNlIGlmIChyb3cuYXV0aF9zdGF0dXMgPT0gMSkgewogICAgICAgIHN0YXR1c1RleHQgPSAi6YCa6L+H6K6k6K+B5a6h5qC4IjsKICAgICAgfQoKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyBzdGF0dXNUZXh0ICsgJyIiJyArIHJvdy5jb2FjaF9uYW1lICsgJyLovr7kurrlkJfvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICByZXR1cm4gdXBkYXRlQ29hY2goewogICAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICAgIHN0YXR1czogbmV3U3RhdHVzLAogICAgICAgICAgYXV0aF9zdGF0dXM6IG5ld0F1dGhTdGF0dXMsCiAgICAgICAgICBzaF90aW1lOiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKSwKICAgICAgICAgIHNoX3RleHQ6ICLnrqHnkIblkZjmjojmnYPpgJrov4ciCiAgICAgICAgfSk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3Moc3RhdHVzVGV4dCArICLmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDnirbmgIHlj5jmm7QgKi8KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3csIHN0YXR1cywgdGV4dCkgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LmNvYWNoX25hbWUgKyAnIui+vuS6uuWQl++8nycpLnRoZW4oKCkgPT4gewogICAgICAgIHJldHVybiB1cGRhdGVDb2FjaCh7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgc3RhdHVzOiBzdGF0dXMKICAgICAgICB9KTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6L6+5Lq657yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbENvYWNoKGlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAoKCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6L6+5Lq657yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbENvYWNoKGlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAoKICAgIC8qKiDovr7kurrlrqHmoLggKi8KICAgIGhhbmRsZUF1ZGl0KHJvdykgewogICAgICB0aGlzLmF1ZGl0Rm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHN0YXR1czogbnVsbCwKICAgICAgICBzaF90ZXh0OiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMuYXVkaXRPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5a6h5qC4ICovCiAgICBzdWJtaXRBdWRpdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImF1ZGl0Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGF1ZGl0Q29hY2godGhpcy5hdWRpdEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5hdWRpdE9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojlrqHmoLggKi8KICAgIGNhbmNlbEF1ZGl0KCkgewogICAgICB0aGlzLmF1ZGl0T3BlbiA9IGZhbHNlOwogICAgICB0aGlzLmF1ZGl0Rm9ybSA9IHt9OwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0MA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/massage/coach", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"达人姓名\" prop=\"coach_name\">\n        <el-input\n          v-model=\"queryParams.coach_name\"\n          placeholder=\"请输入达人姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号\" prop=\"mobile\">\n        <el-input\n          v-model=\"queryParams.mobile\"\n          placeholder=\"请输入手机号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"达人状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择达人状态\" clearable>\n          <el-option label=\"待审核\" value=\"1\" />\n          <el-option label=\"正常\" value=\"2\" />\n          <el-option label=\"禁用\" value=\"3\" />\n          <el-option label=\"审核失败\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"认证状态\" prop=\"auth_status\">\n        <el-select v-model=\"queryParams.auth_status\" placeholder=\"请选择认证状态\" clearable>\n          <el-option label=\"未认证\" value=\"0\" />\n          <el-option label=\"待审核\" value=\"1\" />\n          <el-option label=\"已认证\" value=\"2\" />\n          <el-option label=\"认证失败\" value=\"3\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"工作状态\" prop=\"is_work\">\n        <el-select v-model=\"queryParams.is_work\" placeholder=\"请选择工作状态\" clearable>\n          <el-option label=\"工作中\" value=\"1\" />\n          <el-option label=\"休息中\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"推荐状态\" prop=\"recommend\">\n        <el-select v-model=\"queryParams.recommend\" placeholder=\"请选择推荐状态\" clearable>\n          <el-option label=\"推荐\" value=\"1\" />\n          <el-option label=\"普通\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['massage:coach:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleBatchEdit\"\n          v-hasPermi=\"['massage:coach:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['massage:coach:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"coachList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"头像\" align=\"center\" prop=\"work_img\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.work_img\" :width=\"50\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"coach_name\" width=\"120\" />\n      <el-table-column label=\"手机号\" align=\"center\" prop=\"mobile\" width=\"120\" />\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <div>\n            <p>{{ parseTime(scope.row.create_time * 1000, '{y}-{m}-{d}') }}</p>\n            <p>{{ parseTime(scope.row.create_time * 1000, '{h}:{i}:{s}') }}</p>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"是否绑定\" align=\"center\" prop=\"user_id\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.user_id\" type=\"success\">已绑定</el-tag>\n          <el-tag v-else type=\"info\">未绑定</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"认证状态\" align=\"center\" prop=\"auth_status\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.auth_status == 0\" type=\"info\">未认证</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 2\" type=\"success\">已认证</el-tag>\n          <el-tag v-else-if=\"scope.row.auth_status == 3\" type=\"danger\">认证失败</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status == 1\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 2\" type=\"success\">正常</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 3\" type=\"danger\">禁用</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 4\" type=\"info\">审核失败</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"设为推荐\" align=\"center\" prop=\"recommend\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.recommend\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n            @change=\"handleRecommendChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"编辑\" align=\"center\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"primary\"\n            plain\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n            v-hasPermi=\"['massage:coach:edit']\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更多菜单\" align=\"center\" width=\"120\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-dropdown @command=\"(command) => handleCommand(command, scope.row)\">\n            <el-button size=\"mini\" type=\"danger\" plain>\n              更多菜单<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"auth\" v-if=\"scope.row.status == 1 || scope.row.auth_status == 1\" v-hasPermi=\"['massage:coach:audit']\">授权达人</el-dropdown-item>\n              <el-dropdown-item command=\"disable\" v-if=\"scope.row.status == 2\" v-hasPermi=\"['massage:coach:edit']\">禁用达人</el-dropdown-item>\n              <el-dropdown-item command=\"enable\" v-if=\"scope.row.status == 3\" v-hasPermi=\"['massage:coach:edit']\">启用达人</el-dropdown-item>\n              <el-dropdown-item command=\"delete\" v-hasPermi=\"['massage:coach:remove']\">删除</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n\n\n\n\n    <!-- 达人审核对话框 -->\n    <el-dialog title=\"达人审核\" :visible.sync=\"auditOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\n        <el-form-item label=\"审核状态\" prop=\"status\">\n          <el-radio-group v-model=\"auditForm.status\">\n            <el-radio :label=\"2\">审核通过</el-radio>\n            <el-radio :label=\"4\">审核失败</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核意见\" prop=\"sh_text\">\n          <el-input v-model=\"auditForm.sh_text\" type=\"textarea\" placeholder=\"请输入审核意见\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitAuditForm\">确 定</el-button>\n        <el-button @click=\"cancelAudit\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listCoach, getCoach, delCoach, updateCoach, auditCoach } from \"@/api/massage/coach\";\n\nexport default {\n  name: \"Coach\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 达人表格数据\n      coachList: [],\n\n      auditOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        coach_name: null,\n        mobile: null,\n        status: null,\n        auth_status: null\n      },\n      // 表单参数\n      form: {},\n      auditForm: {},\n      // 表单校验\n      rules: {\n        coach_name: [\n          { required: true, message: \"达人姓名不能为空\", trigger: \"blur\" }\n        ],\n        mobile: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      },\n      auditRules: {\n        status: [\n          { required: true, message: \"审核状态不能为空\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询达人列表 */\n    getList() {\n      this.loading = true;\n      listCoach(this.queryParams).then(response => {\n        this.coachList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.$router.push({ path: \"/massage/coach/edit\" });\n    },\n    /** 编辑按钮操作 */\n    handleEdit(row) {\n      this.$router.push({ path: \"/massage/coach/edit\", query: { id: row.id } });\n    },\n    /** 批量编辑操作 */\n    handleBatchEdit() {\n      if (this.ids.length === 1) {\n        this.$router.push(\"/massage/coach/edit?id=\" + this.ids[0]);\n      } else {\n        this.$modal.msgError(\"请选择一条数据进行编辑\");\n      }\n    },\n    /** 推荐状态切换 */\n    handleRecommendChange(row) {\n      const text = row.recommend === 1 ? \"推荐\" : \"取消推荐\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          recommend: row.recommend\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(() => {\n        row.recommend = row.recommend === 1 ? 0 : 1;\n      });\n    },\n    /** 更多菜单操作 */\n    handleCommand(command, row) {\n      switch (command) {\n        case \"auth\":\n          this.handleAuthCoach(row);\n          break;\n        case \"disable\":\n          this.handleStatusChange(row, 3, \"禁用\");\n          break;\n        case \"enable\":\n          this.handleStatusChange(row, 2, \"启用\");\n          break;\n        case \"delete\":\n          this.handleDelete(row);\n          break;\n      }\n    },\n    /** 授权达人 */\n    handleAuthCoach(row) {\n      let statusText = \"\";\n      let newStatus = 2;\n      let newAuthStatus = 2;\n\n      if (row.status == 1) {\n        statusText = \"通过达人审核\";\n      } else if (row.auth_status == 1) {\n        statusText = \"通过认证审核\";\n      }\n\n      this.$modal.confirm('确认要\"' + statusText + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          status: newStatus,\n          auth_status: newAuthStatus,\n          sh_time: Math.floor(Date.now() / 1000),\n          sh_text: \"管理员授权通过\"\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(statusText + \"成功\");\n      });\n    },\n\n    /** 状态变更 */\n    handleStatusChange(row, status, text) {\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.coach_name + '\"达人吗？').then(() => {\n        return updateCoach({\n          id: row.id,\n          status: status\n        });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除达人编号为\"' + ids + '\"的数据项？').then(function() {\n        return delCoach(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n\n\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除达人编号为\"' + ids + '\"的数据项？').then(function() {\n        return delCoach(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n\n    /** 达人审核 */\n    handleAudit(row) {\n      this.auditForm = {\n        id: row.id,\n        status: null,\n        sh_text: null\n      };\n      this.auditOpen = true;\n    },\n    /** 提交审核 */\n    submitAuditForm() {\n      this.$refs[\"auditForm\"].validate(valid => {\n        if (valid) {\n          auditCoach(this.auditForm).then(response => {\n            this.$modal.msgSuccess(\"审核成功\");\n            this.auditOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    /** 取消审核 */\n    cancelAudit() {\n      this.auditOpen = false;\n      this.auditForm = {};\n    }\n  }\n};\n</script>\n"]}]}