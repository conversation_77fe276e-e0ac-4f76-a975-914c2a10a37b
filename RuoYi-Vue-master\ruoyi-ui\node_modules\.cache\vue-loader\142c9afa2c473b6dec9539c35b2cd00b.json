{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\index.vue", "mtime": 1753759965053}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RVc2VyLCBnZXRVc2VyLCBkZWxVc2VyLCBhZGRVc2VyLCB1cGRhdGVVc2VyLCBhZGp1c3RCYWxhbmNlLCBhZGp1c3RHcm93dGgsIGNoYW5nZVN0YXR1cyB9IGZyb20gIkAvYXBpL21hc3NhZ2UvdXNlciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZScsICdzeXNfdXNlcl9zZXgnXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOaMieaRqeeUqOaIt+ihqOagvOaVsOaNrgogICAgICB1c2VyTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5L2Z6aKd6LCD5pW05by55Ye65bGCCiAgICAgIGJhbGFuY2VPcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbmlja05hbWU6IG51bGwsCiAgICAgICAgcGhvbmU6IG51bGwsCiAgICAgICAgc3RhdHVzOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgZ2VuZGVyOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5b2T5YmN5pON5L2c55qE55So5oi3CiAgICAgIGN1cnJlbnRVc2VyOiB7fSwKICAgICAgLy8g5L2Z6aKd6LCD5pW06KGo5Y2VCiAgICAgIGJhbGFuY2VGb3JtOiB7fSwKICAgICAgYmFsYW5jZU9wZW46IGZhbHNlLAogICAgICAvLyDmiJDplb/lgLzosIPmlbTooajljZUKICAgICAgZ3Jvd3RoRm9ybToge30sCiAgICAgIGdyb3d0aE9wZW46IGZhbHNlLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBuaWNrTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+aYteensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJi+acuuWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g5L2Z6aKd6LCD5pW05qCh6aqMCiAgICAgIGJhbGFuY2VSdWxlczogewogICAgICAgIGFtb3VudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiwg+aVtOmHkemineS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICByZW1hcms6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLosIPmlbTlpIfms6jkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g5oiQ6ZW/5YC86LCD5pW05qCh6aqMCiAgICAgIGdyb3d0aFJ1bGVzOiB7CiAgICAgICAgZ3Jvd3RoOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LCD5pW05oiQ6ZW/5YC85LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHJlbWFyazogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiwg+aVtOWkh+azqOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouaMieaRqeeUqOaIt+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFVzZXIodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgdW5pYWNpZDogbnVsbCwKICAgICAgICBvcGVuaWQ6IG51bGwsCiAgICAgICAgbmlja05hbWU6IG51bGwsCiAgICAgICAgYXZhdGFyVXJsOiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgc3RhdHVzOiAxLAogICAgICAgIGNhcElkOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgY291bnRyeTogbnVsbCwKICAgICAgICBnZW5kZXI6IG51bGwsCiAgICAgICAgbGFuZ3VhZ2U6IG51bGwsCiAgICAgICAgcHJvdmluY2U6IG51bGwsCiAgICAgICAgYmFsYW5jZTogMCwKICAgICAgICBwaG9uZTogbnVsbCwKICAgICAgICBzZXNzaW9uS2V5OiBudWxsLAogICAgICAgIHBpZDogbnVsbCwKICAgICAgICBjYXNoOiAwLAogICAgICAgIHVuaW9uaWQ6IG51bGwsCiAgICAgICAgYXBwT3BlbmlkOiBudWxsLAogICAgICAgIHdlYk9wZW5pZDogbnVsbCwKICAgICAgICB3ZWNoYXRPcGVuaWQ6IG51bGwsCiAgICAgICAgbGFzdExvZ2luVHlwZTogbnVsbCwKICAgICAgICBuZXdDYXNoOiBudWxsLAogICAgICAgIGxvY2s6IG51bGwsCiAgICAgICAgaXNGeDogbnVsbCwKICAgICAgICBpb3NPcGVuaWQ6IG51bGwsCiAgICAgICAgcHVzaElkOiBudWxsLAogICAgICAgIGFsaXBheU51bWJlcjogbnVsbCwKICAgICAgICBhbGlwYXlOYW1lOiBudWxsLAogICAgICAgIGlwOiBudWxsLAogICAgICAgIGdyb3d0aDogMCwKICAgICAgICBtZW1iZXJDYWxjdWxhdGVUaW1lOiBudWxsLAogICAgICAgIHVzZXJTdGF0dXM6IG51bGwsCiAgICAgICAgaXNRcjogbnVsbCwKICAgICAgICBzb3VyY2VUeXBlOiBudWxsLAogICAgICAgIGFyZWE6IG51bGwsCiAgICAgICAgYWRtaW5JZDogbnVsbCwKICAgICAgICBpc1BvcEltZzogbnVsbCwKICAgICAgICBtZW1iZXJEaXNjb3VudFRpbWU6IG51bGwsCiAgICAgICAgbWVtYmVyRGlzY291bnRJZDogbnVsbCwKICAgICAgICBwYXJ0bmVyTW9uZXk6IG51bGwsCiAgICAgICAgdG90YWxQYXJ0bmVyTW9uZXk6IG51bGwsCiAgICAgICAgZGVsVXNlcklkOiBudWxsLAogICAgICAgIGFsaXBheUlkQ29kZTogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDmjInmkannlKjmiLciOwogICAgfSwKICAgIC8qKiDmn6XnnIvor6bmg4Xmk43kvZwgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL21hc3NhZ2UvdXNlci9kZXRhaWwvJyArIHJvdy5pZAogICAgICB9KTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHMKICAgICAgZ2V0VXNlcihpZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55oyJ5pGp55So5oi3IjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkVXNlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmjInmkannlKjmiLfnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsVXNlcihpZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbWFzc2FnZS91c2VyL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGB1c2VyXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIC8qKiDnlKjmiLfnirbmgIHkv67mlLkgKi8KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAxID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy5uaWNrTmFtZSArICci55So5oi35ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5pZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24oKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09IDEgPyAyIDogMTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS4i+aLieiPnOWNleWRveS7pOWkhOeQhiAqLwogICAgaGFuZGxlRHJvcGRvd25Db21tYW5kKGNvbW1hbmQsIHJvdykgewogICAgICB0aGlzLmN1cnJlbnRVc2VyID0gcm93OwogICAgICBzd2l0Y2ggKGNvbW1hbmQpIHsKICAgICAgICBjYXNlICdiYWxhbmNlJzoKICAgICAgICAgIHRoaXMuaGFuZGxlQWRqdXN0QmFsYW5jZShyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAnZ3Jvd3RoJzoKICAgICAgICAgIHRoaXMuaGFuZGxlQWRqdXN0R3Jvd3RoKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICdibGFja2xpc3QnOgogICAgICAgICAgdGhpcy5oYW5kbGVCbGFja2xpc3RVc2VyKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8qKiDkvZnpop3osIPmlbQgKi8KICAgIGhhbmRsZUFkanVzdEJhbGFuY2Uocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXIgPSByb3c7CiAgICAgIHRoaXMuYmFsYW5jZUZvcm0gPSB7CiAgICAgICAgdXNlcklkOiByb3cuaWQsCiAgICAgICAgYW1vdW50OiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLmJhbGFuY2VPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5oiQ6ZW/5YC86LCD5pW0ICovCiAgICBoYW5kbGVBZGp1c3RHcm93dGgocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXIgPSByb3c7CiAgICAgIHRoaXMuZ3Jvd3RoRm9ybSA9IHsKICAgICAgICB1c2VySWQ6IHJvdy5pZCwKICAgICAgICBncm93dGg6IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMuZ3Jvd3RoT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOaLiem7keeUqOaItyAqLwogICAgaGFuZGxlQmxhY2tsaXN0VXNlcihyb3cpIHsKICAgICAgY29uc3QgdGV4dCA9IHJvdy5zdGF0dXMgPT09IDEgPyAi5ouJ6buRIiA6ICLop6PpmaTmi4npu5EiOwogICAgICBjb25zdCBuZXdTdGF0dXMgPSByb3cuc3RhdHVzID09PSAxID8gMiA6IDE7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cubmlja05hbWUgKyAnIueUqOaIt+WQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cuaWQsIG5ld1N0YXR1cyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24oKSB7CiAgICAgICAgLy8g5pON5L2c5Y+W5raICiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTkvZnpop3osIPmlbQgKi8KICAgIHN1Ym1pdEJhbGFuY2VGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJiYWxhbmNlRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGFkanVzdEJhbGFuY2UodGhpcy5iYWxhbmNlRm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS9memineiwg+aVtOaIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmJhbGFuY2VPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAvLyDlj5HpgIHkvZnpop3osIPmlbTmiJDlip/kuovku7YKICAgICAgICAgICAgdGhpcy4kZXZlbnRCdXMuJGVtaXQoJ3VzZXJCYWxhbmNlQWRqdXN0ZWQnLCB0aGlzLmJhbGFuY2VGb3JtLnVzZXJJZCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojkvZnpop3osIPmlbQgKi8KICAgIGNhbmNlbEJhbGFuY2UoKSB7CiAgICAgIHRoaXMuYmFsYW5jZU9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5iYWxhbmNlRm9ybSA9IHt9OwogICAgICB0aGlzLmN1cnJlbnRVc2VyID0ge307CiAgICB9LAogICAgLyoqIOaPkOS6pOaIkOmVv+WAvOiwg+aVtCAqLwogICAgc3VibWl0R3Jvd3RoRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZ3Jvd3RoRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGFkanVzdEdyb3d0aCh0aGlzLmdyb3d0aEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmiJDplb/lgLzosIPmlbTmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5ncm93dGhPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAvLyDlj5HpgIHmiJDplb/lgLzosIPmlbTmiJDlip/kuovku7YKICAgICAgICAgICAgdGhpcy4kZXZlbnRCdXMuJGVtaXQoJ3VzZXJHcm93dGhBZGp1c3RlZCcsIHRoaXMuZ3Jvd3RoRm9ybS51c2VySWQpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5oiQ6ZW/5YC86LCD5pW0ICovCiAgICBjYW5jZWxHcm93dGgoKSB7CiAgICAgIHRoaXMuZ3Jvd3RoT3BlbiA9IGZhbHNlOwogICAgICB0aGlzLmdyb3d0aEZvcm0gPSB7fTsKICAgICAgdGhpcy5jdXJyZW50VXNlciA9IHt9OwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4QA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/massage/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"用户昵称\" prop=\"nickName\">\n        <el-input\n          v-model=\"queryParams.nickName\"\n          placeholder=\"请输入用户昵称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号\" prop=\"phone\">\n        <el-input\n          v-model=\"queryParams.phone\"\n          placeholder=\"请输入手机号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"用户状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择用户状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"城市\" prop=\"city\">\n        <el-input\n          v-model=\"queryParams.city\"\n          placeholder=\"请输入城市\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-select v-model=\"queryParams.gender\" placeholder=\"请选择性别\" clearable>\n          <el-option label=\"未知\" value=\"0\" />\n          <el-option label=\"男\" value=\"1\" />\n          <el-option label=\"女\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['massage:user:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['massage:user:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['massage:user:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['massage:user:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"用户头像\" align=\"center\" prop=\"avatarUrl\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.avatarUrl\" :width=\"50\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"用户昵称\" align=\"center\" prop=\"nickName\" width=\"120\" />\n      <el-table-column label=\"手机号\" align=\"center\" prop=\"phone\" width=\"120\" />\n      <el-table-column label=\"会员等级\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.status === 1 ? 'success' : 'danger'\">\n            {{ scope.row.status === 1 ? '普通会员' : '已拉黑' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"用户余额\" align=\"center\" prop=\"balance\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #f56c6c;\">¥{{ scope.row.balance || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"现金余额\" align=\"center\" prop=\"cash\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #67c23a;\">¥{{ scope.row.cash || 0 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"成长值\" align=\"center\" prop=\"growth\" width=\"100\" />\n      <el-table-column label=\"消费总金额\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #409eff;\">¥{{ scope.row.totalConsumption || 0 }}</span>\n          <el-tooltip content=\"包含支付下单但尚未消费的金额\" placement=\"top\">\n            <i class=\"el-icon-question\" style=\"margin-left: 5px; color: #909399;\"></i>\n          </el-tooltip>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所在城市\" align=\"center\" prop=\"city\" width=\"120\" />\n      <el-table-column label=\"注册时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime * 1000) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"280\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['massage:user:query']\"\n          >查看</el-button>\n          <el-dropdown @command=\"(command) => handleDropdownCommand(command, scope.row)\" trigger=\"click\">\n            <el-button size=\"mini\" type=\"text\">\n              修改/充值<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"balance\">修改余额</el-dropdown-item>\n              <el-dropdown-item command=\"growth\">修改成长</el-dropdown-item>\n              <el-dropdown-item command=\"blacklist\" divided>拉黑用户</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改按摩用户对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"用户昵称\" prop=\"nickName\">\n          <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" />\n        </el-form-item>\n        <el-form-item label=\"性别\" prop=\"gender\">\n          <el-select v-model=\"form.gender\" placeholder=\"请选择性别\">\n            <el-option\n              v-for=\"dict in dict.type.sys_user_sex\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"parseInt(dict.value)\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"城市\" prop=\"city\">\n          <el-input v-model=\"form.city\" placeholder=\"请输入城市\" />\n        </el-form-item>\n        <el-form-item label=\"省份\" prop=\"province\">\n          <el-input v-model=\"form.province\" placeholder=\"请输入省份\" />\n        </el-form-item>\n        <el-form-item label=\"国家\" prop=\"country\">\n          <el-input v-model=\"form.country\" placeholder=\"请输入国家\" />\n        </el-form-item>\n        <el-form-item label=\"语言\" prop=\"language\">\n          <el-input v-model=\"form.language\" placeholder=\"请输入语言\" />\n        </el-form-item>\n        <el-form-item label=\"地区\" prop=\"area\">\n          <el-input v-model=\"form.area\" placeholder=\"请输入地区\" />\n        </el-form-item>\n        <el-form-item label=\"IP地址\" prop=\"ip\">\n          <el-input v-model=\"form.ip\" placeholder=\"请输入IP地址\" />\n        </el-form-item>\n        <el-form-item label=\"应用ID\" prop=\"uniacid\">\n          <el-input v-model=\"form.uniacid\" placeholder=\"请输入应用ID\" />\n        </el-form-item>\n        <el-form-item label=\"来源类型\" prop=\"sourceType\">\n          <el-input v-model=\"form.sourceType\" placeholder=\"请输入来源类型\" />\n        </el-form-item>\n        <el-form-item label=\"支付宝账号\" prop=\"alipayNumber\">\n          <el-input v-model=\"form.alipayNumber\" placeholder=\"请输入支付宝账号\" />\n        </el-form-item>\n        <el-form-item label=\"支付宝姓名\" prop=\"alipayName\">\n          <el-input v-model=\"form.alipayName\" placeholder=\"请输入支付宝姓名\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n\n    <!-- 余额调整对话框 -->\n    <el-dialog title=\"余额调整\" :visible.sync=\"balanceOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"balanceForm\" :model=\"balanceForm\" :rules=\"balanceRules\" label-width=\"80px\">\n        <el-form-item label=\"当前余额\">\n          <span style=\"color: #f56c6c; font-weight: bold;\">¥{{ currentUser.balance || 0 }}</span>\n        </el-form-item>\n        <el-form-item label=\"调整金额\" prop=\"amount\">\n          <el-input v-model=\"balanceForm.amount\" placeholder=\"请输入调整金额（正数为增加，负数为减少）\" />\n        </el-form-item>\n        <el-form-item label=\"调整备注\" prop=\"remark\">\n          <el-input v-model=\"balanceForm.remark\" type=\"textarea\" placeholder=\"请输入调整备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitBalanceForm\">确 定</el-button>\n        <el-button @click=\"cancelBalance\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 成长值调整对话框 -->\n    <el-dialog title=\"成长值调整\" :visible.sync=\"growthOpen\" width=\"400px\" append-to-body>\n      <el-form ref=\"growthForm\" :model=\"growthForm\" :rules=\"growthRules\" label-width=\"80px\">\n        <el-form-item label=\"当前成长值\">\n          <span style=\"color: #67c23a; font-weight: bold;\">{{ currentUser.growth || 0 }}</span>\n        </el-form-item>\n        <el-form-item label=\"调整成长值\" prop=\"growth\">\n          <el-input v-model=\"growthForm.growth\" placeholder=\"请输入调整成长值（正数为增加，负数为减少）\" />\n        </el-form-item>\n        <el-form-item label=\"调整备注\" prop=\"remark\">\n          <el-input v-model=\"growthForm.remark\" type=\"textarea\" placeholder=\"请输入调整备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitGrowthForm\">确 定</el-button>\n        <el-button @click=\"cancelGrowth\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listUser, getUser, delUser, addUser, updateUser, adjustBalance, adjustGrowth, changeStatus } from \"@/api/massage/user\";\n\nexport default {\n  name: \"User\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 按摩用户表格数据\n      userList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 余额调整弹出层\n      balanceOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        nickName: null,\n        phone: null,\n        status: null,\n        city: null,\n        gender: null\n      },\n      // 表单参数\n      form: {},\n      // 当前操作的用户\n      currentUser: {},\n      // 余额调整表单\n      balanceForm: {},\n      balanceOpen: false,\n      // 成长值调整表单\n      growthForm: {},\n      growthOpen: false,\n      // 表单校验\n      rules: {\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, message: \"手机号不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      },\n      // 余额调整校验\n      balanceRules: {\n        amount: [\n          { required: true, message: \"调整金额不能为空\", trigger: \"blur\" }\n        ],\n        remark: [\n          { required: true, message: \"调整备注不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 成长值调整校验\n      growthRules: {\n        growth: [\n          { required: true, message: \"调整成长值不能为空\", trigger: \"blur\" }\n        ],\n        remark: [\n          { required: true, message: \"调整备注不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询按摩用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.queryParams).then(response => {\n        this.userList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        uniacid: null,\n        openid: null,\n        nickName: null,\n        avatarUrl: null,\n        createTime: null,\n        status: 1,\n        capId: null,\n        city: null,\n        country: null,\n        gender: null,\n        language: null,\n        province: null,\n        balance: 0,\n        phone: null,\n        sessionKey: null,\n        pid: null,\n        cash: 0,\n        unionid: null,\n        appOpenid: null,\n        webOpenid: null,\n        wechatOpenid: null,\n        lastLoginType: null,\n        newCash: null,\n        lock: null,\n        isFx: null,\n        iosOpenid: null,\n        pushId: null,\n        alipayNumber: null,\n        alipayName: null,\n        ip: null,\n        growth: 0,\n        memberCalculateTime: null,\n        userStatus: null,\n        isQr: null,\n        sourceType: null,\n        area: null,\n        adminId: null,\n        isPopImg: null,\n        memberDiscountTime: null,\n        memberDiscountId: null,\n        partnerMoney: null,\n        totalPartnerMoney: null,\n        delUserId: null,\n        alipayIdCode: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加按摩用户\";\n    },\n    /** 查看详情操作 */\n    handleView(row) {\n      this.$router.push({\n        path: '/massage/user/detail/' + row.id\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getUser(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改按摩用户\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除按摩用户编号为\"' + ids + '\"的数据项？').then(function() {\n        return delUser(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('massage/user/export', {\n        ...this.queryParams\n      }, `user_${new Date().getTime()}.xlsx`)\n    },\n    /** 用户状态修改 */\n    handleStatusChange(row) {\n      let text = row.status === 1 ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.nickName + '\"用户吗？').then(function() {\n        return changeStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === 1 ? 2 : 1;\n      });\n    },\n    /** 下拉菜单命令处理 */\n    handleDropdownCommand(command, row) {\n      this.currentUser = row;\n      switch (command) {\n        case 'balance':\n          this.handleAdjustBalance(row);\n          break;\n        case 'growth':\n          this.handleAdjustGrowth(row);\n          break;\n        case 'blacklist':\n          this.handleBlacklistUser(row);\n          break;\n      }\n    },\n    /** 余额调整 */\n    handleAdjustBalance(row) {\n      this.currentUser = row;\n      this.balanceForm = {\n        userId: row.id,\n        amount: null,\n        remark: null\n      };\n      this.balanceOpen = true;\n    },\n    /** 成长值调整 */\n    handleAdjustGrowth(row) {\n      this.currentUser = row;\n      this.growthForm = {\n        userId: row.id,\n        growth: null,\n        remark: null\n      };\n      this.growthOpen = true;\n    },\n    /** 拉黑用户 */\n    handleBlacklistUser(row) {\n      const text = row.status === 1 ? \"拉黑\" : \"解除拉黑\";\n      const newStatus = row.status === 1 ? 2 : 1;\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.nickName + '\"用户吗？').then(function() {\n        return changeStatus(row.id, newStatus);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n        this.getList();\n      }).catch(function() {\n        // 操作取消\n      });\n    },\n    /** 提交余额调整 */\n    submitBalanceForm() {\n      this.$refs[\"balanceForm\"].validate(valid => {\n        if (valid) {\n          adjustBalance(this.balanceForm).then(response => {\n            this.$modal.msgSuccess(\"余额调整成功\");\n            this.balanceOpen = false;\n            this.getList();\n            // 发送余额调整成功事件\n            this.$eventBus.$emit('userBalanceAdjusted', this.balanceForm.userId);\n          });\n        }\n      });\n    },\n    /** 取消余额调整 */\n    cancelBalance() {\n      this.balanceOpen = false;\n      this.balanceForm = {};\n      this.currentUser = {};\n    },\n    /** 提交成长值调整 */\n    submitGrowthForm() {\n      this.$refs[\"growthForm\"].validate(valid => {\n        if (valid) {\n          adjustGrowth(this.growthForm).then(response => {\n            this.$modal.msgSuccess(\"成长值调整成功\");\n            this.growthOpen = false;\n            this.getList();\n            // 发送成长值调整成功事件\n            this.$eventBus.$emit('userGrowthAdjusted', this.growthForm.userId);\n          });\n        }\n      });\n    },\n    /** 取消成长值调整 */\n    cancelGrowth() {\n      this.growthOpen = false;\n      this.growthForm = {};\n      this.currentUser = {};\n    }\n  }\n};\n</script>\n\n<style scoped>\n.user-detail {\n  padding: 20px;\n}\n\n.user-avatar {\n  text-align: center;\n  padding: 20px;\n}\n\n.user-avatar img {\n  border: 3px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar h3 {\n  margin-top: 15px;\n  color: #333;\n  font-weight: 500;\n}\n\n.user-info {\n  padding: 20px 0;\n}\n\n.el-descriptions {\n  margin-top: 20px;\n}\n\n.el-descriptions-item__label {\n  font-weight: 600;\n  color: #606266;\n}\n</style>\n"]}]}