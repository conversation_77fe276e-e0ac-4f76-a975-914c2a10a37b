{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\GrowthDetailTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\components\\GrowthDetailTab.vue", "mtime": 1753763463398}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753582859848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GrowthDetailTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "GrowthDetailTab.vue", "sourceRoot": "src/views/massage/user/components", "sourcesContent": ["<template>\n  <div class=\"growth-detail-tab\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"记录类型\" prop=\"type\">\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择记录类型\" clearable>\n          <el-option label=\"服务订单消费\" value=\"1\" />\n          <el-option label=\"管理员调整\" value=\"2\" />\n          <el-option label=\"系统降级扣除\" value=\"3\" />\n          <el-option label=\"储值订单消费\" value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"变动类型\" prop=\"isAdd\">\n        <el-select v-model=\"queryParams.isAdd\" placeholder=\"请选择变动类型\" clearable>\n          <el-option label=\"增加(+)\" value=\"1\" />\n          <el-option label=\"减少(-)\" value=\"0\" />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"时间范围\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button type=\"text\" icon=\"el-icon-d-arrow-left\" size=\"mini\" @click=\"showSearch=!showSearch\">\n          {{showSearch ? '隐藏' : '显示'}}搜索\n        </el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ currentGrowth }}</div>\n            <div class=\"stat-label\">当前成长值</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">+{{ totalEarned }}</div>\n            <div class=\"stat-label\">累计获得</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">-{{ totalUsed }}</div>\n            <div class=\"stat-label\">累计消耗</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ lastChangeDate }}</div>\n            <div class=\"stat-label\">最近变动</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 成长值明细表格 -->\n    <el-table v-loading=\"loading\" :data=\"recordList\">\n      <el-table-column label=\"操作者\" align=\"center\" prop=\"create_user\" width=\"120\" />\n      <el-table-column label=\"操作记录\" align=\"center\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.type_text }}</div>\n          <span :class=\"[{ 'c-link': scope.row.is_add }, { 'c-warning': !scope.row.is_add }]\">\n            {{ `${scope.row.is_add ? '+' : '-'} ${scope.row.growth}` }}\n          </span>\n          ，现成长值<span class=\"ml-sm c-success\">{{ scope.row.after_growth }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"create_time\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.create_time * 1000) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getUserGrowthList } from \"@/api/massage/user\";\n\nexport default {\n  name: \"GrowthDetailTab\",\n  props: {\n    userId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      showSearch: true,\n      total: 0,\n      recordList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userId: this.userId,\n        type: null,\n        isAdd: null\n      },\n      currentGrowth: 0,\n      totalEarned: 0,\n      totalUsed: 0,\n      lastChangeDate: '-'\n    };\n  },\n  created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      const params = {\n        ...this.queryParams,\n        id: this.userId\n      };\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n\n      getUserGrowthList(params).then(response => {\n        this.recordList = response.rows || [];\n        this.total = response.total || 0;\n        this.loading = false;\n        this.getStatistics();\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    getStatistics() {\n      if (this.recordList.length > 0) {\n        // 计算当前成长值（最新记录的after_growth）\n        const latestRecord = this.recordList.reduce((latest, current) => {\n          return (current.create_time > latest.create_time) ? current : latest;\n        });\n        this.currentGrowth = latestRecord.after_growth || 0;\n\n        // 计算累计获得（正数变动的总和）\n        this.totalEarned = this.recordList\n          .filter(item => item.growth > 0)\n          .reduce((sum, item) => sum + item.growth, 0);\n\n        // 计算累计消耗（负数变动的总和）\n        this.totalUsed = Math.abs(this.recordList\n          .filter(item => item.growth < 0)\n          .reduce((sum, item) => sum + item.growth, 0));\n\n        // 获取最近变动时间\n        this.lastChangeDate = this.parseTime(latestRecord.create_time * 1000, '{y}-{m}-{d}');\n      } else {\n        this.currentGrowth = 0;\n        this.totalEarned = 0;\n        this.totalUsed = 0;\n        this.lastChangeDate = '-';\n      }\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 提供给父组件调用的刷新方法\n    refresh() {\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.growth-detail-tab {\n  padding: 20px 0;\n}\n\n.stat-card {\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}