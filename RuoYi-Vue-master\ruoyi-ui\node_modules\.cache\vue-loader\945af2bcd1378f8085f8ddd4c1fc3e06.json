{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue?vue&type=style&index=0&id=64325432&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\src\\views\\massage\\user\\detail.vue", "mtime": 1753760057459}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753582855261}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753582864848}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753582856704}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753582856948}, {"path": "C:\\Users\\<USER>\\Desktop\\peiwan2\\RuoYi-Vue-master\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753582858957}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoudXNlci1pbmZvLWNhcmQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi51c2VyLWF2YXRhci1zZWN0aW9uIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogMjBweDsKfQoKLnVzZXItYXZhdGFyIHsKICB3aWR0aDogMTIwcHg7CiAgaGVpZ2h0OiAxMjBweDsKICBib3JkZXItcmFkaXVzOiA1MCU7CiAgYm9yZGVyOiAzcHggc29saWQgI2YwZjBmMDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgoudXNlci1hdmF0YXItc2VjdGlvbiBoMyB7CiAgbWFyZ2luOiAxNXB4IDAgNXB4IDA7CiAgY29sb3I6ICMzMzM7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLnVzZXItaWQgewogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnVzZXItc3RhdHMgewogIHBhZGRpbmc6IDIwcHggMDsKfQoKLnN0YXQtaXRlbSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDIwcHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLnN0YXQtdmFsdWUgewogIGZvbnQtc2l6ZTogMjRweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzQwOWVmZjsKICBtYXJnaW4tYm90dG9tOiA1cHg7Cn0KCi5zdGF0LWxhYmVsIHsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5lbC1kZXNjcmlwdGlvbnMgewogIG1hcmdpbi10b3A6IDIwcHg7Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqMA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/massage/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 用户基本信息 -->\n    <el-card class=\"user-info-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>用户详情</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"user-avatar-section\">\n            <img :src=\"userInfo.avatarUrl || '/static/default-avatar.png'\" alt=\"用户头像\" class=\"user-avatar\">\n            <h3>{{ userInfo.nickName || '未设置昵称' }}</h3>\n            <p class=\"user-id\">用户ID: {{ userInfo.id }}</p>\n          </div>\n        </el-col>\n        <el-col :span=\"18\">\n          <div class=\"user-stats\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.balance || 0 }}</div>\n                  <div class=\"stat-label\">用户余额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.cash || 0 }}</div>\n                  <div class=\"stat-label\">现金余额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">¥{{ userInfo.totalConsumption || 0 }}</div>\n                  <div class=\"stat-label\">消费总金额</div>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"stat-item\">\n                  <div class=\"stat-value\">{{ userInfo.growth || 0 }}</div>\n                  <div class=\"stat-label\">成长值</div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n          \n          <el-descriptions :column=\"3\" border style=\"margin-top: 20px;\">\n            <el-descriptions-item label=\"手机号\">{{ userInfo.phone || '未绑定' }}</el-descriptions-item>\n            <el-descriptions-item label=\"性别\">\n              <span v-if=\"userInfo.gender === 1\">男</span>\n              <span v-else-if=\"userInfo.gender === 2\">女</span>\n              <span v-else>未知</span>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"城市\">{{ userInfo.city || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"省份\">{{ userInfo.province || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"国家\">{{ userInfo.country || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"用户状态\">\n              <el-tag :type=\"userInfo.status === 1 ? 'success' : 'danger'\">\n                {{ userInfo.status === 1 ? '正常' : '禁用' }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"注册时间\">{{ parseTime(userInfo.createTime * 1000) }}</el-descriptions-item>\n            <el-descriptions-item label=\"应用ID\">{{ userInfo.uniacid || '未设置' }}</el-descriptions-item>\n            <el-descriptions-item label=\"来源类型\">{{ userInfo.sourceType || '未知' }}</el-descriptions-item>\n          </el-descriptions>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 标签页内容 -->\n    <el-card style=\"margin-top: 20px;\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"用户信息\" name=\"userInfo\">\n          <user-info-tab ref=\"userInfoTab\" :user-info=\"userInfo\" @refresh=\"getUserInfo\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"消费记录\" name=\"consumption\">\n          <consumption-record-tab :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"余额明细\" name=\"recharge\">\n          <recharge-record-tab ref=\"rechargeRecordTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"成长值明细\" name=\"growth\">\n          <growth-detail-tab ref=\"growthDetailTab\" :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"持有优惠券\" name=\"coupons\">\n          <user-coupons-tab :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"持有折扣卡\" name=\"discounts\">\n          <user-discounts-tab :user-id=\"userId\" />\n        </el-tab-pane>\n        <el-tab-pane label=\"屏蔽达人\" name=\"blocked\">\n          <blocked-users-tab :user-id=\"userId\" />\n        </el-tab-pane>\n      </el-tabs>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getUser } from \"@/api/massage/user\";\nimport UserInfoTab from './components/UserInfoTab.vue';\nimport ConsumptionRecordTab from './components/ConsumptionRecordTab.vue';\nimport RechargeRecordTab from './components/RechargeRecordTab.vue';\nimport GrowthDetailTab from './components/GrowthDetailTab.vue';\nimport UserCouponsTab from './components/UserCouponsTab.vue';\nimport UserDiscountsTab from './components/UserDiscountsTab.vue';\nimport BlockedUsersTab from './components/BlockedUsersTab.vue';\n\nexport default {\n  name: \"UserDetail\",\n  components: {\n    UserInfoTab,\n    ConsumptionRecordTab,\n    RechargeRecordTab,\n    GrowthDetailTab,\n    UserCouponsTab,\n    UserDiscountsTab,\n    BlockedUsersTab\n  },\n  data() {\n    return {\n      userId: null,\n      userInfo: {},\n      activeTab: 'userInfo',\n      loading: false\n    };\n  },\n  created() {\n    this.userId = this.$route.params.id;\n    this.getUserInfo();\n  },\n  mounted() {\n    // 监听余额调整事件\n    this.$eventBus.$on('userBalanceAdjusted', (userId) => {\n      if (userId == this.userId) {\n        this.refreshBalanceRecords();\n        // 重新获取用户信息以更新余额显示\n        this.getUserInfo();\n      }\n    });\n\n    // 监听成长值调整事件\n    this.$eventBus.$on('userGrowthAdjusted', (userId) => {\n      if (userId == this.userId) {\n        this.refreshGrowthRecords();\n        // 重新获取用户信息以更新成长值显示\n        this.getUserInfo();\n      }\n    });\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    this.$eventBus.$off('userBalanceAdjusted');\n    this.$eventBus.$off('userGrowthAdjusted');\n  },\n  methods: {\n    /** 获取用户信息 */\n    getUserInfo() {\n      this.loading = true;\n      getUser(this.userId).then(response => {\n        this.userInfo = response.data;\n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    /** 标签页切换 */\n    handleTabClick(tab) {\n      this.activeTab = tab.name;\n    },\n    /** 返回列表 */\n    goBack() {\n      this.$router.go(-1);\n    },\n    /** 刷新余额明细 */\n    refreshBalanceRecords() {\n      if (this.$refs.rechargeRecordTab) {\n        this.$refs.rechargeRecordTab.refresh();\n      }\n    },\n    /** 刷新成长值明细 */\n    refreshGrowthRecords() {\n      if (this.$refs.growthDetailTab) {\n        this.$refs.growthDetailTab.refresh();\n      }\n    },\n    /** 刷新所有明细记录 */\n    refreshAllRecords() {\n      this.refreshBalanceRecords();\n      this.refreshGrowthRecords();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.user-info-card {\n  margin-bottom: 20px;\n}\n\n.user-avatar-section {\n  text-align: center;\n  padding: 20px;\n}\n\n.user-avatar {\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 3px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar-section h3 {\n  margin: 15px 0 5px 0;\n  color: #333;\n  font-weight: 500;\n}\n\n.user-id {\n  color: #666;\n  font-size: 14px;\n}\n\n.user-stats {\n  padding: 20px 0;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n.el-descriptions {\n  margin-top: 20px;\n}\n</style>\n"]}]}